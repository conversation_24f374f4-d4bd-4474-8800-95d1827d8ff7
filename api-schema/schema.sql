-- BEGIN main_db_tables

CREATE TABLE logs (
    sequence_id serial primary key
  , row_created timestamp with time zone default current_timestamp
  , data jsonb
  , audit_data jsonb

  , scores jsonb GENERATED ALWAYS AS (data->'scores') STORED

  , id text GENERATED ALWAYS AS (data->>'id') STORED
  , span_id text GENERATED ALWAYS AS (data->>'span_id') STORED
  , root_span_id text GENERATED ALWAYS AS (data->>'root_span_id') STORED
  , _xact_id bigint NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_xact_id')::bigint, 0)) STORED
  , _object_delete boolean NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_object_delete')::boolean, false)) STORED
  , created text GENERATED ALWAYS AS (data->>'created') STORED

  , org_id text GENERATED ALWAYS AS (data->>'org_id') STORED
  , project_id text GENERATED ALWAYS AS (data->>'project_id') STORED
  , experiment_id text GENERATED ALWAYS AS (data->>'experiment_id') STORED
  , dataset_id text GENERATED ALWAYS AS (data->>'dataset_id') STORED
  , prompt_session_id text GENERATED ALWAYS AS (data->>'prompt_session_id') STORED
  , log_id text GENERATED ALWAYS AS (data->>'log_id') STORED
);

-- Alternative to the logs table that we use for inserting new records for
-- high-volume object types. The main motivation for adding this is that the
-- sequence_id counter for `logs` is nearing the limit. Since the set of queries
-- we serve out of postgres is extremely limited now, it should not be too
-- complicated to adjust any remaining queries to consider both tables.
--
-- We believe this is less operationally complicated than trying to migrate the
-- existing `logs` table to a new sequence ID column, which is a multi-step
-- migration that would be challenging to accomplish on other folks' data
-- planes.
CREATE TABLE logs2 (
    sequence_id bigserial primary key
  , row_created timestamp with time zone default current_timestamp
  , data jsonb
  , audit_data jsonb

  , scores jsonb GENERATED ALWAYS AS (data->'scores') STORED

  , id text GENERATED ALWAYS AS (data->>'id') STORED
  , span_id text GENERATED ALWAYS AS (data->>'span_id') STORED
  , root_span_id text GENERATED ALWAYS AS (data->>'root_span_id') STORED
  , _xact_id bigint NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_xact_id')::bigint, 0)) STORED
  , _object_delete boolean NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_object_delete')::boolean, false)) STORED
  , created text GENERATED ALWAYS AS (data->>'created') STORED

  , org_id text GENERATED ALWAYS AS (data->>'org_id') STORED
  , project_id text GENERATED ALWAYS AS (data->>'project_id') STORED
  , experiment_id text GENERATED ALWAYS AS (data->>'experiment_id') STORED
  , dataset_id text GENERATED ALWAYS AS (data->>'dataset_id') STORED
  , prompt_session_id text GENERATED ALWAYS AS (data->>'prompt_session_id') STORED
  , log_id text GENERATED ALWAYS AS (data->>'log_id') STORED
) PARTITION BY RANGE(sequence_id);

-- Create a template table for partman to apply unique indices against.
CREATE TABLE logs2_template (LIKE logs2);

CREATE TABLE comments (
  -- System generated fields for housekeeping
    sequence_id serial primary key
  , row_created timestamp with time zone default current_timestamp
  , data jsonb

  , id text GENERATED ALWAYS AS (data->>'id') STORED
  , _xact_id bigint NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_xact_id')::bigint, 0)) STORED
  , _object_delete boolean NOT NULL GENERATED ALWAYS AS (COALESCE((data->>'_object_delete')::boolean, false)) STORED
  , created text GENERATED ALWAYS AS (data->>'created') STORED

  , origin_id text GENERATED ALWAYS AS (data->'origin'->>'id') STORED

  -- These mirror the object_id fields above
  , org_id text GENERATED ALWAYS AS (data->>'org_id') STORED
  , project_id text GENERATED ALWAYS AS (data->>'project_id') STORED
  , experiment_id text GENERATED ALWAYS AS (data->>'experiment_id') STORED
  , dataset_id text GENERATED ALWAYS AS (data->>'dataset_id') STORED
  , prompt_session_id text GENERATED ALWAYS AS (data->>'prompt_session_id') STORED
  , log_id text GENERATED ALWAYS AS (data->>'log_id') STORED
);

CREATE TABLE views (
    id uuid not null primary key default gen_random_uuid()
  , row_created timestamp with time zone default current_timestamp
  , view_data jsonb
);

-- This table addresses the lambda functions that are available to be used
-- to run code.
CREATE TABLE lambda_functions(
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    vpc_id text NOT NULL,
    runtime text NOT NULL,
    runtime_version text NOT NULL, -- This is the runtime version, not the function version
    arn text NOT NULL,
    created_at timestamp NOT NULL DEFAULT now(),
    used_at timestamp NOT NULL DEFAULT now()
);
CREATE INDEX lambda_functions_runtime_version ON lambda_functions (runtime, runtime_version, vpc_id);
CREATE UNIQUE INDEX lambda_functions_arn ON lambda_functions (arn);

-- This table addresses versions of lambda_functions that can be used to run
-- inline functions.
CREATE TABLE inline_code_runners(
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    lambda_function_arn text NOT NULL REFERENCES lambda_functions(arn) ON DELETE CASCADE,
    runtime text NOT NULL,
    runtime_version text NOT NULL,
    version text NOT NULL,
    code_hash text NOT NULL,
    org_id text NOT NULL,
    created_at timestamp NOT NULL DEFAULT now()
);
CREATE UNIQUE INDEX inline_code_runners_org_id ON inline_code_runners (org_id, lambda_function_arn);
CREATE UNIQUE INDEX inline_code_runners_org_id_runtime_version ON inline_code_runners (org_id, runtime, runtime_version);

-- This table addresses the code bundles that are available to be used
-- to run more complex code that gets bundled ahead of time.
CREATE TABLE code_bundles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id text NOT NULL,
    user_id text NOT NULL,
    created_at timestamp NOT NULL DEFAULT now(),
    path text NOT NULL,
    code_hash text NOT NULL,
    lambda_function_arn text REFERENCES lambda_functions(arn) ON DELETE CASCADE,
    version text
);
CREATE UNIQUE INDEX code_bundles_path ON code_bundles (path);

CREATE TYPE function_secret_object_type AS ENUM ('organization', 'project', 'function');

-- Each secret can be scoped to an organization, project, or function. We store the
-- encrypted output in each secret_value, which can only be decrypted by a secret value
-- that is provided via a manager or as an environment variable.
CREATE TABLE function_secrets (
    id uuid not null primary key default gen_random_uuid(),
    object_type function_secret_object_type NOT NULL,
    object_id TEXT NOT NULL,
    secret_name TEXT NOT NULL,
    secret_value TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT now(),
    used_at TIMESTAMP NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX function_secrets_object_type_object_id_idx ON function_secrets (object_type, object_id, secret_name);

CREATE TYPE project_column_object_type AS ENUM ('project', 'experiment', 'dataset', 'project_log');

CREATE TABLE project_columns (
    id uuid not null primary key default gen_random_uuid(),
    object_id TEXT NOT NULL,
    object_type project_column_object_type NOT NULL,
    subtype project_column_object_type,
    column_name TEXT NOT NULL,
    column_expr TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp
);

CREATE UNIQUE INDEX project_columns_object_type_object_id_idx ON project_columns (object_type, object_id, subtype, column_name);

-- This composite type lets us index by the tuple `(_xact_id, root_span_id)`. I
-- noticed that if we index over the fields separately (`CREATE INDEX ON logs
-- (object_id, _xact_id, root_span_id)`) the search query wouldn't use the index
-- when we write `_xact_id < X OR (xact_id = X AND root_span_id < Y)`, but if we
-- index over the composite type (`CREATE INDEX on logs (object_id,
-- _xact_id_root_span_id)`), it does use the index when we write `(_xact_id,
-- root_span_id) < (X, Y)`.
CREATE TYPE _xact_id_root_span_id AS (
	_xact_id bigint,
	root_span_id text
);

-- For all objects except "global logs", will resolve to the ID itself. For
-- global logs, will resolve to 'global_log:[project_id]'. If all IDs are null,
-- will resolve to NULL.
--
-- Note: any changes to this function will require manually dropping and
-- recreating any indices that depend on it (postgres will not do this
-- automatically).
--
-- Note: postgres is not always great about picking indices with UDF expressions
-- in them. It seems to be working for this function in the way we use it, but
-- in case it stops working at some point, we could try inlining the expression.
CREATE FUNCTION make_object_id(
    project_id text, experiment_id text, dataset_id text, prompt_session_id text, log_id text)
RETURNS text
LANGUAGE SQL
AS $func$
SELECT COALESCE(
    'experiment:' || experiment_id,
    'dataset:' || dataset_id,
    'prompt_session:' || prompt_session_id,
    CASE
        WHEN log_id = 'g' THEN 'global_log:' || project_id
        WHEN log_id = 'p' THEN 'prompt:' || project_id
        ELSE 'log:' || log_id
    END)
$func$;

-- For scanning data from a particular object.
--
-- Unique because we should not have multiple versions of the same row in the
-- same data collection logged in the same transaction.
CREATE UNIQUE INDEX ON logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    id, _xact_id DESC);

CREATE UNIQUE INDEX ON logs2_template (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    id, _xact_id DESC);

-- We create roughly the same indices on the comments table. Specifically, we create one
-- on the (object_id, id) to retrieve specific comments, and one on (object_id, origin_id)
-- to retrieve all comments for a given object (used by the main audit log query).
CREATE UNIQUE INDEX ON comments (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    id, _xact_id DESC);

CREATE INDEX ON comments (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    origin_id, _xact_id DESC);

-- Scanning across projects and org-global-logs.
-- NOTE: We do not index on log_id='p' yet, which would make org_prompt queries fast.
CREATE INDEX ON logs (project_id, id, _xact_id DESC);
CREATE INDEX ON logs (org_id, id, _xact_id DESC) WHERE log_id='g';

CREATE INDEX ON logs2 (project_id, id, _xact_id DESC);
CREATE INDEX ON logs2 (org_id, id, _xact_id DESC) WHERE log_id='g';

-- This index is used to perform searches on user-defined attributes, e.g. metadata.user_id='X'
CREATE INDEX ON logs USING GIN (data);
-- Skip the GIN index on logs2 because it slows down inserts.
-- CREATE INDEX ON logs2 USING GIN (data);

-- This index is used to find the most recent rows. We paginate by the tuple
-- (_xact_id, root_span_id).
CREATE INDEX on logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC)
    WHERE NOT _object_delete;
CREATE INDEX on logs (
    org_id,
    (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC)
    WHERE log_id='g' AND NOT _object_delete;
-- This index is used in the "expansion" query, once we search, to find all the
-- ids that match the target set of root_span_ids
CREATE INDEX on logs (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (COALESCE(root_span_id, id)));
CREATE INDEX on logs (org_id, (COALESCE(root_span_id, id))) WHERE log_id='g';

CREATE INDEX on logs2 (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC)
    WHERE NOT _object_delete;
CREATE INDEX on logs2 (
    org_id,
    (ROW(_xact_id, COALESCE(root_span_id, id))::_xact_id_root_span_id) DESC)
    WHERE log_id='g' AND NOT _object_delete;
CREATE INDEX on logs2 (
    (make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id)),
    (COALESCE(root_span_id, id)));
CREATE INDEX on logs2 (org_id, (COALESCE(root_span_id, id))) WHERE log_id='g';

-- END main_db_tables

-- BEGIN org_prompt_log_rows

-- We are at the point where creating new indices on the 'logs' table is too
-- expensive, so instead, we use this table as an "external" index over the set
-- of prompts for each org.
create table org_prompt_log_rows(
  org_id text not null,
  logs_sequence_id serial not null,
  primary key (org_id, logs_sequence_id)
);

-- Set up a trigger to insert new prompt rows from logs into this table.

create function insert_org_prompt_log_row_trigger_f()
returns trigger
language plpgsql
as $$
begin
  insert into org_prompt_log_rows(org_id, logs_sequence_id)
  values (new.org_id, new.sequence_id)
  on conflict do nothing;
  return new;
end;
$$;

create trigger insert_org_prompt_log_row_trigger
after insert on logs
for each row
when (new.log_id = 'p')
execute function insert_org_prompt_log_row_trigger_f();

-- END org_prompt_log_rows

-- BEGIN brainstore_global_store

create table brainstore_global_store_segment_id_to_liveness(
    segment_id uuid primary key not null,
    object_id text not null,
    is_live boolean not null,
    last_written_ts timestamp with time zone not null default '1980-01-01'::timestamptz,
    vacuum_index_last_successful_start_ts timestamp with time zone not null default '1970-01-01'::timestamptz
);

create index on brainstore_global_store_segment_id_to_liveness(object_id, is_live);
create index on brainstore_global_store_segment_id_to_liveness(is_live, (vacuum_index_last_successful_start_ts - last_written_ts));
-- For paginating through (object_id, segment_id) pairs to apply retention policies.
create index on brainstore_global_store_segment_id_to_liveness(object_id, segment_id) where is_live;

create table brainstore_global_store_object_id_to_metadata(
    object_id text primary key not null,
    last_processed_xact_id bigint,
    wal_token uuid not null default gen_random_uuid()
);

create index on brainstore_global_store_object_id_to_metadata(last_processed_xact_id DESC, object_id DESC)
    where last_processed_xact_id is not null;

create table brainstore_global_store_segment_id_to_metadata(
    segment_id uuid primary key not null,
    last_compacted_index_meta_xact_id bigint,
    last_compacted_index_meta_tantivy_meta text,
    minimum_pagination_key numeric(20, 0) not null,
    num_rows bigint not null
);

-- NOTE: this table is deprecated and is now in read-only mode. All writes
-- should now go to brainstore_global_store_row_id_to_segment_id and
-- brainstore_global_store_root_span_id_to_segment_id.
create table brainstore_global_store_segment_id_to_row_info(
    segment_id uuid not null,
    row_id text not null,
    root_span_id text not null,
    primary key (segment_id, row_id)
);

-- For quickly querying by row_id or root_span_id.
create index on brainstore_global_store_segment_id_to_row_info(row_id);
create index on brainstore_global_store_segment_id_to_row_info(root_span_id);

create table brainstore_global_store_row_id_to_segment_id(
    row_id text not null,
    segment_id uuid not null,
    primary key (row_id, segment_id)
);

create table brainstore_global_store_root_span_id_to_segment_id(
    root_span_id text not null,
    segment_id uuid not null,
    primary key (root_span_id, segment_id)
);

-- For quickly querying by segment_id.
create index on brainstore_global_store_row_id_to_segment_id(segment_id);
create index on brainstore_global_store_root_span_id_to_segment_id(segment_id);

create table brainstore_global_store_segment_id_to_wal_entries(
    segment_id uuid not null,
    xact_id bigint not null,
    wal_filename uuid not null,
    byte_range_start bigint not null,
    byte_range_end bigint not null,
    is_compacted boolean not null,
    digest bigint,
    deleted_at timestamp with time zone,
    primary key (segment_id, xact_id, wal_filename)
);

-- For searches including the is_compacted filter.
create index on brainstore_global_store_segment_id_to_wal_entries(segment_id, xact_id, wal_filename) where (NOT is_compacted);
create index on brainstore_global_store_segment_id_to_wal_entries(segment_id, xact_id, wal_filename) where (is_compacted);

-- For quickly identifying which wal_filenames exist in a segment.
create index on brainstore_global_store_segment_id_to_wal_entries(segment_id, wal_filename);

create table brainstore_global_store_segment_id_to_last_index_operation(
    segment_id uuid primary key not null,
    start timestamp with time zone not null,
    last_updated timestamp with time zone not null,
    operation jsonb not null,
    current_op_token uuid
);

create table brainstore_global_store_segment_id_to_column_statistics(
    segment_id uuid not null,
    field_name text not null,
    min_u64 numeric(20, 0),
    max_u64 numeric(20, 0),
    primary key (segment_id, field_name)
);

create index on brainstore_global_store_segment_id_to_column_statistics(segment_id, field_name, min_u64);
create index on brainstore_global_store_segment_id_to_column_statistics(segment_id, field_name, max_u64);

create table brainstore_global_store_time_based_retention_state(
    id text primary key default 'singleton' check (id = 'singleton'),
    last_successful_start_ts timestamp with time zone,
    current_op_start_ts timestamp with time zone,
    object_id_cursor text,
    segment_id_cursor uuid,
    operation jsonb
);

create table brainstore_global_store_segment_id_to_task_info(
    segment_id uuid primary key not null,
    time_based_retention_info jsonb,
    vacuum_index_info jsonb
);

-- END brainstore_global_store

-- BEGIN brainstore_backfill

-- A single-row table which tracks global backfilling state. Relevant states are
-- duplicated for the logs2 table.
create table brainstore_backfill_global_state(
    -- The sequence id up to which we have synced between the logs table and
    -- brainstore_backfill_tracked_objects.
    frontier_sequence_id bigint not null,
    frontier_sequence_id_2 bigint not null,
    -- The last sequence ID considered for the historical full backfill. null
    -- means we have not started any backfill.
    historical_full_backfill_sequence_id bigint not null,
    historical_full_backfill_sequence_id_2 bigint not null,
    -- The last sequence ID considered for the comments backfill. null means we
    -- have not started any backfill.
    comments_backfill_sequence_id bigint not null,
    -- The timestamp when we first got to the end of the comments in the
    -- backfill. Note that this is for processing only, not compaction.
    completed_initial_comments_backfill_ts timestamp with time zone
);

create table brainstore_backfill_tracked_objects(
    project_id text not null,
    object_type text not null,
    primary key (project_id, object_type),

    -- The sequence_id that we have backfilled up to for this object.
    last_processed_sequence_id bigint not null,
    last_processed_sequence_id_2 bigint not null,
    -- The last sequence_id we have encountered in the log for this object. An
    -- object is considered fully-backfilled if last_processed_sequence_id >=
    -- last_encountered_sequence_id.
    last_encountered_sequence_id bigint not null,
    last_encountered_sequence_id_2 bigint not null,

    -- Bookkeeping information.
    org_id text,
    initial_sequence_id bigint not null, -- Used to compute progress estimates.
    initial_sequence_id_2 bigint not null,
    -- This should only ever be flipped from false to true, but never back. To
    -- un-mark an object as backfilled, it is safest to delete the row and
    -- re-add from scratch. But if you must, you should also set this to true
    -- along with clearing `completed_initial_backfill_ts`.
    is_backfilling boolean not null default false,
    row_created timestamp with time zone default now() not null,
    last_backfilled_ts timestamp with time zone,
    last_backfilled_ts_2 timestamp with time zone,
    -- Records when the backfill process first caught up to the logs for this
    -- object. Precisely, it records the time when last_processed_sequence_id is
    -- updated to be >= last_encountered_sequence_id. Once it is set, it should
    -- never be updated.
    completed_initial_backfill_ts timestamp with time zone
);

-- This should help with the following queries:
--    - Fast-forwarding the set of already-backfilled objects in the ETL loop.
--    - Grabbing the set of un-backfilled objects in the ETL loop.
create index on brainstore_backfill_tracked_objects(
    (last_processed_sequence_id < last_encountered_sequence_id),
    (completed_initial_backfill_ts is null),
    last_processed_sequence_id,
    last_encountered_sequence_id) where is_backfilling;

create index on brainstore_backfill_tracked_objects(
    (last_processed_sequence_id_2 < last_encountered_sequence_id_2),
    (completed_initial_backfill_ts is null),
    last_processed_sequence_id_2,
    last_encountered_sequence_id_2) where is_backfilling;

-- For enumerating all objects that have not yet completed the initial backfill.
create index on brainstore_backfill_tracked_objects((1)) where (
    is_backfilling
    and (completed_initial_backfill_ts is null)
);

-- For grabbing a set of candidate objects for flipping the
-- 'completed_initial_backfill_ts' flag.
create index on brainstore_backfill_tracked_objects((1)) where (
    is_backfilling
    and (completed_initial_backfill_ts is null)
    and (last_processed_sequence_id >= last_encountered_sequence_id)
    and (last_processed_sequence_id_2 >= last_encountered_sequence_id_2)
);

-- For iterating over all objects that have completed initial backfill and need
-- backfilling, in order of primary key.
create index on brainstore_backfill_tracked_objects(project_id, object_type) where (
    is_backfilling
    and (completed_initial_backfill_ts is not null)
    and (
        last_processed_sequence_id < last_encountered_sequence_id
        or last_processed_sequence_id_2 < last_encountered_sequence_id_2
    )
);

-- For iterating over all objects that have NOT completed initial backfill and need
-- backfilling, in order of primary key.
create index on brainstore_backfill_tracked_objects(project_id, object_type) where (
    is_backfilling
    and (completed_initial_backfill_ts is null)
    and (
        last_processed_sequence_id < last_encountered_sequence_id
        or last_processed_sequence_id_2 < last_encountered_sequence_id_2
    )
);

-- This table keeps track of the set of brainstore objects that we encounter
-- while backfilling each tracking entry. We use it to query brainstore metadata
-- relevant to each tracking entry.
create table brainstore_backfill_tracked_objects_to_brainstore_objects(
    project_id text not null,
    object_type text not null,
    brainstore_object_id text not null,
    primary key (project_id, object_type, brainstore_object_id)
);

-- END brainstore_backfill

-- BEGIN brainstore_global_locks_manager

create table brainstore_global_locks_manager_locks(
    name text primary key not null,
    lock_created timestamp with time zone default now() not null
);

create table brainstore_global_locks_manager_last_lock_state(
    name text primary key not null,
    last_lock_state text
);

-- END brainstore_global_locks_manager

-- BEGIN migration utils

-- migration_version table, used to track the last run migration.
CREATE TABLE migration_version(last_migration_id bigint);

-- Functionality for running migrations in the background with pg_cron.
--
-- NOTE: we comment out creating the pg_cron extension because you cannot create
-- it inside a different DB in the cluster, so 'migra'-generated migrations will
-- fail to on this line. We have hacked the 'generate_migration' script to skip
-- dropping the extension.
--CREATE EXTENSION IF NOT EXISTS pg_cron;

CREATE TABLE run_migration_results(
    id serial primary key,
    job_name text unique,
    job_id bigint,
    message text);

CREATE FUNCTION run_migration_inner(
    expected_current_migration_id int,
    next_migration_id int,
    stmts text[])
returns text
language plpgsql
as $$
declare
    _stmt text;
    _current_migration_id int;
begin
    lock table migration_version;

    -- Check if the last_migration_id still matches the existing one.
    select last_migration_id
    into strict _current_migration_id
    from migration_version;

    if _current_migration_id <> expected_current_migration_id then
        return format('Skipping migrations: Expected migration id %s does not match actual %s.',
                      expected_current_migration_id, _current_migration_id);
    end if;

    -- Run all the migrations.
    foreach _stmt in array stmts loop
        execute _stmt;
    end loop;

    -- Update the migration version if it is not null.
    if next_migration_id is not null then
        update migration_version set last_migration_id = next_migration_id;
    end if;

    return 'Success';
end;
$$;

CREATE FUNCTION run_migration(
    job_name text,
    expected_current_migration_id int,
    next_migration_id int,
    stmts text[],
    result_id integer)
returns void
language plpgsql
as $$
declare
    _result_message text;
begin
    -- Run the migration and capture the result.
    begin
        select run_migration_inner(expected_current_migration_id, next_migration_id, stmts)
        into strict _result_message;
    exception when others then
        _result_message := format('Exception: %s %s', SQLERRM, SQLSTATE);
    end;

    -- Store the result for recordkeeping.
    update run_migration_results
    set message = _result_message
    where id = result_id;

    -- Unschedule the cron job.
    perform cron.unschedule(job_name);
end;
$$;

-- END migration utils

CREATE FUNCTION setup_logs2_partman(p_schedule_cron_job boolean)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Make sure the extension is installed and the schema exists.
    if not exists (select 1 from pg_extension where extname = 'pg_partman') then
        raise exception 'pg_partman extension not installed';
    end if;
    if not exists (select 1 from pg_namespace where nspname = 'partman') then
        raise exception 'partman schema not created';
    end if;
    if not exists (select 1 from partman.part_config where parent_table = 'public.logs2') then
      perform partman.create_parent(
          p_parent_table => 'public.logs2',
          p_control => 'sequence_id',
          p_interval => '1000000000',
          p_template_table => 'public.logs2_template'
      );
    end if;
    if p_schedule_cron_job then
        if not exists (select 1 from pg_namespace where nspname = 'cron') then
            raise exception 'cron schema not created';
        end if;
        if not exists (select 1 from cron.job where jobname = 'partman_maintenance_f0fdffe1') then
            perform cron.schedule(
                'partman_maintenance_f0fdffe1',
                '0 * * * *',
                'call partman.run_maintenance_proc();'
            );
        end if;
    end if;
END;
$$;

create schema if not exists partman;
create extension if not exists pg_partman schema partman;
-- This is quite sneaky, but we set this to false in the `api-schema/schema.sql`
-- file to let migra avoid running the cron job since it doesn't have access to
-- the `cron` schema. But when actually running the migration, we set it to
-- true.
select setup_logs2_partman(p_schedule_cron_job => false);

create table automation_cron_jobs(
    automation_id text primary key not null,
    last_executed timestamp with time zone,
    next_execution timestamp with time zone,
    service_token_encrypted text,
    state jsonb not null
);
-- This allows us to efficiently query jobs that are due to run.
CREATE INDEX automation_cron_jobs_next_execution ON automation_cron_jobs (next_execution);

create table service_tokens(
    name text primary key not null,
    service_token_encrypted text not null,
    created_at timestamp with time zone not null default now()
);

create table environments (
  id uuid primary key default gen_random_uuid(),
  org_id text not null,
  name text not null,

  -- a url-friendly, unique identifier for the environment within an organization.
  slug text not null,

  description text,
  created timestamp with time zone default current_timestamp,
  deleted_at timestamp with time zone
);

create unique index environments_org_id_slug_deleted_at_idx
on environments (org_id, slug, deleted_at) nulls not distinct;

create index idx_environments_org_id on environments (org_id);

 -- Associate objects with environments
create table environment_objects (
  id uuid primary key default gen_random_uuid(),
  object_type text not null,
  object_id text not null,
  object_version bigint not null,
  environment_id uuid not null references environments(id),
  created timestamp with time zone default current_timestamp,
  constraint uq_env_objects_env_type_id unique (environment_id, object_type, object_id)
);

create index idx_env_objects_environment_id on environment_objects (environment_id);
create index idx_env_objects_object_type_id on environment_objects (object_type, object_id);
