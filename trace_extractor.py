#!/usr/bin/env python3
"""
Braintrust Trace Extractor

A utility to extract complete trace hierarchies from Braintrust and clean them
for LLM analysis by removing Braintrust-specific metadata while preserving
the essential LLM interaction data.
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
import requests
from datetime import datetime


@dataclass
class TraceExtractionConfig:
    """Configuration for trace extraction and cleaning."""
    
    # What to preserve
    preserve_llm_interactions: bool = True
    preserve_tool_calls: bool = True
    preserve_tool_inputs_outputs: bool = True
    preserve_user_messages: bool = True
    preserve_assistant_messages: bool = True
    preserve_system_messages: bool = False  # Often contains Braintrust templates
    preserve_timestamps: bool = True
    preserve_span_hierarchy: bool = True
    preserve_span_names: bool = True
    preserve_errors: bool = True
    
    # What to strip out
    strip_braintrust_metadata: bool = True
    strip_internal_prompts: bool = True
    strip_app_templates: bool = True
    strip_braintrust_tools: bool = True
    strip_eval_metadata: bool = True
    strip_scores: bool = True
    strip_custom_columns: bool = True
    
    # Span type filtering
    include_span_types: Optional[Set[str]] = None  # If None, include all
    exclude_span_types: Optional[Set[str]] = None  # If None, exclude none
    
    def __post_init__(self):
        if self.include_span_types is None:
            self.include_span_types = {"llm", "tool", "task", "workflow"}
        if self.exclude_span_types is None:
            self.exclude_span_types = {"score", "eval"}


class BraintrustTraceExtractor:
    """Extract and clean Braintrust traces for LLM analysis."""
    
    def __init__(self, api_key: str, api_url: str = "https://api.braintrust.dev"):
        self.api_key = api_key
        self.api_url = api_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })
    
    def extract_trace_by_id(self, project_id: str, trace_id: str, 
                           config: Optional[TraceExtractionConfig] = None) -> Dict[str, Any]:
        """
        Extract a complete trace by its ID.
        
        Args:
            project_id: Braintrust project ID
            trace_id: The root span ID of the trace
            config: Configuration for extraction and cleaning
            
        Returns:
            Complete cleaned trace hierarchy
        """
        if config is None:
            config = TraceExtractionConfig()
            
        # Use BTQL to get the complete trace with all spans
        query = f"""
        select: *
        | from: project_logs('{project_id}') traces
        | filter: root_span_id = '{trace_id}'
        """
        
        response = self._execute_btql_query(query)
        
        if not response or len(response) == 0:
            raise ValueError(f"No trace found with ID {trace_id}")
        
        # Build the complete trace hierarchy
        trace_data = self._build_trace_hierarchy(response)
        
        # Clean the trace according to config
        cleaned_trace = self._clean_trace(trace_data, config)
        
        return cleaned_trace
    
    def extract_traces_by_filter(self, project_id: str, filter_expr: str,
                                limit: int = 10, config: Optional[TraceExtractionConfig] = None) -> List[Dict[str, Any]]:
        """
        Extract multiple traces based on a filter expression.
        
        Args:
            project_id: Braintrust project ID
            filter_expr: BTQL filter expression (e.g., "created > '2024-01-01'")
            limit: Maximum number of traces to extract
            config: Configuration for extraction and cleaning
            
        Returns:
            List of complete cleaned trace hierarchies
        """
        if config is None:
            config = TraceExtractionConfig()
            
        query = f"""
        select: *
        | from: project_logs('{project_id}') traces
        | filter: {filter_expr}
        | limit: {limit}
        """
        
        response = self._execute_btql_query(query)
        
        if not response:
            return []
        
        # Group spans by root_span_id to build individual traces
        traces_by_root = {}
        for span in response:
            root_id = span.get('root_span_id')
            if root_id not in traces_by_root:
                traces_by_root[root_id] = []
            traces_by_root[root_id].append(span)
        
        # Build and clean each trace
        cleaned_traces = []
        for root_id, spans in traces_by_root.items():
            trace_data = self._build_trace_hierarchy(spans)
            cleaned_trace = self._clean_trace(trace_data, config)
            cleaned_traces.append(cleaned_trace)
        
        return cleaned_traces
    
    def _execute_btql_query(self, query: str) -> List[Dict[str, Any]]:
        """Execute a BTQL query and return the results."""
        payload = {
            "query": query,
            "use_brainstore": True,
            "brainstore_realtime": True,
            "brainstore_default_traces": True,  # This is key for getting complete traces
            "fmt": "json"
        }
        
        response = self.session.post(f"{self.api_url}/btql", json=payload)
        response.raise_for_status()
        
        return response.json()
    
    def _build_trace_hierarchy(self, spans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build a hierarchical trace structure from flat span data."""
        if not spans:
            return {}
        
        # Create span lookup
        spans_by_id = {span['span_id']: span for span in spans}
        
        # Find root span
        root_span = None
        for span in spans:
            if span.get('is_root') or not span.get('span_parents'):
                root_span = span
                break
        
        if not root_span:
            # Fallback: use first span as root
            root_span = spans[0]
        
        # Build hierarchy
        def build_children(span):
            span_id = span['span_id']
            children = []
            
            for other_span in spans:
                parents = other_span.get('span_parents', [])
                if span_id in parents:
                    child = build_children(other_span)
                    children.append(child)
            
            span_copy = span.copy()
            span_copy['children'] = children
            return span_copy
        
        return build_children(root_span)
    
    def _clean_trace(self, trace: Dict[str, Any], config: TraceExtractionConfig) -> Dict[str, Any]:
        """Clean a trace according to the provided configuration."""
        if not trace:
            return trace
        
        cleaned = self._clean_span(trace, config)
        return cleaned
    
    def _clean_span(self, span: Dict[str, Any], config: TraceExtractionConfig) -> Dict[str, Any]:
        """Clean an individual span according to configuration."""
        if not span:
            return span
        
        # Check if we should include this span type
        span_type = span.get('span_attributes', {}).get('type')
        if config.include_span_types and span_type not in config.include_span_types:
            return None
        if config.exclude_span_types and span_type in config.exclude_span_types:
            return None
        
        cleaned_span = {}
        
        # Always preserve basic span structure
        if config.preserve_span_hierarchy:
            cleaned_span['span_id'] = span.get('span_id')
            cleaned_span['root_span_id'] = span.get('root_span_id')
            cleaned_span['span_parents'] = span.get('span_parents', [])
        
        if config.preserve_span_names:
            span_attrs = span.get('span_attributes', {})
            cleaned_span['name'] = span_attrs.get('name', '')
            cleaned_span['type'] = span_attrs.get('type', '')
        
        if config.preserve_timestamps:
            metrics = span.get('metrics', {})
            cleaned_span['start_time'] = metrics.get('start')
            cleaned_span['end_time'] = metrics.get('end')
            cleaned_span['duration'] = metrics.get('duration')
        
        # Handle input/output based on span type and config
        if span_type == 'llm' and config.preserve_llm_interactions:
            cleaned_span.update(self._clean_llm_span(span, config))
        elif span_type == 'tool' and config.preserve_tool_calls:
            cleaned_span.update(self._clean_tool_span(span, config))
        else:
            # For other span types, preserve basic input/output if configured
            if config.preserve_tool_inputs_outputs:
                cleaned_span['input'] = span.get('input')
                cleaned_span['output'] = span.get('output')
        
        if config.preserve_errors and span.get('error'):
            cleaned_span['error'] = span.get('error')
        
        # Recursively clean children
        children = span.get('children', [])
        cleaned_children = []
        for child in children:
            cleaned_child = self._clean_span(child, config)
            if cleaned_child is not None:
                cleaned_children.append(cleaned_child)
        
        if cleaned_children:
            cleaned_span['children'] = cleaned_children
        
        return cleaned_span
    
    def _clean_llm_span(self, span: Dict[str, Any], config: TraceExtractionConfig) -> Dict[str, Any]:
        """Clean LLM-specific span data."""
        result = {}
        
        # Parse input (usually contains messages)
        input_data = span.get('input')
        if input_data:
            if isinstance(input_data, str):
                try:
                    input_data = json.loads(input_data)
                except json.JSONDecodeError:
                    pass
            
            if isinstance(input_data, dict):
                messages = input_data.get('messages', [])
                cleaned_messages = self._clean_messages(messages, config)
                if cleaned_messages:
                    result['messages'] = cleaned_messages
        
        # Parse output (LLM response)
        output_data = span.get('output')
        if output_data and config.preserve_assistant_messages:
            result['response'] = output_data
        
        # Parse metadata for model info (but strip Braintrust-specific stuff)
        metadata = span.get('metadata')
        if metadata:
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except json.JSONDecodeError:
                    pass
            
            if isinstance(metadata, dict) and not config.strip_braintrust_metadata:
                # Keep only essential model metadata
                essential_keys = ['model', 'temperature', 'max_tokens', 'top_p']
                essential_metadata = {k: v for k, v in metadata.items() if k in essential_keys}
                if essential_metadata:
                    result['model_config'] = essential_metadata
        
        return result
    
    def _clean_tool_span(self, span: Dict[str, Any], config: TraceExtractionConfig) -> Dict[str, Any]:
        """Clean tool call span data."""
        result = {}
        
        if config.preserve_tool_inputs_outputs:
            tool_input = span.get('input')
            tool_output = span.get('output')
            
            # Check if this is a Braintrust internal tool
            span_name = span.get('span_attributes', {}).get('name', '')
            if config.strip_braintrust_tools and self._is_braintrust_internal_tool(span_name):
                return {}
            
            result['tool_name'] = span_name
            result['tool_input'] = tool_input
            result['tool_output'] = tool_output
        
        return result
    
    def _clean_messages(self, messages: List[Dict[str, Any]], config: TraceExtractionConfig) -> List[Dict[str, Any]]:
        """Clean message list, filtering based on configuration."""
        if not messages:
            return []
        
        cleaned_messages = []
        for msg in messages:
            role = msg.get('role', '')
            content = msg.get('content', '')
            
            # Filter based on role and config
            should_include = False
            if role == 'user' and config.preserve_user_messages:
                should_include = True
            elif role == 'assistant' and config.preserve_assistant_messages:
                should_include = True
            elif role == 'system' and config.preserve_system_messages:
                should_include = True
            
            # Skip if it looks like a Braintrust template
            if config.strip_app_templates and self._looks_like_braintrust_template(content):
                should_include = False
            
            if should_include:
                cleaned_messages.append({
                    'role': role,
                    'content': content
                })
        
        return cleaned_messages
    
    def _is_braintrust_internal_tool(self, tool_name: str) -> bool:
        """Check if a tool name indicates a Braintrust internal tool."""
        braintrust_tools = {
            'braintrust_eval', 'braintrust_score', 'braintrust_log',
            'bt_eval', 'bt_score', 'bt_log'
        }
        return tool_name.lower() in braintrust_tools
    
    def _looks_like_braintrust_template(self, content: str) -> bool:
        """Heuristic to detect Braintrust template content."""
        if not isinstance(content, str):
            return False
        
        # Look for common Braintrust template patterns
        braintrust_patterns = [
            'braintrust',
            '{{',  # Template variables
            'bt_',
            'eval_',
            'scorer_',
            'You are an AI assistant that helps evaluate'
        ]
        
        content_lower = content.lower()
        return any(pattern in content_lower for pattern in braintrust_patterns)


    def export_for_claude_analysis(self, traces: List[Dict[str, Any]],
                                  format_type: str = "conversation") -> str:
        """
        Export traces in a format optimized for Claude analysis.

        Args:
            traces: List of cleaned traces
            format_type: 'conversation', 'structured', or 'minimal'

        Returns:
            Formatted string suitable for Claude analysis
        """
        if format_type == "conversation":
            return self._format_as_conversation(traces)
        elif format_type == "structured":
            return self._format_as_structured(traces)
        elif format_type == "minimal":
            return self._format_as_minimal(traces)
        else:
            raise ValueError(f"Unknown format_type: {format_type}")

    def _format_as_conversation(self, traces: List[Dict[str, Any]]) -> str:
        """Format traces as natural conversation flows."""
        output = []

        for i, trace in enumerate(traces):
            output.append(f"=== TRACE {i+1} ===")
            output.append(f"Trace ID: {trace.get('root_span_id', 'unknown')}")
            output.append("")

            self._format_span_as_conversation(trace, output, level=0)
            output.append("")

        return "\n".join(output)

    def _format_span_as_conversation(self, span: Dict[str, Any], output: List[str], level: int):
        """Recursively format a span and its children as conversation."""
        indent = "  " * level
        span_type = span.get('type', 'unknown')
        span_name = span.get('name', 'unnamed')

        if span_type == 'llm':
            output.append(f"{indent}🤖 LLM Call: {span_name}")

            # Show messages
            messages = span.get('messages', [])
            for msg in messages:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                if role == 'user':
                    output.append(f"{indent}  👤 User: {content}")
                elif role == 'assistant':
                    output.append(f"{indent}  🤖 Assistant: {content}")
                elif role == 'system':
                    output.append(f"{indent}  ⚙️  System: {content}")

            # Show response
            response = span.get('response')
            if response:
                output.append(f"{indent}  ✅ Response: {response}")

        elif span_type == 'tool':
            tool_name = span.get('tool_name', span_name)
            output.append(f"{indent}🔧 Tool Call: {tool_name}")

            tool_input = span.get('tool_input')
            if tool_input:
                output.append(f"{indent}  📥 Input: {tool_input}")

            tool_output = span.get('tool_output')
            if tool_output:
                output.append(f"{indent}  📤 Output: {tool_output}")

        else:
            output.append(f"{indent}📋 {span_type.title()}: {span_name}")
            if span.get('input'):
                output.append(f"{indent}  Input: {span.get('input')}")
            if span.get('output'):
                output.append(f"{indent}  Output: {span.get('output')}")

        # Show errors
        if span.get('error'):
            output.append(f"{indent}  ❌ Error: {span.get('error')}")

        # Process children
        for child in span.get('children', []):
            self._format_span_as_conversation(child, output, level + 1)

    def _format_as_structured(self, traces: List[Dict[str, Any]]) -> str:
        """Format traces as structured JSON for analysis."""
        return json.dumps({
            'format': 'structured_traces',
            'traces': traces,
            'metadata': {
                'total_traces': len(traces),
                'extraction_purpose': 'llm_analysis'
            }
        }, indent=2)

    def _format_as_minimal(self, traces: List[Dict[str, Any]]) -> str:
        """Format traces with minimal information for focused analysis."""
        output = []

        for trace in traces:
            minimal_trace = self._extract_minimal_trace(trace)
            if minimal_trace:
                output.append(minimal_trace)

        return json.dumps({
            'format': 'minimal_traces',
            'traces': output
        }, indent=2)

    def _extract_minimal_trace(self, span: Dict[str, Any]) -> Dict[str, Any]:
        """Extract only the most essential information from a trace."""
        result = {
            'type': span.get('type'),
            'name': span.get('name')
        }

        if span.get('type') == 'llm':
            messages = span.get('messages', [])
            # Only keep user and assistant messages
            essential_messages = [
                msg for msg in messages
                if msg.get('role') in ['user', 'assistant']
            ]
            if essential_messages:
                result['conversation'] = essential_messages

            if span.get('response'):
                result['response'] = span.get('response')

        elif span.get('type') == 'tool':
            result['tool_name'] = span.get('tool_name')
            result['input'] = span.get('tool_input')
            result['output'] = span.get('tool_output')

        # Process children
        children = []
        for child in span.get('children', []):
            child_minimal = self._extract_minimal_trace(child)
            if child_minimal and any(child_minimal.values()):
                children.append(child_minimal)

        if children:
            result['children'] = children

        return result


def main():
    """CLI interface for the trace extractor."""
    import argparse

    parser = argparse.ArgumentParser(description='Extract and clean Braintrust traces for LLM analysis')
    parser.add_argument('project_id', help='Braintrust project ID')
    parser.add_argument('--trace-id', help='Specific trace ID to extract')
    parser.add_argument('--filter', help='BTQL filter expression for multiple traces')
    parser.add_argument('--limit', type=int, default=10, help='Maximum number of traces to extract')
    parser.add_argument('--output', '-o', help='Output file path')
    parser.add_argument('--format', choices=['json', 'conversation', 'structured', 'minimal'],
                       default='json', help='Output format')
    parser.add_argument('--preserve-system', action='store_true',
                       help='Preserve system messages (may contain templates)')
    parser.add_argument('--include-braintrust-tools', action='store_true',
                       help='Include Braintrust internal tools')
    parser.add_argument('--api-url', default='https://api.braintrust.dev',
                       help='Braintrust API URL')

    args = parser.parse_args()

    api_key = os.getenv('BRAINTRUST_API_KEY')
    if not api_key:
        print("Error: BRAINTRUST_API_KEY environment variable not set")
        sys.exit(1)

    # Configure extraction
    config = TraceExtractionConfig()
    config.preserve_system_messages = args.preserve_system
    config.strip_braintrust_tools = not args.include_braintrust_tools

    extractor = BraintrustTraceExtractor(args.api_url, api_key)

    # Extract traces
    if args.trace_id:
        traces = [extractor.extract_trace_by_id(args.project_id, args.trace_id, config=config)]
    elif args.filter:
        traces = extractor.extract_traces_by_filter(args.project_id, args.filter,
                                                   limit=args.limit, config=config)
    else:
        print("Error: Either --trace-id or --filter must be specified")
        sys.exit(1)

    # Format output
    if args.format == 'json':
        result = {
            'extracted_at': datetime.now().isoformat(),
            'project_id': args.project_id,
            'traces': traces,
            'config': {
                'preserve_llm_interactions': config.preserve_llm_interactions,
                'preserve_tool_calls': config.preserve_tool_calls,
                'strip_braintrust_metadata': config.strip_braintrust_metadata,
                'strip_internal_prompts': config.strip_internal_prompts
            }
        }
        output_text = json.dumps(result, indent=2)
    else:
        output_text = extractor.export_for_claude_analysis(traces, args.format)

    # Write output
    if args.output:
        with open(args.output, 'w') as f:
            f.write(output_text)
        print(f"Extracted {len(traces)} trace(s) to {args.output}")
    else:
        print(output_text)


if __name__ == '__main__':
    main()
