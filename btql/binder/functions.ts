import {
  jsonSchemaSchema,
  jsonSchemaObjectSchema,
  scalarTypeToLogicalSchema,
  toSchemaObject,
} from "#/schema";
import { z } from "zod";
import { coerceAllTypes, weakestScalarType } from "./types";

export const functionSchema = z.strictObject({
  isAgg: z.boolean(),
  args: z.strictObject({
    named: z.record(jsonSchemaSchema.array()),
    variadic: z.array(jsonSchemaSchema).optional(),
  }),
  type: z.function(
    z.tuple([z.record(jsonSchemaSchema), z.array(jsonSchemaSchema)]),
    jsonSchemaObjectSchema,
  ),
});

export type Function = z.infer<typeof functionSchema>;

export const FUNCTION_NAMES = [
  "second",
  "minute",
  "hour",
  "day",
  "week",
  "month",
  "year",
  "current_timestamp",
  "current_date",
  "sha256",
  "insert",
  "coalesce",
  "concat",
  "nullif",
  "least",
  "lower",
  "upper",
  "greatest",
  "count",
  "sum",
  "avg",
  "min",
  "max",
  "percentile",
  "len",
  "json_extract",
] as const;
export const functionNameSchema = z.enum(FUNCTION_NAMES);

export const DATETIME_TRUNC_FUNCTION: Function = {
  isAgg: false,
  args: {
    named: {
      expr: [{ type: "string", format: "date-time" }],
    },
  },
  type: () => ({
    type: "string",
    format: "date-time",
  }),
} as const;

export const COALESCE_LIKE_FUNCTION: Function = {
  isAgg: false,
  args: {
    named: {},
    variadic: [{}],
  },
  type: (named, variadic) => {
    return scalarTypeToLogicalSchema(
      coerceAllTypes(variadic.map(toSchemaObject).map(weakestScalarType), true),
    );
  },
} as const;

export const FUNCTIONS: { [K in (typeof FUNCTION_NAMES)[number]]: Function } = {
  second: DATETIME_TRUNC_FUNCTION,
  minute: DATETIME_TRUNC_FUNCTION,
  hour: DATETIME_TRUNC_FUNCTION,
  day: DATETIME_TRUNC_FUNCTION,
  week: DATETIME_TRUNC_FUNCTION,
  month: DATETIME_TRUNC_FUNCTION,
  year: DATETIME_TRUNC_FUNCTION,
  current_timestamp: {
    isAgg: false,
    args: {
      named: {},
    },
    type: () => ({
      type: "string",
      format: "date-time",
    }),
  },
  current_date: {
    isAgg: false,
    args: {
      named: {},
    },
    type: () => ({
      type: "string",
      format: "date",
    }),
  },
  sha256: {
    isAgg: false,
    args: {
      named: { expr: [{}] },
    },
    type: () => ({
      type: "string",
    }),
  },
  insert: {
    isAgg: false,
    args: {
      named: {
        expr: [{}],
      },
      variadic: [{ type: "string" }, {}],
    },
    type: ({ expr }) => toSchemaObject(expr),
  },
  coalesce: COALESCE_LIKE_FUNCTION,
  least: COALESCE_LIKE_FUNCTION,
  greatest: COALESCE_LIKE_FUNCTION,
  concat: {
    isAgg: false,
    args: {
      named: {},
      variadic: [{ type: "string" }, {}],
    },
    type: () => ({ type: "string" }),
  },
  nullif: {
    isAgg: false,
    args: {
      named: {
        left: [{}],
        right: [{}],
      },
    },
    type: ({ left, right }) =>
      scalarTypeToLogicalSchema(
        coerceAllTypes(
          [left, right].map(toSchemaObject).map(weakestScalarType),
          true,
        ),
      ),
  },
  count: {
    isAgg: true,
    args: {
      named: { expr: [{ default: 1 }] },
    },
    type: () => ({
      type: "integer",
    }),
  },
  sum: {
    isAgg: true,
    args: {
      named: {
        expr: [{ type: "number" }, { type: "integer" }, { type: "boolean" }],
      },
    },
    type: ({ expr }) => {
      if (
        ["integer", "boolean"].includes(weakestScalarType(toSchemaObject(expr)))
      ) {
        return { type: ["integer", "null"] };
      } else {
        return { type: ["number", "null"] };
      }
    },
  },
  avg: {
    isAgg: true,
    args: {
      named: { expr: [{ type: "number" }, { type: "integer" }] },
    },
    type: ({ expr }) => ({ type: ["number", "null"] }),
  },
  min: {
    isAgg: true,
    args: {
      named: { expr: [{}] },
    },
    type: ({ expr }) => toSchemaObject(expr),
  },
  max: {
    isAgg: true,
    args: {
      named: { expr: [{}] },
    },
    type: ({ expr }) => toSchemaObject(expr),
  },
  percentile: {
    isAgg: true,
    args: {
      named: {
        expr: [{ type: "number" }, { type: "integer" }],
        p: [{ type: "number" }],
      },
    },
    type: ({ expr }) => ({ type: ["number", "null"] }),
  },
  len: {
    isAgg: false,
    args: {
      named: {
        expr: [{ type: "array" }],
      },
    },
    type: () => ({ type: ["integer", "null"] }),
  },
  lower: {
    isAgg: false,
    args: {
      named: { expr: [{ type: "string" }] },
    },
    type: () => ({ type: "string" }),
  },
  upper: {
    isAgg: false,
    args: {
      named: { expr: [{ type: "string" }] },
    },
    type: () => ({ type: "string" }),
  },
  json_extract: {
    isAgg: false,
    args: {
      named: { expr: [{ type: "string" }] },
      variadic: [{ type: "string" }],
    },
    type: ({ expr }) => ({}),
  },
} as const;

export const FUNCTION_ALIAS: {
  [alias: string]: (typeof FUNCTION_NAMES)[number];
} = {
  now: "current_timestamp",
};
