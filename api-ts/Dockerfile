# The build context of this Dockerfile is assumed to be the root of the
# repository.

FROM node:22-bookworm-slim AS base

ARG GIT_COMMIT_HASH
ENV GIT_COMMIT_HASH=${GIT_COMMIT_HASH}
ENV PATH="/root/.cargo/bin:$PATH"

# Install dependencies only when needed
FROM base AS builder
RUN corepack enable
RUN corepack prepare pnpm@8.15.9 --activate

# Install dependencies. git is not used directly, but is referenced in the build
# script, so we need to install it.
RUN apt-get update && apt-get upgrade -y && apt-get install -y zip python3 git curl build-essential

# Install rust and wasm-pack.
RUN curl https://sh.rustup.rs -sSf | sh -s -- -y --no-modify-path;
RUN curl -L --proto '=https' --tlsv1.2 -sSf https://raw.githubusercontent.com/cargo-bins/cargo-binstall/main/install-from-binstall-release.sh | bash;
RUN cargo binstall wasm-pack -y

WORKDIR /braintrust

# Copy js dependency directories.
COPY api-ts api-ts
COPY autoevals autoevals
COPY brainstore brainstore
COPY btql btql
COPY local/js local/js
COPY sdk/core/js sdk/core/js
COPY sdk/js sdk/js
COPY proxy/packages/proxy proxy/packages/proxy
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml turbo.json .npmrc .eslintrc.cjs VERSION ./

# Run update scripts.
COPY scripts/docker_update_root_package_json.py .
RUN python3 docker_update_root_package_json.py

# Build api-ts
RUN pnpm install
RUN pnpm build --filter '@braintrust/api-ts^...'
WORKDIR /braintrust/api-ts
RUN pnpm build-docker
WORKDIR /braintrust

# -----
# Production proxy-only image: copy necessary files.
FROM base AS runner_proxy

# install runtime and debugging tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    python3 \
    curl vim dstat procps net-tools ripgrep zip

WORKDIR /braintrust

# Copy the built api-ts.
COPY --from=builder /braintrust/node_modules node_modules
COPY --from=builder /braintrust/api-ts/node_modules api-ts/node_modules
COPY --from=builder /braintrust/api-ts/dist/local-proxy api-ts/local-proxy

COPY api-ts/scripts/docker_entrypoint_proxy.py .

EXPOSE 8787
ENTRYPOINT ["python3", "docker_entrypoint_proxy.py"]

HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8787/ || exit 1

# -----
# Production full image: copy necessary files.
FROM base AS runner

# install runtime and debugging tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    python3 python3-venv python3-dev gcc \
    curl vim dstat procps net-tools ripgrep zip

WORKDIR /braintrust

# Copy migrations directory.
COPY api-schema api-schema

# Copy local Python dependencies.
COPY autoevals autoevals
COPY sdk sdk

COPY api-ts/requirements.txt api-ts/requirements.txt

# Copy the built api-ts.
COPY --from=builder /braintrust/node_modules node_modules
COPY --from=builder /braintrust/api-ts/node_modules api-ts/node_modules
COPY --from=builder /braintrust/brainstore/btql-wasm brainstore/btql-wasm
COPY --from=builder /braintrust/api-ts/dist/local api-ts/local

# Create venv and root environment.
RUN python3 -m venv venv
RUN . venv/bin/activate && export > /root/env

# Install python deps.
RUN . /root/env && pip install --disable-pip-version-check -r api-schema/lambda_function_requirements.txt
WORKDIR /braintrust/api-ts
RUN . /root/env && pip install --disable-pip-version-check -r requirements.txt

WORKDIR /braintrust
COPY scripts/docker_entrypoint_loader.sh api-ts/scripts/docker_entrypoint.py ./

EXPOSE 8000
ENTRYPOINT ["./docker_entrypoint_loader.sh", "python", "docker_entrypoint.py"]

HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1
