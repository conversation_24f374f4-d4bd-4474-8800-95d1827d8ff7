import { Request, Response } from "express";
import { z } from "zod";
import { getPG } from "./db/pg";
import {
  createEnvironmentSchema,
  environmentSchema,
  patchEnvironmentSchema,
} from "@braintrust/core/typespecs";
import {
  AccessDeniedError,
  BadRequestError,
  wrapZodError,
  handleUniqueConstraintViolation,
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  getWasCachedToken,
} from "./util";
import { getRequestContext } from "./request_context";
import { parseNoStrip } from "@braintrust/core";
import { sql, join, ToSQL } from "@braintrust/btql/planner";
import { OBJECT_CACHE } from "./object_cache";
import { resolveOrganizationId } from "./organization_utils";

// PostgreSQL constraint names
const ENVIRONMENTS_UNIQUE_CONSTRAINT =
  "environments_org_id_slug_deleted_at_idx";

const environmentReadSchema = z.object({
  name: z.string().nullish(),
  ids: z
    .union([z.string().uuid(), z.string().uuid().array().nonempty()])
    .nullish(),
  org_name: z.string().nullish(),
});

const environmentCreateSchema = createEnvironmentSchema;

async function checkOrganizationPermissions({
  appOrigin,
  token,
  orgId,
  permission,
  wasCachedToken,
}: {
  appOrigin: string;
  token: string | undefined;
  orgId: string;
  permission: "read" | "update";
  wasCachedToken: string | undefined;
}): Promise<void> {
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken: token,
    aclObjectType: "organization",
    overrideRestrictObjectType: "organization",
    objectId: orgId,
    wasCachedToken,
  });

  if (!permissions.permissions.includes(permission)) {
    throw new AccessDeniedError({
      permission,
      aclObjectType: "organization",
      overrideRestrictObjectType: "organization",
      objectId: orgId,
    });
  }
}

export async function environmentReadHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    parseNoStrip(environmentReadSchema, ctx.data),
  );

  const orgId = await resolveOrganizationId({
    providedOrgName: params.org_name,
    ctx,
  });

  // Check if user has read permissions on the organization
  await checkOrganizationPermissions({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    orgId,
    permission: "read",
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  const pg = getPG();

  const queryTemplate = sql`
    SELECT id, org_id, name, slug, description,
           created::text as created,
           deleted_at::text as deleted_at
    FROM environments
    WHERE org_id = ${orgId} AND deleted_at IS NULL
    ${params.name ? sql`AND name = ${params.name}` : sql``}
    ${
      params.ids
        ? Array.isArray(params.ids) && params.ids.length > 1
          ? sql`AND id = ANY(${params.ids})`
          : sql`AND id = ${Array.isArray(params.ids) ? params.ids[0] : params.ids}`
        : sql``
    }
    ORDER BY created DESC
  `;

  const { query, params: queryParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, queryParams);
  const environments = environmentSchema.array().parse(result.rows);

  res.json({ objects: environments });
}

export async function environmentReadIdHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const id = req.params.id;

  if (!id) {
    throw new BadRequestError("Must specify environment id");
  }

  // For reading by ID, we get the org_id from the environment record in the database
  // instead of requiring it in the request
  const pg = getPG();
  const queryTemplate = sql`
    SELECT id, org_id, name, slug, description,
           created::text as created,
           deleted_at::text as deleted_at
    FROM environments
    WHERE id = ${id} AND deleted_at IS NULL
  `;

  const { query, params: queryParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, queryParams);

  if (result.rows.length === 0) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "environment",
      objectId: id,
    });
  }

  const environment = environmentSchema.parse(result.rows[0]);

  // Check if user has read permissions on the organization
  await checkOrganizationPermissions({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    orgId: environment.org_id,
    permission: "read",
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  res.json(environment);
}

export async function environmentWriteHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    parseNoStrip(environmentCreateSchema, ctx.data),
  );

  if (!params.name || !params.slug) {
    throw new BadRequestError("Must specify name and slug");
  }

  const orgId = await resolveOrganizationId({
    providedOrgName: params.org_name,
    ctx,
  });

  // Check if user has update permissions on the organization
  await checkOrganizationPermissions({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    orgId,
    permission: "update",
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  const pg = getPG();

  try {
    // Insert new environment
    const insertTemplate = sql`
      INSERT INTO environments (org_id, name, slug, description)
      VALUES (${orgId}, ${params.name}, ${params.slug}, ${params.description || null})
      RETURNING id, org_id, name, slug, description,
                created::text as created,
                deleted_at::text as deleted_at
    `;

    const { query, params: queryParams } = insertTemplate.toNumericParamQuery();
    const insertResult = await pg.query(query, queryParams);

    const environment = environmentSchema.parse(insertResult.rows[0]);
    res.json(environment);
  } catch (error) {
    handleUniqueConstraintViolation(
      error,
      ENVIRONMENTS_UNIQUE_CONSTRAINT,
      "Environment with this slug already exists in organization",
    );
  }
}

export async function environmentIdWriteHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const id = req.params.id;
  const method = req.method;

  if (!id) {
    throw new BadRequestError("Must specify environment id");
  }

  if (method !== "DELETE" && method !== "PATCH") {
    throw new BadRequestError("Invalid method");
  }

  // Get the existing environment to extract org_id and verify it exists
  const pg = getPG();
  const existingTemplate = sql`
    SELECT id, org_id FROM environments WHERE id = ${id} AND deleted_at IS NULL
  `;

  const { query: existingQuery, params: existingParams } =
    existingTemplate.toNumericParamQuery();
  const existingResult = await pg.query(existingQuery, existingParams);

  if (existingResult.rows.length === 0) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "environment",
      objectId: id,
    });
  }

  const orgId = z
    .object({ org_id: z.string() })
    .parse(existingResult.rows[0]).org_id;

  // Check permissions
  await checkOrganizationPermissions({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    orgId,
    permission: "update",
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  // Execute the operation
  if (method === "DELETE") {
    const deleteTemplate = sql`
      UPDATE environments SET deleted_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, org_id, name, slug, description,
                created::text as created,
                deleted_at::text as deleted_at
    `;

    const { query, params: queryParams } = deleteTemplate.toNumericParamQuery();
    const result = await pg.query(query, queryParams);
    res.json(environmentSchema.parse(result.rows[0]));
  } else {
    // Parse parameters based on method
    const patchParams = wrapZodError(() =>
      parseNoStrip(patchEnvironmentSchema, ctx.data),
    );

    const updateClauses: ToSQL[] = [];

    if (patchParams.name !== undefined) {
      updateClauses.push(sql`name = ${patchParams.name}`);
    }

    if (patchParams.slug !== undefined) {
      updateClauses.push(sql`slug = ${patchParams.slug}`);
    }

    if (patchParams.description !== undefined) {
      updateClauses.push(sql`description = ${patchParams.description}`);
    }

    if (updateClauses.length === 0) {
      throw new BadRequestError("Must specify at least one field to update");
    }

    try {
      const updateTemplate = sql`
        UPDATE environments SET ${join(updateClauses, ", ")}
        WHERE id = ${id}
        RETURNING id, org_id, name, slug, description,
                  created::text as created,
                  deleted_at::text as deleted_at
      `;

      const { query, params: queryParams } =
        updateTemplate.toNumericParamQuery();
      const updateResult = await pg.query(query, queryParams);

      res.json(environmentSchema.parse(updateResult.rows[0]));
    } catch (error) {
      handleUniqueConstraintViolation(
        error,
        ENVIRONMENTS_UNIQUE_CONSTRAINT,
        "Environment with this slug already exists in organization",
      );
    }
  }
}
