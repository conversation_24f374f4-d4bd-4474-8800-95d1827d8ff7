import { z } from "zod";
import { extractDebugErrorText, wrapZodError } from "./util";
import { objectNullish } from "@braintrust/core";
import { getRedis } from "./redis";
import {
  ALLOWED_ORIGIN,
  ATTACHMENT_BUCKET_NAME,
  BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
  BRAINSTORE_URL,
  BRAINSTORE_WRITER_URL,
  CODE_BUNDLE_BUCKET_NAME,
  PG_URL,
  REALTIME_URL,
  RESPONSE_BUCKET_NAME,
} from "./env";
import { getPGByURL } from "./db/pg";
import { makeObjectStore } from "./object-storage/object-storage";

export const healthcheckInputSchema = objectNullish(z.object({}));
export type HealthcheckInput = z.infer<typeof healthcheckInputSchema>;

export async function runStatusCheck(rawInput: unknown) {
  const _input: HealthcheckInput = {
    ...wrapZodError(() => healthcheckInputSchema.nullish().parse(rawInput)),
  };
  const [
    redisPing,
    realtimePing,
    controlPlanePing,
    postgresPing,
    responseBucketPing,
    codeBundleBucketPing,
    attachmentBucketPing,
    brainstoreRealtimeWalBucketPing,
    brainstorePing,
    brainstoreWriterPing,
  ] = await Promise.all([
    wrapError(runRedisPing),
    wrapError(() => runUrlPing("realtime", REALTIME_URL)),
    wrapError(() => runUrlPing("control plane", ALLOWED_ORIGIN)),
    wrapError(() => runPostgresPing("postgres", PG_URL)),
    wrapError(() => runS3Ping("btql response bucket", RESPONSE_BUCKET_NAME)),
    wrapError(() => runS3Ping("code bundle bucket", CODE_BUNDLE_BUCKET_NAME)),
    wrapError(() => runS3Ping("attachment bucket", ATTACHMENT_BUCKET_NAME)),
    wrapError(() =>
      runS3Ping(
        "brainstore realtime wal bucket",
        BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
      ),
    ),
    wrapError(() => runUrlPing("brainstore", BRAINSTORE_URL)),
    BRAINSTORE_WRITER_URL &&
      wrapError(() => runUrlPing("brainstore writer", BRAINSTORE_WRITER_URL)),
  ]);
  return {
    redisPing,
    realtimePing,
    controlPlanePing,
    postgresPing,
    responseBucketPing,
    codeBundleBucketPing,
    attachmentBucketPing,
    brainstoreRealtimeWalBucketPing,
    brainstorePing,
    ...(brainstoreWriterPing ? { brainstoreWriterPing } : undefined),
  };
}

async function wrapError<T>(
  fn: () => Promise<T>,
): Promise<{ success: boolean; response: T | string }> {
  try {
    const res = await fn();
    return {
      success: true,
      response: res,
    };
  } catch (e) {
    return {
      success: false,
      response: extractDebugErrorText(e),
    };
  }
}

async function runRedisPing() {
  const conn = await getRedis();
  const response = await conn.ping();
  return response;
}

async function runUrlPing(service: string, url: string | undefined) {
  if (!url) {
    throw new Error(`${service} not configured`);
  }
  const resp = await fetch(url, { method: "GET" });
  if (!resp.ok) {
    throw new Error(
      `Failed to ping ${service}: ${resp.status} ${await resp.text()}`,
    );
  }
  return "success";
}

async function runPostgresPing(service: string, url: string | undefined) {
  if (!url) {
    throw new Error(`${service} not configured`);
  }
  const conn = getPGByURL(url);
  await conn.query("SELECT 1");
  return "success";
}

async function runS3Ping(service: string, bucket: string | undefined) {
  if (!bucket) {
    throw new Error(`${service} not configured`);
  }
  const objectStore = await makeObjectStore();
  await objectStore.headBucket({ bucket });
  return "success";
}
