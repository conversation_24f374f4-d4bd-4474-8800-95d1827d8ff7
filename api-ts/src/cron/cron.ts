import { z } from "zod";
import { getPG } from "../db/pg";
import {
  AccessDeniedError,
  BadRequestError,
  CronLockAcquisitionError,
  InternalServerError,
  NotFoundError,
  wrapZodError,
} from "../util";
import {
  decryptMessage,
  encryptedMessageSchema,
  encryptMessage,
} from "@braintrust/proxy/utils";
import { Request, Response } from "express";
import { getRequestContext } from "../request_context";
import { AUTOMATION_CACHE } from "../automations";
import { triggerBtqlExport } from "./export";
import { ProjectAutomation } from "@braintrust/core/typespecs";
import { getCurrentSpan, otelWrapTraced } from "../instrumentation/api";
import {
  btqlExportJobSchema,
  cronJobSchema,
  CronJobState,
  cronJobStatusResponseSchema,
  RunCronJobResponse,
} from "@braintrust/local/api-schema";
import { OBJECT_CACHE } from "../object_cache";
import { sha256Digest } from "../proxy/proxy";
import { SERVICE_TOKEN_SECRET_KEY } from "../env";
import { backOff } from "exponential-backoff";
import { PoolingBtPgClient } from "@braintrust/local/bt-pg";

export const registerCronJobSchema = z.object({
  automation_id: z.string(),
  cron: cronJobSchema,
});

export const getServiceTokenSecretKey = async () => {
  if (!SERVICE_TOKEN_SECRET_KEY) {
    throw new Error("SERVICE_TOKEN_SECRET_KEY is not set");
  }
  return sha256Digest(SERVICE_TOKEN_SECRET_KEY);
};

export async function registerCronJobRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const body = wrapZodError(() => registerCronJobSchema.parse(ctx.data));

  const { automation_id: automationId, cron } = body;

  // Separately, parse the service token, and throw a more opaque error if it's invalid
  const serviceTokenParsed = z
    .object({ service_token: z.string() })
    .safeParse(ctx.data);
  if (!serviceTokenParsed.success) {
    throw new BadRequestError("Missing service_token field in request body");
  }
  const serviceToken = serviceTokenParsed.data.service_token;

  const automation = await AUTOMATION_CACHE.getAutomationById({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    automationId,
  });
  if (!automation) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "project_automation",
      objectId: automationId,
    });
  }

  const db = getPG();
  const serviceTokenEncrypted = await encryptMessage(
    await getServiceTokenSecretKey(),
    serviceToken,
  );
  await db.query(
    `INSERT INTO automation_cron_jobs (automation_id, service_token_encrypted, state, next_execution)
     VALUES ($1, $2, $3, NOW())
     ON CONFLICT (automation_id) DO UPDATE SET service_token_encrypted = $2, state = $3, next_execution = NOW()`,
    [automationId, JSON.stringify(serviceTokenEncrypted), cron],
  );
  res.json({ success: true });
}

export async function deleteAutomationCronJob(automationId: string) {
  const db = getPG();
  await db.query(`DELETE FROM automation_cron_jobs WHERE automation_id = $1`, [
    automationId,
  ]);
}

export const runCronJobSchema = z.object({
  iterations: z.number().min(1).max(100).optional(),
});

export async function runCronJobRequest(
  req: Request,
  res: Response,
  id: string,
) {
  const ctx = getRequestContext(req);
  const appOrigin = ctx.appOrigin;
  if (!appOrigin) {
    throw new InternalServerError("Missing appOrigin");
  }
  const automation = await AUTOMATION_CACHE.getAutomationById({
    appOrigin,
    authToken: ctx.token,
    automationId: id,
  });
  if (!automation) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "project_automation",
      objectId: id,
    });
  }

  const projectInfo = await OBJECT_CACHE.checkAndGet({
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: automation.project_id,
    appOrigin,
    authToken: ctx.token,
  });
  if (!projectInfo) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "project",
      objectId: automation.project_id,
    });
  }
  const orgId = projectInfo.parent_cols.get("organization")?.id;
  if (!orgId) {
    throw new InternalServerError(
      `Project ${automation.project_id} has no organization ID`,
    );
  }

  const body = wrapZodError(() => runCronJobSchema.parse(ctx.data ?? {}));
  const result = await runCronJob({
    orgId,
    automation,
    appOrigin,
    iterations: body.iterations,
  });
  if (result.result?.last_execution.error) {
    res.status(400);
  }
  res.json(result);
}

export async function resetCronJobRequest(
  req: Request,
  res: Response,
  id: string,
) {
  const ctx = getRequestContext(req);
  const appOrigin = ctx.appOrigin;
  if (!appOrigin) {
    throw new InternalServerError("Missing appOrigin");
  }
  const automation = await AUTOMATION_CACHE.getAutomationById({
    appOrigin,
    authToken: ctx.token,
    automationId: id,
  });
  if (!automation) {
    throw new AccessDeniedError({
      permission: "write",
      objectType: "project_automation",
      objectId: id,
    });
  }
  let baseCronState: CronJobState;
  switch (automation.config.event_type) {
    case "btql_export":
      baseCronState = btqlExportJobSchema.parse({ type: "btql_export" });
      break;
    default:
      throw new BadRequestError(
        `Unsupported automation event type: ${automation.config.event_type}`,
      );
  }
  const db = getPG();
  await db.query(
    `UPDATE automation_cron_jobs SET state = $1 WHERE automation_id = $2`,
    [JSON.stringify(baseCronState), id],
  );
  res.json({ success: true });
}

export async function getCronJobStatusRequest(
  req: Request,
  res: Response,
  id: string,
) {
  const ctx = getRequestContext(req);
  const appOrigin = ctx.appOrigin;
  if (!appOrigin) {
    throw new InternalServerError("Missing appOrigin");
  }
  const automation = await AUTOMATION_CACHE.getAutomationById({
    appOrigin,
    authToken: ctx.token,
    automationId: id,
  });
  if (!automation) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "project_automation",
      objectId: id,
    });
  }
  const db = getPG();
  const { rows } = await db.query(
    `SELECT state, last_executed, next_execution FROM automation_cron_jobs WHERE automation_id = $1`,
    [automation.id],
  );
  if (rows.length === 0) {
    throw new NotFoundError(
      `Automation cron job not found (id=${automation.id})`,
    );
  } else if (rows.length > 1) {
    throw new InternalServerError(
      `Multiple automation cron jobs found (id=${automation.id})`,
    );
  }

  const state = wrapZodError(() => cronJobStatusResponseSchema.parse(rows[0]));
  res.json(state);
}

export async function decryptServiceToken(
  encryptedServiceToken: string,
): Promise<string | undefined> {
  const encryptedServiceTokenParsed = encryptedMessageSchema.parse(
    JSON.parse(encryptedServiceToken),
  );
  return await decryptMessage(
    await getServiceTokenSecretKey(),
    encryptedServiceTokenParsed.iv,
    encryptedServiceTokenParsed.data,
  );
}

export const runCronJob = otelWrapTraced(
  "runCronJob",
  async function runCronJob({
    orgId,
    automation,
    appOrigin,
    iterations,
    tryAcquire,
  }: {
    orgId: string;
    automation: ProjectAutomation;
    appOrigin: string;
    iterations?: number;
    tryAcquire?: boolean;
  }): Promise<RunCronJobResponse> {
    if (automation.config.event_type === "retention") {
      throw new BadRequestError(
        `Cron jobs do not support time-based retention`,
      );
    }
    const currentSpan = getCurrentSpan();
    currentSpan?.setAttributes({
      "automation.id": automation.id,
      "automation.name": automation.name,
      "automation.event_type": automation.config.event_type,
      "automation.interval_seconds": automation.config.interval_seconds,
    });
    return await withCronLock({
      automation,
      fn: async ({ automation, serviceToken, cron }) => {
        switch (cron.type) {
          case "btql_export":
            return await triggerBtqlExport({
              appOrigin,
              orgId,
              automation,
              serviceToken,
              cron,
              iterations,
            });
          default:
            const _: never = cron.type;
            throw new BadRequestError(
              `Unsupported cron job type: ${cron.type}`,
            );
        }
      },
      tryAcquire,
    });
  },
);

const jobSchema = z.object({
  automation_id: z.string(),
  service_token_encrypted: z.string(),
  state: cronJobSchema,
});

async function acquireCronJobWithLock({
  db,
  automation,
}: {
  db: PoolingBtPgClient;
  automation: ProjectAutomation;
}): Promise<unknown[]> {
  try {
    await db.query("BEGIN");
    const { rows } = await db.query(
      `SELECT * FROM automation_cron_jobs WHERE automation_id = $1 FOR UPDATE NOWAIT
          `,
      [automation.id],
    );
    return rows;
  } catch (e) {
    await db.query("ROLLBACK");
    // PostgreSQL error code 55P03 means "lock_not_available"
    if (e && typeof e === "object" && "code" in e && e.code === "55P03") {
      throw new CronLockAcquisitionError(
        `Could not acquire lock for automation cron job (id=${automation.id})`,
      );
    }
    throw e;
  }
}

export async function withCronLock<
  F extends (args: {
    automation: ProjectAutomation;
    serviceToken: string | undefined;
    cron: CronJobState;
  }) => Promise<CronJobState>,
>({
  automation,
  fn,
  tryAcquire,
}: {
  automation: ProjectAutomation;
  fn: F;
  tryAcquire?: boolean;
}): Promise<{
  result: CronJobState | undefined;
  locked: boolean;
}> {
  if (automation.config.event_type === "retention") {
    throw new BadRequestError(`Cron jobs do not support retention automations`);
  }

  const db = await getPG().connect();
  let committed = false;
  try {
    const maxDelay = 10_000; // 10 seconds
    const numOfAttempts = tryAcquire ? 1 : (15 * 60_000) / maxDelay; // 15 minutes
    const rows = await backOff(
      () => acquireCronJobWithLock({ db, automation }),
      {
        jitter: "full",
        maxDelay,
        numOfAttempts,
      },
    );

    if (rows.length === 0) {
      throw new InternalServerError(
        `Automation cron job not found (id=${automation.id})`,
      );
    } else if (rows.length > 1) {
      throw new InternalServerError(
        `Multiple automation cron jobs found (id=${automation.id})`,
      );
    }
    const job = rows[0];

    const parsedJob = jobSchema.safeParse(job);
    if (!parsedJob.success) {
      throw new InternalServerError(
        `Invalid automation cron job (id=${automation.id})`,
      );
    }

    const encryptedServiceToken = encryptedMessageSchema.parse(
      JSON.parse(parsedJob.data.service_token_encrypted),
    );
    const serviceToken = await decryptMessage(
      await getServiceTokenSecretKey(),
      encryptedServiceToken.iv,
      encryptedServiceToken.data,
    );
    const result = await fn({
      automation,
      serviceToken,
      cron: z.object({ state: cronJobSchema }).parse(job).state,
    });

    if (result) {
      await db.query(
        `UPDATE automation_cron_jobs
          SET state = $1
              , next_execution = COALESCE(last_executed, NOW()) + '${automation.config.interval_seconds} second'
              , last_executed = NOW()
          WHERE automation_id = $2`,
        [JSON.stringify(result), automation.id],
      );
    }

    await db.query("COMMIT");
    committed = true;
    return { result, locked: true };
  } finally {
    if (!committed) {
      await db.query("ROLLBACK");
    }
    db.release();
  }
}
