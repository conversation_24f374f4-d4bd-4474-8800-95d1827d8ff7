import { z } from "zod";
import {
  RetentionAutomation,
  RetentionObjectType,
  retentionObjectTypeEnum,
} from "@braintrust/core/typespecs";
import { RequestContext } from "./request_context";
import { AUTOMATION_CACHE } from "./automations";
import {
  wrapZodError,
  objectTypeToAclObjectType,
  BadRequestError,
} from "./util";
import { OBJECT_CACHE } from "./object_cache";
import { objectTypeSchema } from "./schema";
import { mapSetDefault } from "@braintrust/core";
import { InternalServerError } from "./util";

const retentionPoliciesRequestSchema = z.object({
  objects: z.array(
    z.object({
      // Allow any object type in the request if `skip_unknown_objects` is true; we'll
      // silently skip ones that retention doesn't apply to. Otherwise, validate the
      // type against `RetentionObjectType` in the request handler.
      object_type: objectTypeSchema,
      object_id: z.string().uuid(),
    }),
  ),
  // If true, skip missing objects and objects that the auth token is not authorized
  // to access, as well as object types that we don't yet support retention for, instead
  // of throwing an error. This is important so the request doesn't fail when brainstore
  // asks this endpoint to resolve the retention policies for arbitrary objects.
  skip_unknown_objects: z.boolean().nullish(),
});

type RetentionPoliciesRequest = z.infer<typeof retentionPoliciesRequestSchema>;

type RetentionPolicy = {
  object_type: RetentionObjectType;
  object_id: string | null;
  policy: {
    automation_id: string;
    retention_days: number;
  } | null;
};

async function getProjectIdFromObjects({
  appOrigin,
  authToken,
  objectType,
  objectIds,
  includeDeletedObjects,
  ignoreMissingObjects,
}: {
  appOrigin: string;
  authToken: string | undefined;
  objectType: RetentionObjectType;
  objectIds: string[];
  includeDeletedObjects: boolean;
  ignoreMissingObjects: boolean;
}): Promise<
  {
    object_type: RetentionObjectType;
    object_id: string;
    project_id: string;
  }[]
> {
  // Convert the object type to an ACL object type
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType(objectType);

  // Use the object cache to look up the object and get its metadata
  const objectInfos = await OBJECT_CACHE.checkAndGetMulti({
    appOrigin,
    authToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectIds,
    includeDeletedObjects,
    ignoreMissingObjects,
  });

  // Get the project ID based on the object type
  switch (objectType) {
    case "project_logs":
      return Object.entries(objectInfos).map(([objectId, objectInfo]) => ({
        object_type: objectType,
        object_id: objectId,
        project_id: objectInfo.object_id,
      }));
    case "experiment":
    case "dataset":
      return Object.entries(objectInfos).flatMap(([objectId, objectInfo]) => {
        const projectId = objectInfo?.parent_cols.get("project")?.id;
        // NOTE: this should never happen but ignore objects with no parent project
        if (!projectId) {
          return [];
        }
        return [
          {
            object_type: objectType,
            object_id: objectId,
            project_id: projectId,
          },
        ];
      });
    default:
      const _exhaustiveCheck: never = objectType;
      throw new Error(`Unknown object type: ${_exhaustiveCheck}`);
  }
}

async function resolveProjectIds({
  appOrigin,
  authToken,
  objects,
  skipUnknownObjects,
}: {
  appOrigin: string;
  authToken: string | undefined;
  objects: RetentionPoliciesRequest["objects"];
  skipUnknownObjects: boolean;
}) {
  const objectTypeToObjectIds = objects.reduce((acc, object) => {
    const parseResult = retentionObjectTypeEnum.safeParse(object.object_type);
    if (!parseResult.success) {
      // If `skipUnknownObjects` is set, skip object types that we don't support retention for.
      // Otherwise, throw an error.
      if (!skipUnknownObjects) {
        throw new BadRequestError(
          `Invalid retention object type: ${object.object_type}`,
        );
      }
      return acc;
    }
    mapSetDefault(acc, parseResult.data, []).push(object.object_id);
    return acc;
  }, new Map<RetentionObjectType, Array<string>>());

  const resp = await Promise.all(
    Array.from(objectTypeToObjectIds.entries()).map(
      async ([objectType, objectIds]) => {
        return await getProjectIdFromObjects({
          appOrigin,
          authToken,
          objectType,
          objectIds,
          includeDeletedObjects: true,
          ignoreMissingObjects: skipUnknownObjects,
        });
      },
    ),
  );

  return resp.flat();
}

export async function getRetentionPolicies(
  ctx: RequestContext,
): Promise<RetentionPolicy[]> {
  const req = wrapZodError(() =>
    retentionPoliciesRequestSchema.parse(ctx.data),
  );
  const { objects, skip_unknown_objects } = req;

  const objectsWithProjectIds = await resolveProjectIds({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    objects,
    skipUnknownObjects: skip_unknown_objects ?? false,
  });
  const projectIdToAutomations = await AUTOMATION_CACHE.getAutomationMulti({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    projectIds: Array.from(
      new Set(objectsWithProjectIds.map(({ project_id }) => project_id)),
    ),
  });

  const policies: RetentionPolicy[] = objectsWithProjectIds.map(
    ({ object_type, object_id, project_id }) => {
      const retentionAutomations = (
        projectIdToAutomations.get(project_id) || []
      ).reduce((acc: RetentionAutomation[], automation) => {
        const { config, ...rest } = automation;
        if (
          config.event_type === "retention" &&
          config.object_type === object_type
        ) {
          return [
            ...acc,
            {
              ...rest,
              config,
            },
          ];
        } else {
          return acc;
        }
      }, []);

      if (retentionAutomations.length > 1) {
        throw new InternalServerError(
          `Multiple retention policies found for object ${object_type}:${object_id}`,
        );
      }

      const policy =
        retentionAutomations.length == 1 ? retentionAutomations[0] : null;

      return {
        object_type,
        object_id,
        policy: policy
          ? {
              automation_id: policy.id,
              retention_days: policy.config.retention_days,
            }
          : null,
      };
    },
  );

  return policies;
}
