import {
  Bad<PERSON><PERSON><PERSON><PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  post<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NotFoundError,
} from "./util";
import { z } from "zod";
import { customFetchRequest } from "./custom_fetch";
import { getRedis } from "./redis";
import { sha1 } from "./hash";
import { endpointSchemas } from "@braintrust/local/app-schema";
import { getPG } from "./db/pg";
import { sql } from "@braintrust/btql/planner";

const authTokenIdsResponseSchema = endpointSchemas.self_auth_token_ids.output;

function getOrgNotFoundError(
  providedOrgName?: string | null | undefined,
): string {
  return providedOrgName
    ? `Organization '${providedOrgName}' not found or user does not have access`
    : "Must specify org_name or use org-scoped token";
}

export async function resolveOrganizationId({
  providedOrgName,
  ctx,
}: {
  providedOrgName?: string | null | undefined;
  ctx: { token?: string | undefined; appOrigin: string };
}): Promise<string> {
  if (!ctx.token) {
    throw new BadRequestError("Authorization token required");
  }

  const redisClient = await getRedis();
  const cacheKey = `auth_token_ids:${sha1(ctx.token)}${
    providedOrgName ? `:${providedOrgName}` : ""
  }`;
  const cached = await redisClient.get(cacheKey);

  if (cached) {
    const { org_id } = authTokenIdsResponseSchema.parse(JSON.parse(cached));
    if (org_id) {
      return org_id;
    } else {
      throw new NotFoundError(getOrgNotFoundError(providedOrgName));
    }
  }

  const headers = postDefaultHeaders({ token: ctx.token });
  const resp = await customFetchRequest(
    `${ctx.appOrigin}/api/self/auth_token_ids`,
    {
      method: "POST",
      headers,
      body: JSON.stringify({ org_name: providedOrgName }),
    },
  );

  if (!resp.ok) {
    throw new HTTPError(
      resp.statusCode,
      `Failed to get auth token ids: ${await resp.body.text()}`,
    );
  }

  const result = authTokenIdsResponseSchema.parse(await resp.body.json());

  // Cache the result
  await redisClient
    .set(cacheKey, JSON.stringify(result), {
      EX: 60, // Cache for 1 minute
    })
    .catch((e) => {
      console.error("Error caching auth token ids:", e);
    });

  if (!result.org_id) {
    throw new NotFoundError(getOrgNotFoundError(providedOrgName));
  }

  return result.org_id;
}

export async function resolveEnvironmentToXactId({
  objectType,
  objectId,
  environmentSlug,
}: {
  objectType: string;
  objectId: string;
  environmentSlug: string;
}): Promise<string> {
  const pg = getPG();
  const queryTemplate = sql`
    SELECT eo.object_version::text as object_version
    FROM environment_objects eo
    JOIN environments e ON eo.environment_id = e.id
    WHERE eo.object_type = ${objectType}
      AND eo.object_id = ${objectId}
      AND e.slug = ${environmentSlug}
      AND e.deleted_at IS NULL
  `;

  const { query, params: queryParams } = queryTemplate.toNumericParamQuery();
  const result = await pg.query(query, queryParams);

  if (result.rows.length === 0) {
    throw new NotFoundError(
      `No association found for object ${objectId} in environment ${environmentSlug}`,
    );
  }

  return z.object({ object_version: z.string() }).parse(result.rows[0])
    .object_version;
}
