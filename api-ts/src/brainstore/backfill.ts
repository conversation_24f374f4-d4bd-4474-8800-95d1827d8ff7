import { z } from "zod";
import { getPG, LOG_PG_TRANSACTION_QUERY_IDENTIFIER } from "../db/pg";
import { ident, join, snippet, sql, ToSQL } from "@braintrust/btql/planner";
import { extractDebugErrorText, InternalServerError } from "../util";
import {
  BRAINSTORE_BACKFILL_DISABLE_HISTORICAL,
  BRAINSTORE_BACKFILL_ENABLE_NONHISTORICAL,
  BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
  BRAINSTORE_DISABLE_ETL_LOOP,
  BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL,
  PG_COMMENTS_TABLE,
  PG_LOGS_TABLE,
  PG_LOGS2_TABLE,
  BRAINSTORE_BACKFILL_DISABLE_COMMENTS,
  BRAINSTORE_BACKFILL_DISABLE_FRONTIER_LOOP,
} from "../env";
import { getLogger } from "../instrumentation/logger";
import { makeObjectIdExprFromTable } from "../query_util";
import {
  assertBrainstoreEnabled,
  brainstoreEnabled,
  makeBrainstoreObjectId,
  processWal,
} from "./brainstore";
import {
  OBJECT_TYPE_FIELD,
  objectIdsUnionSchema,
} from "@braintrust/local/api-schema";
import {
  backfillableObjectTypeSchema,
  FullTrackedObject,
  GlobalBackfillStatus,
  ObjectBackfillStatus,
  objectBackfillStatusSchema,
  ProjectBackfillStatus,
  TrackedObject,
  trackedObjectSchema,
} from "@braintrust/local/app-schema";
import { ObjectType } from "../schema";
import { mapSetDefault } from "@braintrust/core";
import { AdvisoryLocks, advisoryLockInfo } from "../advisory_locks";
import { v4 as uuidv4 } from "uuid";
import { PoolingBtPg, PoolingBtPgClient } from "@braintrust/local/bt-pg";

export const ETL_PERIOD_S = 600; // 10 minutes

export interface RunEtlLoopOutput {
  locked?: boolean;
  tableResults?: Record<string, EtlLoopBatch[] | null>;
}

export interface EtlLoopBatch {
  maxSequenceId: number;
  elapsedTimeS: number;
}

type SourceDbTableIndex = "one" | "two";

function makeCounterSuffix(sourceDbTableIndex: SourceDbTableIndex) {
  return snippet(sourceDbTableIndex === "one" ? "" : "_2");
}

// This expression is the same as the index expression we use in the DB schema.
function compareSequenceIdsExpr(sourceDbTableIndex: SourceDbTableIndex) {
  const suffix = makeCounterSuffix(sourceDbTableIndex);
  return sql`(last_processed_sequence_id${suffix} < last_encountered_sequence_id${suffix})`;
}

const estimatedProgressExpr = sql`greatest(least(
  (
    case when last_encountered_sequence_id <= initial_sequence_id then 1
    else (
      ((last_processed_sequence_id - initial_sequence_id)::double precision) /
      ((last_encountered_sequence_id - initial_sequence_id)::double precision)
    )
    end
  ),
  (
    case when last_encountered_sequence_id_2 <= initial_sequence_id_2 then 1
    else (
      ((last_processed_sequence_id_2 - initial_sequence_id_2)::double precision) /
      ((last_encountered_sequence_id_2 - initial_sequence_id_2)::double precision)
    )
    end
  ),
  1
), 0)`;

function isAlreadyBackfilledExpr(isAlreadyBackfilled: boolean) {
  return isAlreadyBackfilled
    ? sql`(completed_initial_backfill_ts is not null)`
    : sql`(completed_initial_backfill_ts is null)`;
}

// NOTE: These functions assume you have already done ACL checks.

export async function getGlobalBackfillStatus(): Promise<GlobalBackfillStatus> {
  const getBackfillStatus = async () => {
    const conn = getPG();
    const queryTemplate = sql`
        with
        per_object_stats as (
        select
            last_processed_sequence_id,
            last_encountered_sequence_id,
            last_processed_sequence_id_2,
            last_encountered_sequence_id_2
        from brainstore_backfill_tracked_objects
        where is_backfilling
        )
        select
            coalesce(
                bool_and(
                    last_processed_sequence_id >= last_encountered_sequence_id and
                    last_processed_sequence_id_2 >= last_encountered_sequence_id_2
                ),
                true) as all_backfilled
        from per_object_stats
    `;
    const { query, params } = queryTemplate.toNumericParamQuery();
    const { rows } = await conn.query(query, params);
    if (rows.length > 1) {
      throw new InternalServerError("Expected at most one row");
    }
    return z
      .object({
        all_backfilled: z.boolean(),
      })
      .parse(rows[0]);
  };

  const [
    backfillStatus,
    frontierSequenceId,
    logsMaxSequenceId,
    logs2MaxSequenceId,
    commentsMaxSequenceId,
  ] = await Promise.all([
    getBackfillStatus(),
    getBackfillGlobalState(),
    getMaxSequenceId(PG_LOGS_TABLE),
    getMaxSequenceId(PG_LOGS2_TABLE),
    getCommentsMaxSequenceId(),
  ]);

  const historicalBackfillProgressOne =
    frontierSequenceId.one.frontier_sequence_id === 0
      ? null
      : frontierSequenceId.one.historical_full_backfill_sequence_id /
        frontierSequenceId.one.frontier_sequence_id;
  const historicalBackfillProgressTwo =
    frontierSequenceId.two.frontier_sequence_id === 0
      ? null
      : frontierSequenceId.two.historical_full_backfill_sequence_id /
        frontierSequenceId.two.frontier_sequence_id;
  const historicalBackfillProgress =
    historicalBackfillProgressOne === null &&
    historicalBackfillProgressTwo === null
      ? null
      : Math.min(
          historicalBackfillProgressOne ?? 1,
          historicalBackfillProgressTwo ?? 1,
        );

  return {
    ...backfillStatus,
    logs_max_sequence_id: logsMaxSequenceId.id,
    logs2_max_sequence_id: logs2MaxSequenceId.id,
    backfill_frontier_sequence_id: frontierSequenceId.one.frontier_sequence_id,
    backfill_frontier_sequence_id_2:
      frontierSequenceId.two.frontier_sequence_id,

    historical_backfill_progress: historicalBackfillProgress,

    comments_backfill_progress:
      frontierSequenceId.comments_backfill_sequence_id === 0
        ? null
        : frontierSequenceId.comments_backfill_sequence_id /
          commentsMaxSequenceId.comments_max_sequence_id,

    completed_initial_comments_backfill_ts:
      frontierSequenceId.completed_initial_comments_backfill_ts
        ? frontierSequenceId.completed_initial_comments_backfill_ts.toISOString()
        : null,

    backfill_mode: BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL
      ? "historical_full"
      : "per_project",
  };
}

export async function getTrackedObjectStatus({
  projectIds,
  objectType,
}: {
  projectIds: string[];
  objectType: ObjectType | null;
}): Promise<ObjectBackfillStatus[]> {
  if (projectIds.length === 0) {
    return [];
  }
  const db = getPG();
  const selectQuery = sql`
      select
          project_id,
          object_type,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_processed_sequence_id_2
            else last_processed_sequence_id
            end
          ) as backfill_frontier_sequence_id,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_encountered_sequence_id_2
            else last_encountered_sequence_id
            end
          ) as backfill_target_sequence_id,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_backfilled_ts_2
            else last_backfilled_ts
            end
          ) as last_backfilled_ts,
          completed_initial_backfill_ts,
          ${estimatedProgressExpr} as estimated_progress,
          is_backfilling as enabled
      from brainstore_backfill_tracked_objects
      where
          project_id in (${join(
            projectIds.map((p) => sql`${p}`),
            ",",
          )})
          ${objectType ? sql`and object_type = ${objectType}` : snippet("")}
      order by row_created desc, object_type asc
  `;
  const { query, params } = selectQuery.toNumericParamQuery();
  const { rows } = await db.query(query, params);
  return z.array(objectBackfillStatusSchema).parse(rows);
}

export async function getProjectBackfillStatus({
  projectId,
}: {
  projectId: string;
}): Promise<ProjectBackfillStatus> {
  return {
    global_status: await getGlobalBackfillStatus(),
    object_statuses: await getTrackedObjectStatus({
      projectIds: [projectId],
      objectType: null,
    }),
  };
}

export async function getBackfillingObjects({
  queryTrackedObjects,
  conn,
}: {
  queryTrackedObjects: TrackedObject[];
  conn: PoolingBtPg | PoolingBtPgClient;
}): Promise<TrackedObject[]> {
  conn = conn ?? getPG();
  const { rows } = await conn.query(
    `
      select project_id, object_type
      from brainstore_backfill_tracked_objects
      where
        is_backfilling
        and (project_id, object_type) in (
            select project_id, object_type
            from jsonb_to_recordset($1::jsonb) as x(project_id text, object_type text)
        )
    `,
    [JSON.stringify(queryTrackedObjects)],
  );
  return trackedObjectSchema.array().parse(rows);
}

async function getMaxSequenceId(tbl: string): Promise<{
  id: number;
}> {
  const conn = getPG();
  const { rows } = await conn.query(`select max(sequence_id) id from ${tbl}`);
  if (rows.length === 0) {
    return { id: 0 };
  }
  if (rows.length !== 1) {
    throw new InternalServerError("Expected up to one row. Found multiple.");
  }
  const data = z
    .object({
      id: z.number().nullish(),
    })
    .parse(rows[0]);
  return { id: data.id ?? 0 };
}

async function getCommentsMaxSequenceId(): Promise<{
  comments_max_sequence_id: number;
}> {
  const conn = getPG();
  const { rows } = await conn.query(
    "select max(sequence_id) comments_max_sequence_id from comments",
  );
  if (rows.length !== 1) {
    throw new InternalServerError("Expected exactly one row (empty database?)");
  }
  const data = z
    .object({
      comments_max_sequence_id: z.number().nullish(),
    })
    .parse(rows[0]);
  return { comments_max_sequence_id: data.comments_max_sequence_id ?? 0 };
}

export async function getBackfillGlobalState(
  conn?: PoolingBtPg | PoolingBtPgClient,
): Promise<{
  one: {
    frontier_sequence_id: number;
    historical_full_backfill_sequence_id: number;
  };
  two: {
    frontier_sequence_id: number;
    historical_full_backfill_sequence_id: number;
  };
  comments_backfill_sequence_id: number;
  completed_initial_comments_backfill_ts: Date | null;
}> {
  if (!conn) {
    conn = getPG();
  }
  const { rows } = await conn.query(`
    select
        frontier_sequence_id,
        frontier_sequence_id_2,
        historical_full_backfill_sequence_id,
        historical_full_backfill_sequence_id_2,
        comments_backfill_sequence_id,
        completed_initial_comments_backfill_ts
    from brainstore_backfill_global_state
    `);
  if (rows.length === 0) {
    return {
      one: {
        frontier_sequence_id: 0,
        historical_full_backfill_sequence_id: 0,
      },
      two: {
        frontier_sequence_id: 0,
        historical_full_backfill_sequence_id: 0,
      },
      comments_backfill_sequence_id: 0,
      completed_initial_comments_backfill_ts: null,
    };
  } else if (rows.length !== 1) {
    throw new InternalServerError("Expected up to one row. Found multiple.");
  }
  const data = z
    .object({
      frontier_sequence_id: z.number(),
      frontier_sequence_id_2: z.number(),
      historical_full_backfill_sequence_id: z.number(),
      historical_full_backfill_sequence_id_2: z.number(),
      comments_backfill_sequence_id: z.number(),
      completed_initial_comments_backfill_ts: z.coerce.date().nullable(),
    })
    .parse(rows[0]);
  return {
    one: {
      frontier_sequence_id: data.frontier_sequence_id,
      historical_full_backfill_sequence_id:
        data.historical_full_backfill_sequence_id,
    },
    two: {
      frontier_sequence_id: data.frontier_sequence_id_2,
      historical_full_backfill_sequence_id:
        data.historical_full_backfill_sequence_id_2,
    },
    comments_backfill_sequence_id: data.comments_backfill_sequence_id,
    completed_initial_comments_backfill_ts:
      data.completed_initial_comments_backfill_ts,
  };
}

export const brainstoreBackfillRunRequest = z.object({
  timeout: z.number().nullish(),
  iters: z.number().nullish(),
  frontier_batch_size: z.number().nullish(),
  object_batch_size: z.number().nullish(),
  disable_historical_backfill: z.boolean().nullish(),
});
export type BrainstoreBackfillRunRequest = z.infer<
  typeof brainstoreBackfillRunRequest
>;

export function getBrainstoreEtlAdvisoryLocks(): (keyof AdvisoryLocks)[] {
  const locks: (keyof AdvisoryLocks)[] = [];
  if (!BRAINSTORE_BACKFILL_DISABLE_HISTORICAL) {
    locks.push("brainstore_etl_only_historcal");
  }
  if (BRAINSTORE_BACKFILL_ENABLE_NONHISTORICAL) {
    locks.push("brainstore_etl_only_nonhistorcal");
  }
  return locks;
}

export async function runBrainstoreEtlLoopRequest({
  timeout: timeoutS,
  iters,
  object_batch_size: objectBatchSize,
  frontier_batch_size: frontierBatchSize,
  disable_historical_backfill: disableHistoricalBackfill,
}: BrainstoreBackfillRunRequest): Promise<RunEtlLoopOutput> {
  assertBrainstoreEnabled();
  const start = new Date();
  const pgConn = getPG();
  const pgClient = await pgConn.connect();
  await pgClient.query("begin");
  try {
    for (const lock of getBrainstoreEtlAdvisoryLocks()) {
      if (!(await tryAcquireAdvisoryLock(pgClient, lock))) {
        return { locked: false };
      }
    }
    const taskId = uuidv4();
    getLogger().debug({ taskId }, `[BrainstoreTS] [backfill] task starting`);
    const batches = await runBrainstoreEtlLoop({
      sourceDbTable: PG_LOGS_TABLE,
      sourceDbTable2: PG_LOGS2_TABLE,
      commentsDbTable: PG_COMMENTS_TABLE,
      loopStart: new Date(),
      timeoutS: timeoutS ?? 60,
      objectBatchSize: objectBatchSize ?? null,
      frontierBatchSize: frontierBatchSize ?? null,
      iters: iters ?? null,
      disableHistoricalBackfill: disableHistoricalBackfill ?? undefined,
      taskId,
    });
    getLogger().debug(
      {
        taskId,
        durationMs: new Date().getTime() - start.getTime(),
      },
      `[BrainstoreTS] [backfill] task completed`,
    );
    return { locked: true, tableResults: { [PG_LOGS_TABLE]: batches } };
  } finally {
    await pgClient.query("rollback");
    pgClient.release();
  }
}

export function startBrainstoreEtlLoop(): void {
  if (!brainstoreEnabled() || BRAINSTORE_DISABLE_ETL_LOOP) {
    return;
  }

  (async () => {
    getLogger().info("Starting Brainstore ETL loop");
    while (true) {
      const startTime = new Date();
      const endTime = new Date();
      const elapsedS = (endTime.getTime() - startTime.getTime()) / 1000;
      // Commented log: ETL loop took ${elapsedS} seconds
      const sleepS = ETL_PERIOD_S - elapsedS;
      if (sleepS > 0) {
        await new Promise((resolve) => setTimeout(resolve, sleepS * 1000));
      }
    }
  })().catch((err) => {
    getLogger().error(
      { error: extractDebugErrorText(err) },
      "Error in Brainstore ETL loop",
    );
  });
}

const DEFAULT_FRONTIER_BATCH_SIZE = 1000000;
const LOOP_FINISHED_SLEEP_MS = 100;

// This function assumes the ETL locks have been taken.
async function runBrainstoreEtlLoop({
  sourceDbTable,
  sourceDbTable2,
  commentsDbTable,
  loopStart,
  timeoutS,
  objectBatchSize,
  frontierBatchSize: frontierBatchSizeParam,
  iters,
  disableHistoricalBackfill,
  taskId,
}: {
  sourceDbTable: string;
  sourceDbTable2: string;
  commentsDbTable: string;
  loopStart: Date;
  timeoutS: number;
  objectBatchSize: number | null;
  frontierBatchSize: number | null;
  iters: number | null;
  disableHistoricalBackfill?: boolean;
  taskId: string;
}): Promise<EtlLoopBatch[] | null> {
  const conn = getPG();

  // Do nothing if we have no tracked objects.
  if (!BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL) {
    const { rows } = await conn.query(
      "select exists(select 1 from brainstore_backfill_tracked_objects) is_tracking",
    );
    if (!z.object({ is_tracking: z.boolean() }).parse(rows[0]).is_tracking) {
      return [];
    }
  }

  // Accumulated by updateFrontierLoop.
  const batches: EtlLoopBatch[] = [];

  // Accumulated by all loops.
  let iter = 0;

  // Each loop can mark when it has run out of objects to process for the first
  // time. The loops will still keep running to pick up new activity until all
  // loops have flipped their flag to true.
  const hasFinishedAtLeastOnce = {
    one: {
      updateFrontierLoop: false,
      updateHistoricalFullLoop: false,
    },
    two: {
      updateFrontierLoop: false,
      updateHistoricalFullLoop: false,
    },
    flipCompletedInitialBackfillLoop: false,
    processCommentsBackfillLoop: false,
  };

  // Each loop will sleep for LOOP_FINISHED_SLEEP_MS if it has run out of work
  // in its previous iteration but is still waiting for other loops to finish at
  // least once.
  const loopSleepTask = async (callerIdentifier: string) => {
    getLogger().debug(
      {
        callerIdentifier,
        sleepMs: LOOP_FINISHED_SLEEP_MS,
      },
      `Loop sleeping`,
    );
    await new Promise((resolve) => setTimeout(resolve, LOOP_FINISHED_SLEEP_MS));
    getLogger().debug({ callerIdentifier }, `Loop woke up`);
  };

  const errorSleepTask = async (callerIdentifier: string) => {
    getLogger().debug(
      {
        callerIdentifier,
        sleepMs: 1000,
      },
      `Error recovery sleep`,
    );
    await new Promise((resolve) => setTimeout(resolve, 1000));
    getLogger().debug({ callerIdentifier }, `Error recovery wake up`);
  };

  const hasTimedOut = (callerIdentifier: string) => {
    if (new Date().getTime() - loopStart.getTime() > timeoutS * 1000) {
      getLogger().debug({ callerIdentifier, timeoutS }, `Timeout reached`);
      return true;
    }
    if (iters !== null && iter >= iters) {
      getLogger().debug(
        { callerIdentifier, iterations: iter, maxIterations: iters },
        `Iteration limit reached`,
      );
      return true;
    }
    if (
      hasFinishedAtLeastOnce.one.updateFrontierLoop &&
      hasFinishedAtLeastOnce.two.updateFrontierLoop &&
      hasFinishedAtLeastOnce.one.updateHistoricalFullLoop &&
      hasFinishedAtLeastOnce.two.updateHistoricalFullLoop &&
      hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop &&
      hasFinishedAtLeastOnce.processCommentsBackfillLoop
    ) {
      getLogger().debug({ callerIdentifier }, `All loops have finished`);
      return true;
    }
    return false;
  };

  // In the first loop, we advance the frontier as far as possible towards the
  // maximum source-table sequence id.
  //
  // In batches of size frontierBatchSize, we scan the logs table from the
  // current frontier sequence_id and collect the last-encountered sequence id
  // for any object. We aggregate these manually into a max
  // last_encountered_sequence_id for each (project_id, object_type).
  //
  // Finally, we take the brainstore_etl_modify_tracked_objects advisory lock and
  // simultaneously update the frontier sequence id and the last-encountered
  // sequence ids of any existing tracked objects. We must take the lock so that
  // any concurrent process adding new tracked objects doesn't race with our
  // updates to the status of existing tracked objects.
  const updateFrontierLoop = async (sourceDbTableIndex: SourceDbTableIndex) => {
    const tbl = sourceDbTableIndex === "one" ? sourceDbTable : sourceDbTable2;
    const counterSuffix = makeCounterSuffix(sourceDbTableIndex);
    const frontierBatchSize =
      frontierBatchSizeParam ?? DEFAULT_FRONTIER_BATCH_SIZE;

    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [updateFrontierLoop] ${tbl}`;
    getLogger().debug(
      { taskId, table: tbl },
      `${callerIdentifier}: task starting`,
    );
    while (true) {
      getLogger().debug(
        { iteration: iter, table: tbl },
        `${callerIdentifier}: starting loop iteration`,
      );
      try {
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }
        const startTime = new Date();

        // This is essentially a read-write-locked way of deriving the true max
        // sequence id on the source table.
        const sourceMaxSequenceId = await getSourceMaxSequenceId(
          tbl,
          callerIdentifier,
        );

        const globalState = await getBackfillGlobalState();
        const frontierSequenceId =
          globalState[sourceDbTableIndex].frontier_sequence_id;
        if (frontierSequenceId >= sourceMaxSequenceId) {
          previousIterationFinished = true;
          if (!hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop) {
            hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop =
              true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }
        const upperBoundSequenceId = Math.min(
          frontierSequenceId + frontierBatchSize,
          sourceMaxSequenceId,
        );

        const trackedObjectsBatchInfo = await (async () => {
          const querySnippet = sql`
          select
              ${makeObjectIdExprFromTable(tbl)} object_id,
              max(sequence_id) max_sequence_id,
              min(project_id) project_id,
              min(experiment_id) experiment_id,
              min(dataset_id) dataset_id,
              min(prompt_session_id) prompt_session_id,
              min(log_id) log_id
          from ${ident(tbl)}
          where
              sequence_id > ${frontierSequenceId}
              and sequence_id <= ${upperBoundSequenceId}
              and ${makeObjectIdExprFromTable(tbl)} is not null
          group by object_id
        `;
          const { query, params } = querySnippet.toNumericParamQuery();
          const { rows } = await conn.query(query, params);
          return groupByTrackedObject(
            frontierUpdateObjectSchema.array().parse(rows),
          ).groupedRows;
        })();

        // Turn this into a list of records: [{ project_id, object_type,
        // last_encountered_sequence_id }].
        const trackedObjectRecords = Array.from(
          trackedObjectsBatchInfo.entries(),
        ).map(([key, rows]) => {
          const last_encountered_sequence_id = Math.max(
            ...rows.map((r) => r.max_sequence_id),
          );
          return {
            ...fromTrackedObjectKey(key),
            last_encountered_sequence_id,
          };
        });
        getLogger().debug(
          {
            callerIdentifier,
            trackedObjectCount: trackedObjectRecords.length,
          },
          `Found tracked objects to update`,
        );

        // Now under the brainstore_etl_modify_tracked_objects lock, update the
        // last_encountered_sequence_id for each tracked object and the global
        // frontier_sequence_id.
        //
        // Note: we filter by `last_encountered_sequence_id > 0`, to make sure
        // we don't update "null" tracking entries that were default-inserted by
        // the historical backfill loop. We only want to update tracking entries
        // that were initialized by some meaningful initial state.
        const updateLastEncounteredSequenceId = async () => {
          const client = await conn.connect();
          await client.query("begin");
          try {
            await acquireAdvisoryLock(
              client,
              "brainstore_etl_modify_tracked_objects",
            );
            {
              const queryTemplate = sql`
                with
                update_data as (
                    select *
                    from jsonb_to_recordset(${JSON.stringify(trackedObjectRecords)}::jsonb) as x(project_id text, object_type text, last_encountered_sequence_id bigint)
                ),
                ordered_update_data as (
                   select project_id, object_type, update_data.last_encountered_sequence_id
                   from brainstore_backfill_tracked_objects join update_data using (project_id, object_type)
                   order by project_id, object_type
                   for update
                )
                update brainstore_backfill_tracked_objects
                set last_encountered_sequence_id${counterSuffix} = greatest(
                    brainstore_backfill_tracked_objects.last_encountered_sequence_id${counterSuffix},
                    ordered_update_data.last_encountered_sequence_id)
                from ordered_update_data
                where
                    brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
                    and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
                    and brainstore_backfill_tracked_objects.last_encountered_sequence_id${counterSuffix} > 0
              `;
              const { query, params } = queryTemplate.toNumericParamQuery();
              await client.query(query, params);
            }
            {
              const queryTemplate = sql`
                update brainstore_backfill_global_state set frontier_sequence_id${counterSuffix} = ${upperBoundSequenceId}
              `;
              const { query, params } = queryTemplate.toNumericParamQuery();
              await client.query(query, params);
            }
            await client.query("commit");
          } catch (e) {
            await client.query("rollback");
            throw e;
          } finally {
            client.release();
          }
        };
        await Promise.all([
          updateLastEncounteredSequenceId(),
          populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
            conn,
            trackedObjectsBatchInfo,
          ),
        ]);
        getLogger().debug(
          {
            callerIdentifier,
            upperBoundSequenceId,
          },
          `[${callerIdentifier}] Updated frontier sequence id`,
        );

        const endTime = new Date();
        batches.push({
          maxSequenceId: upperBoundSequenceId,
          elapsedTimeS: (endTime.getTime() - startTime.getTime()) / 1000,
        });
        iter++;
      } catch (e) {
        getLogger().error(
          {
            callerIdentifier,
            error: extractDebugErrorText(e),
          },
          `[${callerIdentifier}] Failed to update frontier loop: ${extractDebugErrorText(e)}`,
        );
        await errorSleepTask(callerIdentifier);
      }
    }

    // After pushing the frontier forward, we can fast-forward any
    // fully-backfilled objects to our frontier sequence id. This way, if they
    // do get encountered again later, we can start from the frontier, rather
    // than from wherever they were last backfilled.
    //
    // Note: we filter by `last_encountered_sequence_id > 0`, to make sure we
    // don't update "null" tracking entries that were default-inserted by the
    // historical backfill loop. We only want to fast-forward entries that were
    // initialized by some meaningful initial state.
    getLogger().debug(
      { callerIdentifier },
      `[${callerIdentifier}] Fast-forwarding fully-backfilled objects`,
    );
    {
      const globalState = await getBackfillGlobalState();
      const frontierSequenceId =
        globalState[sourceDbTableIndex].frontier_sequence_id;

      const querySnippet = sql`
        with
        ordered_tracked_objects as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
            where
              is_backfilling
              and ${compareSequenceIdsExpr(sourceDbTableIndex)} = false
              and last_encountered_sequence_id${counterSuffix} > 0
            order by project_id, object_type
            for update
        )
        update brainstore_backfill_tracked_objects
        set last_processed_sequence_id${counterSuffix} = greatest(last_processed_sequence_id${counterSuffix}, ${frontierSequenceId})
        from ordered_tracked_objects
        where
            brainstore_backfill_tracked_objects.project_id = ordered_tracked_objects.project_id
            and brainstore_backfill_tracked_objects.object_type = ordered_tracked_objects.object_type
      `;
      const { query, params } = querySnippet.toNumericParamQuery();
      await conn.query(query, params);
    }
    getLogger().debug(
      { callerIdentifier },
      `[${callerIdentifier}] Done fast-forwarding fully-backfilled objects`,
    );
    getLogger().debug({ callerIdentifier, taskId }, `Task finished`);
  };

  // This loop walks through the logs table and creates/updates tracking entries
  // for any new objects it encounters. Each iteration, it reads from
  // `historical_full_backfill_sequence_id`, discovers which tracking entries
  // exist in the next batch of sequence IDs, and updates their tracking entries
  // to reflect this discovery.
  //
  // We rely on the backfill loops within brainstore to actually process the new
  // entries.
  const historicalFullBackfillLoop = async (
    sourceDbTableIndex: SourceDbTableIndex,
  ) => {
    const tbl = sourceDbTableIndex === "one" ? sourceDbTable : sourceDbTable2;
    const counterSuffix = makeCounterSuffix(sourceDbTableIndex);
    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [historicalFullBackfillLoop] ${tbl}`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "historicalFullBackfillLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iteration: iter }, "starting loop iteration");
      try {
        const loopStart = new Date();
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const backfillGlobalState = await getBackfillGlobalState();
        const frontierSequenceId =
          backfillGlobalState[sourceDbTableIndex].frontier_sequence_id;
        const historicalFullBackfillSequenceId =
          backfillGlobalState[sourceDbTableIndex]
            .historical_full_backfill_sequence_id;

        if (historicalFullBackfillSequenceId >= frontierSequenceId) {
          previousIterationFinished = true;
          if (
            !hasFinishedAtLeastOnce[sourceDbTableIndex].updateHistoricalFullLoop
          ) {
            pino.debug(
              "historical full backfill has caught up to frontier_sequence_id.",
            );
            hasFinishedAtLeastOnce[
              sourceDbTableIndex
            ].updateHistoricalFullLoop = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        // First grab the set of object IDs in the next batch of sequence ids, and
        // group them by tracking entry.
        const upperBoundSequenceId = Math.min(
          frontierSequenceId,
          historicalFullBackfillSequenceId +
            BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
        );
        const {
          fullTrackedObjects: batchFullTrackedObjects,
          batchInfo: trackedObjectsBatchInfo,
        } = await fetchTrackedObjectsBatchInfo({
          conn,
          tbl,
          lowerBoundSequenceId: historicalFullBackfillSequenceId,
          upperBoundSequenceId,
          additionalWhereClause: sql`true`,
        });
        pino.debug(
          {
            objectCount: trackedObjectsBatchInfo.size,
            elapsedSeconds: (Date.now() - loopStart.getTime()) / 1000,
          },
          "fetched objects",
        );

        // For all tracking entries in this batch, determine the tightest
        // (last_processed_sequence_id, last_encountered_sequence_id) based on
        // the min/max sequence IDs we encountered in this batch.
        //
        // We use (0, 0) for the other logs table's sequence IDs.
        // last_encountered_sequence_id = 0 will be ignored by the other log's
        // frontier loop. If the other log's historical loop encounters the same
        // tracking entry, it will update the range.
        const batchFullTrackedObjectsToAdd: AddTrackedObjectInfo[] =
          batchFullTrackedObjects
            .map((trackedObject) => {
              const key = trackedObjectKey(trackedObject);
              const batchObjects = trackedObjectsBatchInfo.get(key);
              if (!batchObjects?.length) {
                return null;
              }
              const initMinMax: [number, number] = [
                Number.POSITIVE_INFINITY,
                0,
              ];
              const minMaxSequenceId = batchObjects.reduce((minMax, obj) => {
                const ret: [number, number] = [
                  Math.min(minMax[0], obj.min_sequence_id),
                  Math.max(minMax[1], obj.max_sequence_id),
                ];
                return ret;
              }, initMinMax);
              const lastProcessedSequenceId = Math.max(
                minMaxSequenceId[0] - 1,
                0,
              );
              const lastEncounteredSequenceId = minMaxSequenceId[1];
              return {
                ...trackedObject,
                ...(sourceDbTableIndex === "one"
                  ? {
                      new_entry_last_processed_sequence_id:
                        lastProcessedSequenceId,
                      new_entry_last_encountered_sequence_id:
                        lastEncounteredSequenceId,
                      new_entry_last_processed_sequence_id_2: 0,
                      new_entry_last_encountered_sequence_id_2: 0,
                    }
                  : {
                      new_entry_last_processed_sequence_id: 0,
                      new_entry_last_encountered_sequence_id: 0,
                      new_entry_last_processed_sequence_id_2:
                        lastProcessedSequenceId,
                      new_entry_last_encountered_sequence_id_2:
                        lastEncounteredSequenceId,
                    }),
              };
            })
            .filter((x) => x !== null);

        // Add objects to the tracking table. For any new entries, mark
        // is_backfilling=true.
        await addTrackedObjects({
          trackedObjects: batchFullTrackedObjectsToAdd,
          shouldBackfill: true,
        });

        // Because addTrackedObjects only sets the sequence_ids for new entries,
        // we go through and update the tracking entries in this batch to
        // reflect any sequence ID updates. We use
        // `last_encountered_sequence_id=0` as a proxy for "tracking entry is
        // uninitialized", which means we can update
        // last_processed_sequence_id/initial_sequence_id.
        // last_encountered_sequence_id is maxed with the value we determined.
        {
          const queryTemplate = sql`
            with
            update_data as (
                select *
                from jsonb_to_recordset(${JSON.stringify(batchFullTrackedObjectsToAdd)}::jsonb)
                         as x(project_id text, object_type text,
                              new_entry_last_processed_sequence_id bigint,
                              new_entry_last_encountered_sequence_id bigint,
                              new_entry_last_processed_sequence_id_2 bigint,
                              new_entry_last_encountered_sequence_id_2 bigint)
            ),
            ordered_update_data as (
               select *
               from update_data
               order by project_id, object_type
               for update
            )
            update brainstore_backfill_tracked_objects
            set
              last_processed_sequence_id${counterSuffix} = (case
                when last_encountered_sequence_id${counterSuffix} = 0
                then ordered_update_data.new_entry_last_processed_sequence_id${counterSuffix}
                else last_processed_sequence_id${counterSuffix}
                end
              ),
              initial_sequence_id${counterSuffix} = (case
                when last_encountered_sequence_id${counterSuffix} = 0
                then ordered_update_data.new_entry_last_processed_sequence_id${counterSuffix}
                else initial_sequence_id${counterSuffix}
                end
              ),
              last_encountered_sequence_id${counterSuffix} = greatest(
                last_encountered_sequence_id${counterSuffix},
                ordered_update_data.new_entry_last_encountered_sequence_id${counterSuffix}
              )
            from ordered_update_data
            where
                brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
                and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
          `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }

        // Finally, update the global backfill state to advance the
        // historical_full_backfill_sequence_id.
        {
          const queryTemplate = sql`
            update brainstore_backfill_global_state set historical_full_backfill_sequence_id${counterSuffix} = ${upperBoundSequenceId}
          `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }
        pino.debug(
          {
            from: historicalFullBackfillSequenceId,
            to: upperBoundSequenceId,
          },
          `Advanced sequence ID`,
        );
        iter++;
      } catch (e) {
        pino.error(
          { error: extractDebugErrorText(e) },
          "failed. continuing...",
        );
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  // Analogue to the historicalFullBackfillLoop for the comments table. This
  // runs without the "explicit object tracking" infrastructure we have for the
  // logs table. We also still do the processing within this loop because we
  // don't have brainstore loops for comments yet.
  const backfillCommentsLoop = async () => {
    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [backfillCommentsLoop] ${commentsDbTable}`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "backfillCommentsLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iteration: iter }, "starting loop iteration");
      try {
        const loopStart = new Date();
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const [getCommentsMaxSequenceIdRes, getBackfillGlobalStateRes] =
          await Promise.all([
            getCommentsMaxSequenceId(),
            getBackfillGlobalState(),
          ]);
        const { comments_max_sequence_id: commentsMaxSequenceId } =
          getCommentsMaxSequenceIdRes;
        const {
          comments_backfill_sequence_id: commentsBackfillSequenceId,
          completed_initial_comments_backfill_ts:
            completedInitialCommentsBackfillTs,
        } = getBackfillGlobalStateRes;

        if (commentsBackfillSequenceId >= commentsMaxSequenceId) {
          previousIterationFinished = true;
          if (!hasFinishedAtLeastOnce.processCommentsBackfillLoop) {
            pino.debug("comments backfill has caught up.");
            hasFinishedAtLeastOnce.processCommentsBackfillLoop = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        // First grab the set of object IDs in the next batch of sequence ids, and
        // group them by tracking entry.
        const upperBoundSequenceId = Math.min(
          commentsMaxSequenceId,
          commentsBackfillSequenceId +
            BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
        );
        const trackedObjectsBatchInfo = (
          await fetchTrackedObjectsBatchInfo({
            conn,
            tbl: commentsDbTable,
            lowerBoundSequenceId: commentsBackfillSequenceId,
            upperBoundSequenceId,
            additionalWhereClause: sql`true`,
          })
        ).batchInfo;
        pino.debug(
          {
            objectCount: trackedObjectsBatchInfo.size,
            elapsedSeconds: (Date.now() - loopStart.getTime()) / 1000,
          },
          "fetched objects",
        );

        await runProcessWalBackfillBatch({
          conn,
          callerIdentifier: "backfillCommentsLoop",
          lowerBoundSequenceId: commentsBackfillSequenceId,
          upperBoundSequenceId,
          maxSequenceId: commentsMaxSequenceId,
          readComments: true,
          // If we haven't yet reached the end of the comments table, mark all of
          // the processed entries as compacted, so that we don't trigger
          // compaction on their behalf. This way, we won't incur a time-consuming
          // compaction from an old xact_id due to an old comment. But once we've
          // caught up, it's fine to run compactions from comments.
          markAsCompacted: !completedInitialCommentsBackfillTs,
          objectBatchSize,
          trackedObjectsBatchInfo,
          trackedObjectsBackfillInfo: null,
        });

        // Finally, update the global backfill state to advance the
        // comments_backfill_sequence_id, and flip the
        // completed_ts if we have gotten to the end.
        {
          const queryTemplate = sql`
          update brainstore_backfill_global_state
          set
              comments_backfill_sequence_id = greatest(comments_backfill_sequence_id, ${upperBoundSequenceId}::bigint),
              completed_initial_comments_backfill_ts = (case
                  when completed_initial_comments_backfill_ts is null and ${upperBoundSequenceId}::bigint >= ${commentsMaxSequenceId}::bigint then now()
                  else completed_initial_comments_backfill_ts
              end)
        `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }
        pino.debug(
          {
            from: commentsBackfillSequenceId,
            to: upperBoundSequenceId,
          },
          "advanced sequence ID",
        );

        iter++;
      } catch (e) {
        pino.error(
          { error: extractDebugErrorText(e) },
          "failed. continuing...",
        );
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  // In the final loop, we flip the completed_initial_backfill_ts for any
  // tracking entry that meets the following criteria:
  //
  // - completed_initial_backfill_ts is null
  //
  // - last_processed_sequence_id >= last_encountered_sequence_id
  //
  // - for all brainstore objects linked to this tracking entry, the value of
  // (ts(last_processed_xact_id) - ts(max(last_compacted_xact_id))) <=
  // ETL_PERIOD_S. If there are no linked brainstore objects, this criterion is
  // vacuously true.
  //
  // Since the compaction lag criterion is somewhat more expensive to check, we
  // restrict the update to a batch of tracked entries that fulfill the other
  // criteria before checking it.
  const flipCompletedInitialBackfillLoop = async () => {
    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [flipCompletedInitialBackfillLoop]`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "flipCompletedInitialBackfillLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iter }, "starting loop iteration");
      try {
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const queryTemplate = sql`
          with
          candidate_tracking_entries as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
            where
                is_backfilling
                and (last_processed_sequence_id >= last_encountered_sequence_id)
                and (last_processed_sequence_id_2 >= last_encountered_sequence_id_2)
                and ${isAlreadyBackfilledExpr(false)}
            limit 100
          ),
          tracking_entry_to_brainstore_object as (
            select brainstore_backfill_tracked_objects_to_brainstore_objects.*
            from brainstore_backfill_tracked_objects_to_brainstore_objects
                join candidate_tracking_entries using (project_id, object_type)
          ),
          brainstore_object_to_last_compacted_xact_id as (
            select
                tracking_entry_to_brainstore_object.brainstore_object_id,
                -- We need the additional case machinery to avoid ignoring
                -- un-compacted segments.
                (case
                    when bool_and(brainstore_global_store_segment_id_to_metadata.last_compacted_index_meta_xact_id is not null)
                        then max(brainstore_global_store_segment_id_to_metadata.last_compacted_index_meta_xact_id)
                    else null
                end) last_compacted_xact_id
            from
                tracking_entry_to_brainstore_object
                join brainstore_global_store_segment_id_to_liveness
                    on tracking_entry_to_brainstore_object.brainstore_object_id = brainstore_global_store_segment_id_to_liveness.object_id
                    and brainstore_global_store_segment_id_to_liveness.is_live
                join brainstore_global_store_segment_id_to_metadata using (segment_id)
            group by brainstore_object_id
          ),
          brainstore_object_to_compaction_delta as (
            select
                tracking_entry_to_brainstore_object.brainstore_object_id,
                (((brainstore_global_store_object_id_to_metadata.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint) -
                 ((brainstore_object_to_last_compacted_xact_id.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) compaction_delta
            from
                tracking_entry_to_brainstore_object
                join brainstore_global_store_object_id_to_metadata
                    on tracking_entry_to_brainstore_object.brainstore_object_id = brainstore_global_store_object_id_to_metadata.object_id
                join brainstore_object_to_last_compacted_xact_id using (brainstore_object_id)
          ),
          tracking_entry_to_max_compaction_delta as (
            select
                candidate_tracking_entries.project_id,
                candidate_tracking_entries.object_type,
                max(brainstore_object_to_compaction_delta.compaction_delta) max_compaction_delta
            from
                candidate_tracking_entries
                left join tracking_entry_to_brainstore_object using (project_id, object_type)
                left join brainstore_object_to_compaction_delta using (brainstore_object_id)
            group by project_id, object_type
          ),
          tracking_entries_to_flip as (
            select project_id, object_type from tracking_entry_to_max_compaction_delta
            where max_compaction_delta <= ${ETL_PERIOD_S}::float8 or max_compaction_delta isnull
          ),
          ordered_tracking_entries_to_flip as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
                join tracking_entries_to_flip using (project_id, object_type)
            order by project_id, object_type
            for update
          )
          update brainstore_backfill_tracked_objects
          set completed_initial_backfill_ts = now()
          from ordered_tracking_entries_to_flip
          where
            brainstore_backfill_tracked_objects.project_id = ordered_tracking_entries_to_flip.project_id
            and brainstore_backfill_tracked_objects.object_type = ordered_tracking_entries_to_flip.object_type
      `;
        const { query, params } = queryTemplate.toNumericParamQuery();
        const { rowCount } = await conn.query(query, params);
        if (rowCount === 0) {
          previousIterationFinished = true;
          if (!hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop) {
            pino.debug(
              "Finished marking objects as completed initial backfill",
            );
            hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        iter++;
      } catch (e) {
        pino.error(
          { error: extractDebugErrorText(e) },
          "failed. continuing...",
        );
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  const tasks: (() => Promise<void>)[] = [];

  if (
    BRAINSTORE_BACKFILL_DISABLE_HISTORICAL ||
    disableHistoricalBackfill ||
    !BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL
  ) {
    hasFinishedAtLeastOnce.one.updateHistoricalFullLoop = true;
    hasFinishedAtLeastOnce.two.updateHistoricalFullLoop = true;
  } else {
    tasks.push(() => historicalFullBackfillLoop("one"));
    tasks.push(() => historicalFullBackfillLoop("two"));
  }

  if (BRAINSTORE_BACKFILL_DISABLE_FRONTIER_LOOP) {
    hasFinishedAtLeastOnce.one.updateFrontierLoop = true;
    hasFinishedAtLeastOnce.two.updateFrontierLoop = true;
    hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop = true;
  } else {
    tasks.push(() => updateFrontierLoop("one"));
    tasks.push(() => updateFrontierLoop("two"));
    tasks.push(() => flipCompletedInitialBackfillLoop());
  }

  if (BRAINSTORE_BACKFILL_DISABLE_COMMENTS) {
    hasFinishedAtLeastOnce.processCommentsBackfillLoop = true;
  } else {
    tasks.push(backfillCommentsLoop);
  }

  await Promise.all(tasks.map((t) => t()));

  return batches;
}

const dbObjectIdsSchema = z.object({
  object_id: z.string(),
  project_id: z.string().nullish(),
  experiment_id: z.string().nullish(),
  dataset_id: z.string().nullish(),
  prompt_session_id: z.string().nullish(),
  log_id: z.string().nullish(),
  org_id: z.string().nullish(),
});
type DbObjectIds = z.infer<typeof dbObjectIdsSchema>;

const frontierUpdateObjectSchema = dbObjectIdsSchema.and(
  z.object({
    max_sequence_id: z.number(),
  }),
);

const processWalObjectSchema = dbObjectIdsSchema.and(
  z.object({
    min_sequence_id: z.number(),
    max_sequence_id: z.number(),
    min_xact_id: z.string(),
    max_xact_id: z.string(),
  }),
);

type ProcessWalObject = z.infer<typeof processWalObjectSchema>;

// NOTE: if you want the FullTrackedObject to include an org_id, then the input
// rows must include an org_id.
function groupByTrackedObject<T extends DbObjectIds>(
  rows: T[],
): { fullTrackedObjects: FullTrackedObject[]; groupedRows: Map<string, T[]> } {
  const fullTrackedObjects: Map<string, FullTrackedObject> = new Map();
  const groupedRows = new Map<string, T[]>();
  for (const row of rows) {
    const objectIdSet = objectIdsUnionSchema.parse(row);
    const parsedObjectType = backfillableObjectTypeSchema.safeParse(
      objectIdSet[OBJECT_TYPE_FIELD],
    );
    if (row.project_id && parsedObjectType.success) {
      const key = trackedObjectKey({
        project_id: row.project_id,
        object_type: parsedObjectType.data,
      });
      fullTrackedObjects.set(key, {
        org_id: row.org_id ?? null,
        project_id: row.project_id,
        object_type: parsedObjectType.data,
      });
      mapSetDefault(groupedRows, key, []).push(row);
    }
  }
  return {
    fullTrackedObjects: Array.from(fullTrackedObjects.values()),
    groupedRows,
  };
}

type BackfillObjectBatch = {
  project_id: string;
  object_type: string;
  last_processed_sequence_id: number;
  last_encountered_sequence_id: number;
};

function trackedObjectKey({
  project_id,
  object_type,
}: {
  project_id: string;
  object_type: string;
}) {
  return `${project_id}/${object_type}`;
}

function fromTrackedObjectKey(key: string) {
  const [project_id, object_type] = key.split("/");
  return { project_id, object_type };
}

async function fetchTrackedObjectsBatchInfo({
  conn,
  tbl,
  lowerBoundSequenceId,
  upperBoundSequenceId,
  additionalWhereClause,
}: {
  conn: PoolingBtPg;
  tbl: string;
  lowerBoundSequenceId: number;
  upperBoundSequenceId: number;
  additionalWhereClause: ToSQL;
}): Promise<{
  fullTrackedObjects: FullTrackedObject[];
  batchInfo: Map<string, ProcessWalObject[]>;
}> {
  const querySnippet = sql`
    select
        ${makeObjectIdExprFromTable(tbl)} object_id,
        min(sequence_id) min_sequence_id,
        max(sequence_id) max_sequence_id,
        min(_xact_id)::text min_xact_id,
        max(_xact_id)::text max_xact_id,
        min(project_id) project_id,
        min(experiment_id) experiment_id,
        min(dataset_id) dataset_id,
        min(prompt_session_id) prompt_session_id,
        min(log_id) log_id,
        min(org_id) org_id
    from ${ident(tbl)}
    where
        true
        and sequence_id > ${lowerBoundSequenceId}
        and sequence_id <= ${upperBoundSequenceId}
        and ${makeObjectIdExprFromTable(tbl)} is not null
        and project_id is not null
        and ${additionalWhereClause}
      group by object_id
  `;
  const { query, params } = querySnippet.toNumericParamQuery();
  const { rows } = await conn.query(query, params);
  const { fullTrackedObjects, groupedRows } = groupByTrackedObject(
    processWalObjectSchema.array().parse(rows),
  );
  return { fullTrackedObjects, batchInfo: groupedRows };
}

async function runProcessWalBackfillBatch({
  conn,
  callerIdentifier,
  lowerBoundSequenceId,
  upperBoundSequenceId,
  maxSequenceId,
  readLogs2,
  readComments,
  markAsCompacted,
  objectBatchSize,
  trackedObjectsBatchInfo,
  trackedObjectsBackfillInfo,
}: {
  conn: PoolingBtPg;
  callerIdentifier: string;
  lowerBoundSequenceId: number;
  upperBoundSequenceId: number;
  maxSequenceId: number;
  readLogs2?: boolean;
  readComments?: boolean;
  markAsCompacted?: boolean;
  objectBatchSize: number | null;
  // The keys should be constructed via trackedObjectKey.
  trackedObjectsBatchInfo: Map<string, ProcessWalObject[]>;
  trackedObjectsBackfillInfo: Map<string, BackfillObjectBatch> | null;
}): Promise<void> {
  // Filter the trackedObjectsBatchInfo down to a list of objects that need
  // backfilling within this batch. Map them to their original tracking entry.
  const objectIdsInBatch = new Map<
    string,
    { project_id: string; object_type: string }
  >();
  trackedObjectsBatchInfo.forEach((rows, trackedObjectsKey) => {
    rows.forEach((row) => {
      if (trackedObjectsBackfillInfo) {
        const backfillInfo = trackedObjectsBackfillInfo.get(trackedObjectsKey);
        if (!backfillInfo) {
          return;
        }
        if (
          row.max_sequence_id < backfillInfo.last_processed_sequence_id ||
          row.min_sequence_id > backfillInfo.last_encountered_sequence_id
        ) {
          // Skip this object because we don't need to backfill it.
          return;
        }
      }
      const trackedObject = fromTrackedObjectKey(trackedObjectsKey);
      const objectIdsUnion = objectIdsUnionSchema.parse(row);
      objectIdsInBatch.set(
        makeBrainstoreObjectId(objectIdsUnion),
        trackedObject,
      );
    });
  });

  // If we have any objects to backfill, process the WAL between the
  // sequence ID range. Also add the set of brainstore object ID <-> tracked
  // object associations to postgres.
  if (objectIdsInBatch.size > 0) {
    // There is a general issue with backfilling, due to the fact that we
    // process in ranges of sequence ids. It's possible that an individual
    // transaction for an object, spanning multiple sequence ids, may get split
    // into separate backfill batches. This is problematic for realtime queries,
    // because say our first batch has completed and updated its
    // `last_processed_xact_id` to X. If we then make a realtime query, it will
    // think the entire transaction X has been processed and read it from the
    // segment WAL, and then miss the remaining rows for that transaction.
    //
    // This issue shows up in our unit tests, so we insert a hacky-workaround
    // for it. If our upperBoundSequenceId is close-enough to the
    // maxSequenceId, we extend the upperBoundSequenceId to the
    // maxSequenceId, but bounding by the max_xact_id in our batch. This
    // will guarantee that we process all logs for any transactions in the
    // "original" batch we wanted to process.
    const { sequenceIdEnd, endXactId } = (() => {
      const maxXactIdInBatch = [...objectIdsInBatch.values()]
        .map((v) => {
          const batchInfoKey = trackedObjectKey(v);
          const batchInfo = trackedObjectsBatchInfo.get(batchInfoKey);
          if (!batchInfo) {
            return "";
          }
          return batchInfo.map((o) => o.max_xact_id);
        })
        .flat()
        .reduce((max, xactId) => {
          if (!max || (xactId && xactId > max)) {
            return xactId;
          }
          return max;
        }, "");
      if (upperBoundSequenceId + 20000 >= maxSequenceId && maxXactIdInBatch) {
        return {
          sequenceIdEnd: maxSequenceId,
          endXactId: maxXactIdInBatch,
        };
      } else {
        return {
          sequenceIdEnd: upperBoundSequenceId,
          endXactId: undefined,
        };
      }
    })();

    await Promise.all([
      processWal({
        callerIdentifier,
        objectIds: [...objectIdsInBatch.keys()],
        sequenceIdStart: lowerBoundSequenceId + 1,
        sequenceIdEnd,
        endXactId,
        readLogs2,
        readComments,
        markAsCompacted,
        disableTracing: true,
        batchSize: objectBatchSize ?? undefined,
      }),
      populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
        conn,
        trackedObjectsBatchInfo,
      ),
    ]);
  }
}

async function populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
  conn: PoolingBtPg,
  trackedObjectToBrainstoreObjects: Map<string, DbObjectIds[]>,
) {
  const snippets = [...trackedObjectToBrainstoreObjects.entries()].flatMap(
    ([trackedObjectKey, objectIdsArr]) => {
      const trackedObject = fromTrackedObjectKey(trackedObjectKey);
      return objectIdsArr.map((objectIds) => {
        const objectIdsUnion = objectIdsUnionSchema.parse(objectIds);
        const brainstoreObjectId = makeBrainstoreObjectId(objectIdsUnion);
        return sql`(${trackedObject.project_id}, ${trackedObject.object_type}, ${brainstoreObjectId})`;
      });
    },
  );
  if (!snippets.length) return;
  const queryTemplate = sql`
      insert into brainstore_backfill_tracked_objects_to_brainstore_objects(project_id, object_type, brainstore_object_id)
      values ${join(snippets, ",")} on conflict do nothing
  `;
  const { query, params } = queryTemplate.toNumericParamQuery();
  await conn.query(query, params);
}

export type AddTrackedObjectInfo = FullTrackedObject & {
  // All sequence IDs will be minimum'd with the frontier sequence IDs from
  // the backfill state table, so we don't exceed the frontier loop.
  new_entry_last_processed_sequence_id: number;
  new_entry_last_processed_sequence_id_2: number;
  // If undefined, will use the frontier sequence IDs from the backfill state
  // table.
  new_entry_last_encountered_sequence_id: number | undefined;
  new_entry_last_encountered_sequence_id_2: number | undefined;
};

export async function addTrackedObjects({
  trackedObjects,
  shouldBackfill,
  noSkipExistingTrackingEntries,
}: {
  trackedObjects: AddTrackedObjectInfo[];
  shouldBackfill: boolean;
  noSkipExistingTrackingEntries?: boolean;
}): Promise<void> {
  // The process for adding tracked objects is as follows:
  //
  // 0. Filter out any objects that are already tracked from the input list,
  // before acquiring a lock. If noSkipExistingTrackingEntries is true, then we
  // skip this step, which will force a refresh of the tracking entries in step
  // 2.
  //
  // 1. Within a transaction, grab the brainstore_etl_modify_tracked_objects
  // advisory lock, to block out the frontier update process from reading the
  // set of tracked objects while we're updating it.
  //
  // - Snapshot the current frontier sequence id
  //
  // - Upsert the set of tracked objects, with defaults determined by the
  // new_entry*_sequence_id fields in the tracked object.
  //
  // 2. Outside the transaction, flip the is_backfilling flag for any tracked
  // objects if specified.

  if (trackedObjects.length === 0) {
    return;
  }

  const conn = getPG();

  // Step 0, filter out any objects that are already tracked, before acquiring a
  // lock. Skip this step if noSkipExistingTrackingEntries is true.
  if (!noSkipExistingTrackingEntries) {
    const alreadyTrackedObjects = await (async () => {
      const { rows } = await conn.query(
        `
          select project_id, object_type from brainstore_backfill_tracked_objects
          where (project_id, object_type) in
            (select x.project_id, x.object_type
             from jsonb_to_recordset($1::jsonb) as x(project_id text, object_type text))
          `,
        [
          JSON.stringify(
            trackedObjects.map((o) => ({
              project_id: o.project_id,
              object_type: o.object_type,
            })),
          ),
        ],
      );

      return trackedObjectSchema.array().parse(rows);
    })();

    trackedObjects = trackedObjects.filter(
      (o) =>
        !alreadyTrackedObjects.some(
          (t) =>
            t.project_id === o.project_id && t.object_type === o.object_type,
        ),
    );

    if (trackedObjects.length === 0) {
      return;
    }
  }

  // Step 1.
  await (async () => {
    const client = await conn.connect();
    await client.query("begin");
    try {
      await acquireAdvisoryLock(
        client,
        "brainstore_etl_modify_tracked_objects",
      );
      const backfillGlobalState = await getBackfillGlobalState(client);
      const frontierSequenceId = backfillGlobalState.one.frontier_sequence_id;
      const frontierSequenceId2 = backfillGlobalState.two.frontier_sequence_id;
      await client.query(
        `
        insert into brainstore_backfill_tracked_objects(
            project_id, object_type, last_processed_sequence_id, last_encountered_sequence_id,
            last_processed_sequence_id_2, last_encountered_sequence_id_2,
            org_id, initial_sequence_id, initial_sequence_id_2)
         select * from jsonb_to_recordset($1::jsonb) as t(
            project_id text, object_type text, last_processed_sequence_id bigint, last_encountered_sequence_id bigint,
            last_processed_sequence_id_2 bigint, last_encountered_sequence_id_2 bigint,
            org_id text, initial_sequence_id bigint, initial_sequence_id_2 bigint)
         on conflict do nothing
      `,
        [
          JSON.stringify(
            trackedObjects.map((o) => ({
              ...o,
              initial_sequence_id: Math.min(
                o.new_entry_last_processed_sequence_id,
                frontierSequenceId,
              ),
              last_processed_sequence_id: Math.min(
                o.new_entry_last_processed_sequence_id,
                frontierSequenceId,
              ),
              last_encountered_sequence_id: Math.min(
                o.new_entry_last_encountered_sequence_id ??
                  Number.POSITIVE_INFINITY,
                frontierSequenceId,
              ),
              initial_sequence_id_2: Math.min(
                o.new_entry_last_processed_sequence_id_2,
                frontierSequenceId2,
              ),
              last_processed_sequence_id_2: Math.min(
                o.new_entry_last_processed_sequence_id_2,
                frontierSequenceId2,
              ),
              last_encountered_sequence_id_2: Math.min(
                o.new_entry_last_encountered_sequence_id_2 ??
                  Number.POSITIVE_INFINITY,
                frontierSequenceId2,
              ),
            })),
          ),
        ],
      );
      await client.query("commit");
    } catch (e) {
      await client.query("rollback");
      throw e;
    } finally {
      client.release();
    }
  })();

  // Step 2.
  if (shouldBackfill) {
    const querySnippet = sql`
      with
      update_data as (
          select *
          from jsonb_to_recordset(${JSON.stringify(trackedObjects)}::jsonb) as x(project_id text, object_type text)
      ),
      ordered_update_data as (
         select project_id, object_type
         from brainstore_backfill_tracked_objects join update_data using (project_id, object_type)
         order by project_id, object_type
         for update
      )
      update brainstore_backfill_tracked_objects
      set is_backfilling = true
      from ordered_update_data
      where
        brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
        and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
    `;
    const { query, params } = querySnippet.toNumericParamQuery();
    await conn.query(query, params);
  }
}

export async function acquireAdvisoryLock(
  conn: PoolingBtPg | PoolingBtPgClient,
  lock: keyof AdvisoryLocks,
) {
  const advisoryLocks = await advisoryLockInfo(conn);
  await conn.query(
    `select pg_advisory_xact_lock($1) result /* for ${lock} */`,
    [advisoryLocks[lock]],
  );
}

export async function tryAcquireAdvisoryLock(
  conn: PoolingBtPg | PoolingBtPgClient,
  lock: keyof AdvisoryLocks,
) {
  const advisoryLocks = await advisoryLockInfo(conn);
  const { rows } = await conn.query(
    "select pg_try_advisory_xact_lock($1) result /* for ${lock} */",
    [advisoryLocks[lock]],
  );
  return z.boolean().parse(rows[0]["result"]);
}

// Notes:
// * There is a tricky race condition where two concurrent writes may do the following:
//
//   Thread A   |   Thread B | ETL thread
//   ------------------------------------
//    begin      |   begin    |
//    insert     |            |
//               |   insert   |
//               |   commit   |
//               |            |  perform etl
//               |            |    - note Thread B will have a higher sequence id
//               |            |      and Thread A's row is not visible yet, so we
//               |            |      will miss it.
//
// We work around this by snapshotting the max sequence id and the set of active transactions, and then waiting for
// all of those transactions to complete before proceeding. Technically, what this guarantees is that any inflight writes
// which reserved those sequence ids will have either committed (or rolled back), so we don't need to worry about
// missing any rows.
export async function getSourceMaxSequenceId(
  table: string,
  callerIdentifier?: string,
) {
  const pino = getLogger();
  const pgConn = getPG();
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
      },
      "getSourceMaxSequenceId: getting max sequence id...",
    );
  }
  const maxSequenceId = await selectMaxSequenceId(pgConn, table);
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
        maxSequenceId,
      },
      "max sequence id is",
    );
  }
  await flushAwaitActiveTransactions(pgConn, callerIdentifier);
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
      },
      "Finished flushing active transactions.",
    );
  }
  return maxSequenceId;
}

async function selectMaxSequenceId(
  pgClient: PoolingBtPg | PoolingBtPgClient,
  table: string,
) {
  const { query, params } =
    sql`select max(sequence_id) res from ${ident(table)}`.toNumericParamQuery();
  const { rows } = await pgClient.query(query, params);
  const res = z.coerce.number().nullish().parse(rows[0]["res"]);
  return res ?? 0;
}

async function getActiveTransactions(conn: PoolingBtPg | PoolingBtPgClient) {
  const { rows } = await conn.query(
    `SELECT DISTINCT backend_xid FROM pg_stat_activity WHERE backend_xid is not null AND backend_type = 'client backend' and query like '%${LOG_PG_TRANSACTION_QUERY_IDENTIFIER}%'`,
  );
  return new Set(rows.map((row) => row["backend_xid"]));
}

const FLUSH_TIMEOUT_MS = 600 * 1000; // 5 minutes
async function flushAwaitActiveTransactions(
  conn: PoolingBtPg | PoolingBtPgClient,
  callerIdentifier?: string,
) {
  const pino = getLogger();
  let activeTransactions = await getActiveTransactions(conn);
  const startTime = new Date();
  let iter = 0;
  while (activeTransactions.size > 0) {
    if (callerIdentifier && iter % 10 === 0) {
      pino.debug(
        {
          callerIdentifier,
          activeTransactions: activeTransactions.size,
        },
        "Active transactions remaining",
      );
    }
    if (new Date().getTime() - startTime.getTime() > FLUSH_TIMEOUT_MS) {
      throw new Error(
        `Timed out waiting for active transactions to flush after ${FLUSH_TIMEOUT_MS}ms`,
      );
    }
    const currentlyActiveTransactions = await getActiveTransactions(conn);
    const stillActiveTransactions = new Set();
    for (const tx of activeTransactions) {
      if (currentlyActiveTransactions.has(tx)) {
        stillActiveTransactions.add(tx);
      }
    }
    activeTransactions = stillActiveTransactions;

    if (activeTransactions.size > 0) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    iter++;
  }

  if (new Date().getTime() - startTime.getTime() > 10 * 1000) {
    pino.warn(
      {
        activeTransactions: activeTransactions.size,
        durationMs: new Date().getTime() - startTime.getTime(),
      },
      "Flushed active transactions took a long time",
    );
  }
}
