import {
  OBJECT_DELETE_FIELD,
  TRANSACTION_ID_FIELD,
  recordFind,
} from "@braintrust/core";
import { z } from "zod";
import {
  PhysicalSchema,
  PostgresTableDefinition,
} from "@braintrust/btql/schema";
import {
  ObjectIdFields,
  ObjectIdsUnion,
  insertControlFieldsSchema,
  omitSchema,
} from "@braintrust/local/api-schema";
import {
  PAGINATION_CURSOR_XACT_ID_FIELD,
  PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
} from "./pagination_cursor";

export const singleObjectTypeSchema = z.enum([
  "experiment",
  "dataset",
  "prompt_session",
  "playground_logs",
  "project_logs",
  "project_prompts",
  "project_functions",
]);
export type SingleObjectType = z.infer<typeof singleObjectTypeSchema>;

export const crossObjectTypeSchema = z.enum([
  "org_prompts",
  "org_functions",
  "org_project_metadata",
  "project",
]);
export type CrossObjectType = z.infer<typeof crossObjectTypeSchema>;

export const objectTypeSchema = z.union([
  singleObjectTypeSchema,
  crossObjectTypeSchema,
]);
export type ObjectType = z.infer<typeof objectTypeSchema>;

// Note: the ordering of fields here is mirrored in SQL queries in
// api-ts/src/object_scan.ts, api-ts/src/query_util.ts, and other places.
export const LEAF_OBJECT_ID_FIELDS = [
  "project_id",
  "experiment_id",
  "dataset_id",
  "prompt_session_id",
  "log_id",
] as const;
export const OBJECT_ID_FIELDS = [...LEAF_OBJECT_ID_FIELDS, "org_id"] as const;

export function getLeafObjectId(
  row: ObjectIdsUnion,
  f: (typeof LEAF_OBJECT_ID_FIELDS)[number],
): string | null | undefined {
  // The typesystem can't handle indexing certain ID fields which are in
  // some object ID configurations but not in others.
  if (f === "project_id") {
    return f in row ? row[f] : undefined;
  } else {
    return row[f];
  }
}

const insertControlFieldKeys: string[] =
  insertControlFieldsSchema.keyof().options;
export const noControlFieldsSchema = z.object(
  Object.fromEntries(insertControlFieldKeys.map((f) => [f, omitSchema])),
);

export function removeControlFields(
  opaqueData: Record<string, unknown>,
): Record<string, unknown> {
  return Object.fromEntries(
    Object.entries(opaqueData).filter(
      ([k, _v]) => !insertControlFieldKeys.includes(k),
    ),
  );
}

// Over time, we may change the names of fields. We need to be able to do this
// without backfilling data (which is written as plain JSON). This mapping
// tracks those changes. Changes specific to individual object types are handled
// in the functions below.
const globalColumnStorageToLogical: Record<string, string> = {
  inputs: "input",
};

export function getStorageToLogicalMap(
  objectType: SingleObjectType,
): Record<string, string> {
  return {
    ...globalColumnStorageToLogical,
    ...(objectType === "dataset" ? { output: "expected" } : {}),
  };
}

export function getLogicalToStorageMap(
  objectType: SingleObjectType,
): Record<string, string> {
  return Object.fromEntries(
    Object.entries(getStorageToLogicalMap(objectType)).map(([k, v]) => [v, k]),
  );
}

export function convertPathStorageLogical(
  directionMap: Record<string, string>,
  path: string[],
): string[] {
  return path
    .slice(0, 1)
    .map((x) => recordFind(directionMap, x) ?? x)
    .concat(path.slice(1));
}

const auditLogBasePostgresColumns: PostgresTableDefinition["columns"] = {
  origin: { path: ["data", "origin"], type: { type: "jsonb" } },
  source: { path: ["data", "source"], type: { type: "text" } },
  metadata: { path: ["data", "metadata"], type: { type: "jsonb" } },
  comment: { path: ["data", "comment"], type: { type: "jsonb" } },
  audit_data: { path: ["data", "audit_data"], type: { type: "jsonb" } },
};

function makeAuditLogSchema<T>(
  columns: Record<string, T>,
  auditColumns: Record<string, T>,
): Record<string, T> {
  return {
    ...Object.fromEntries(
      Object.entries(columns).filter(([k, _]) =>
        [
          "id",
          TRANSACTION_ID_FIELD,
          OBJECT_DELETE_FIELD,
          "created",
          PAGINATION_CURSOR_XACT_ID_FIELD,
          PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
          ...ObjectIdFields,
        ].includes(k),
      ),
    ),
    ...auditColumns,
  };
}

function makeAuditLogTables<D, T>(
  schema: { type: D; tables: Record<string, { columns: Record<string, T> }> },
  auditColumns: Record<string, T>,
): { type: D; tables: Record<string, { columns: Record<string, T> }> } {
  return {
    type: schema.type,
    tables: {
      ...Object.fromEntries(
        Object.entries(schema.tables).map(([name, columns]) => [
          name,
          { columns: makeAuditLogSchema(columns.columns, auditColumns) },
        ]),
      ),
    },
  };
}

const basePostgresColumns: PostgresTableDefinition["columns"] = {
  id: { path: ["id"], type: { type: "text" } },
  span_id: { path: ["span_id"], type: { type: "text" } },
  root_span_id: { path: ["root_span_id"], type: { type: "text" } },
  data: { path: ["data"], type: { type: "jsonb" } },
  _xact_id: { path: ["_xact_id"], type: { type: "bigint" } },
  _object_delete: { path: ["_object_delete"], type: { type: "boolean" } },
  created: { path: ["created"], type: { type: "text" } },
  tags: { path: ["data", "tags"], type: { type: "jsonb" } },
  [PAGINATION_CURSOR_XACT_ID_FIELD]: {
    path: [PAGINATION_CURSOR_XACT_ID_FIELD],
    type: { type: "bigint" },
  },
  [PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD]: {
    path: [PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD],
    type: { type: "text" },
  },
  is_root: { path: ["is_root"], type: { type: "boolean" } },
  // This is a total hack just to get the planner not to complain when we project this field.
  _pagination_key: {
    path: ["_xact_id"],
    type: { type: "bigint" },
  },
};

const commonLogPostgresColumns: PostgresTableDefinition["columns"] = {
  ...basePostgresColumns,
  project_id: { path: ["project_id"], type: { type: "text" } },
  input: { path: ["data", "inputs"], type: { type: "jsonb" } },
  output: { path: ["data", "output"], type: { type: "jsonb" } },
  expected: { path: ["data", "expected"], type: { type: "jsonb" } },
  error: { path: ["data", "error"], type: { type: "jsonb" } },
  scores: { path: ["scores"], type: { type: "jsonb" } },
  metadata: { path: ["data", "metadata"], type: { type: "jsonb" } },
  metrics: { path: ["data", "metrics"], type: { type: "jsonb" } },
  context: { path: ["data", "context"], type: { type: "jsonb" } },
  span_parents: { path: ["data", "span_parents"], type: { type: "jsonb" } },
  span_attributes: {
    path: ["data", "span_attributes"],
    type: { type: "jsonb" },
  },
  origin: { path: ["data", "origin"], type: { type: "jsonb" } },
};

const experimentPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...commonLogPostgresColumns,
    experiment_id: { path: ["experiment_id"], type: { type: "text" } },
    dataset_record_id: {
      path: ["data", "dataset_record_id"],
      type: { type: "text" },
    },
  },
};

const logsPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...commonLogPostgresColumns,
    org_id: { path: ["org_id"], type: { type: "text" } },
    log_id: { path: ["log_id"], type: { type: "text" } },
  },
};

const projectPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...experimentPostgresSchema.columns,
    ...logsPostgresSchema.columns,
  },
};

const datasetPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...basePostgresColumns,
    project_id: { path: ["project_id"], type: { type: "text" } },
    dataset_id: { path: ["dataset_id"], type: { type: "text" } },
    input: { path: ["data", "inputs"], type: { type: "jsonb" } },
    expected: { path: ["data", "output"], type: { type: "jsonb" } },
    metadata: { path: ["data", "metadata"], type: { type: "jsonb" } },
    origin: { path: ["data", "origin"], type: { type: "jsonb" } },
  },
};

const promptSessionPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...logsPostgresSchema.columns,
    prompt_session_id: { path: ["prompt_session_id"], type: { type: "text" } },
    project_id: { path: ["project_id"], type: { type: "text" } },
    log_id: { path: ["log_id"], type: { type: "text" } },
    prompt_session_data: {
      path: ["data", "prompt_session_data"],
      type: { type: "jsonb" },
    },
    prompt_data: { path: ["data", "prompt_data"], type: { type: "jsonb" } },
    function_data: { path: ["data", "function_data"], type: { type: "jsonb" } },
    function_type: { path: ["data", "function_type"], type: { type: "text" } },
    object_data: { path: ["data", "object_data"], type: { type: "jsonb" } },
    completion: { path: ["data", "completion"], type: { type: "jsonb" } },
  },
};

const promptPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...basePostgresColumns,
    org_id: { path: ["org_id"], type: { type: "text" } },
    project_id: { path: ["project_id"], type: { type: "text" } },
    log_id: { path: ["log_id"], type: { type: "text" } },
    name: { path: ["data", "name"], type: { type: "text" } },
    slug: { path: ["data", "slug"], type: { type: "text" } },
    description: { path: ["data", "description"], type: { type: "text" } },
    prompt_data: { path: ["data", "prompt_data"], type: { type: "jsonb" } },
    metadata: { path: ["data", "metadata"], type: { type: "jsonb" } },
    function_type: { path: ["data", "function_type"], type: { type: "text" } },
  },
};

// See the comment in the definition of functionDataSchema in app_types.ts. We should change
// this once we nest things properly
const functionPostgresSchema: PostgresTableDefinition = {
  columns: {
    ...promptPostgresSchema.columns,
    function_data: { path: ["data", "function_data"], type: { type: "jsonb" } },
    function_schema: {
      path: ["data", "function_schema"],
      type: { type: "jsonb" },
    },
    origin: { path: ["data", "origin"], type: { type: "jsonb" } },
  },
};

const POSTGRES_PHYSICAL_SCHEMA: PhysicalSchema = {
  type: "postgres",
  tables: {
    experiment: experimentPostgresSchema,
    dataset: datasetPostgresSchema,
    prompt_session: promptSessionPostgresSchema,
    playground_logs: experimentPostgresSchema,
    project_logs: logsPostgresSchema,
    project: projectPostgresSchema,
    project_prompts: promptPostgresSchema,
    org_prompts: promptPostgresSchema,
    project_functions: functionPostgresSchema,
    org_functions: functionPostgresSchema,
  },
};

export const PHYSICAL_SCHEMA = {
  postgres: {
    main: POSTGRES_PHYSICAL_SCHEMA,
    audit_log: makeAuditLogTables(
      POSTGRES_PHYSICAL_SCHEMA,
      auditLogBasePostgresColumns,
    ),
  },
};
