import {
  PAGINATION_CURSOR_XACT_ID_FIELD,
  PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
} from "./pagination_cursor";
import {
  crossObjectTypeSchema,
  LEAF_OBJECT_ID_FIELDS,
  ObjectType,
  objectTypeSchema,
  singleObjectTypeSchema,
} from "./schema";
import { ident, join, sql, ToSQL } from "@braintrust/btql/planner";
import { PG_COMMENTS_TABLE, PG_ORG_PROMPT_LOG_ROWS_TABLE } from "./env";
import {
  BadRequestError,
  InternalServerError,
  isEmpty,
  normalizeXact,
} from "./util";
import {
  CREATED_FIELD,
  OBJECT_DELETE_FIELD,
  TRANSACTION_ID_FIELD,
} from "@braintrust/core";
import { ObjectIdFields } from "@braintrust/local/api-schema";
import { makeObjectIdExprFromTable } from "./query_util";

export interface SearchParams {
  max_xact_id?: string;
  max_root_span_id?: string;
  limit?: number;
  filter?: ToSQL;
  sequenceIdFilter?: ToSQL;
}
function nonEmptySearchParams(params: SearchParams) {
  return (
    !isEmpty(params.max_xact_id) ||
    !isEmpty(params.limit) ||
    !isEmpty(params.filter)
  );
}

function makeObjectIdExprFromValues(
  objectColumnToId: Record<string, string>,
): ToSQL {
  if (Object.keys(objectColumnToId).length === 0) {
    throw new Error("Empty object columns");
  }

  return sql`make_object_id(${join(
    LEAF_OBJECT_ID_FIELDS.map((field) => {
      return objectColumnToId[field]
        ? sql`${objectColumnToId[field]}`
        : sql`null`;
    }),
    ", ",
  )})`;
}

function expandObjectIdFilters(
  objectType: ObjectType,
  objectIds: string[],
): Record<string, string[]> {
  if (objectType === "org_prompts" || objectType === "org_functions") {
    return { org_id: objectIds, log_id: ["p"] };
  } else if (objectType === "project") {
    return { project_id: objectIds };
  } else if (objectType === "project_logs") {
    return { project_id: objectIds, log_id: ["g"] };
  } else if (
    objectType === "project_prompts" ||
    objectType === "project_functions"
  ) {
    return { project_id: objectIds, log_id: ["p"] };
  } else if (objectType === "playground_logs") {
    return { prompt_session_id: objectIds.map((id) => `${id}:x`) };
  } else {
    objectTypeSchema.parse(objectType); // Equivalent to the BadRequestError check
    return { [objectType + "_id"]: objectIds };
  }
}

function genEmptyFunctionDataFilter(table: string): ToSQL {
  return sql`(jsonb_extract_path(${ident([
    table,
    "data",
  ])}, 'function_data') IS NULL OR jsonb_extract_path_text(${ident([
    table,
    "data",
  ])}, 'function_data', 'type') = 'prompt')`;
}

function genCrossObjectIdsFilter({
  table,
  objectColumn,
  objectIds,
}: {
  table: string;
  objectColumn: string;
  objectIds: string[];
}): ToSQL {
  const objectIdent = ident([table, objectColumn]);
  if (objectIds.length === 0) {
    return sql`false`;
  } else if (objectIds.length === 1) {
    return sql`${objectIdent} = ${objectIds[0]}`;
  } else {
    return sql`${objectIdent} IN (${join(
      objectIds.map((id) => sql`${id}`),
      ", ",
    )})`;
  }
}

function genSingleObjectIdsFilter({
  table,
  objectColumnToIds,
}: {
  table: string;
  objectColumnToIds: Record<string, string[]>;
}): ToSQL {
  if (Object.values(objectColumnToIds).some((ids) => ids.length === 0)) {
    return sql`false`;
  } else if (
    Object.values(objectColumnToIds).every((ids) => ids.length === 1)
  ) {
    const objectColumnToId = Object.fromEntries(
      Object.entries(objectColumnToIds).map(([objectColumn, ids]) => [
        objectColumn,
        ids[0],
      ]),
    );
    return sql`${makeObjectIdExprFromTable(
      table,
    )} = ${makeObjectIdExprFromValues(objectColumnToId)}`;
  } else {
    const exprs: ToSQL[] = [];
    // Compute the cartesian product over all sets of object ids.
    const expandedObjectColumnIds = Object.entries(objectColumnToIds).map(
      ([objectColumn, ids]) => ids.map((id) => ({ objectColumn, id })),
    );
    const allObjectColumnIdCombinations = expandedObjectColumnIds.reduce(
      (acc: Record<string, string>[], curr) => {
        return acc.flatMap((combo) =>
          curr.map((id) => ({ ...combo, [id.objectColumn]: id.id })),
        );
      },
      [{}],
    );
    for (const objectColumnToId of allObjectColumnIdCombinations) {
      exprs.push(makeObjectIdExprFromValues(objectColumnToId));
    }
    return sql`${makeObjectIdExprFromTable(table)} IN (${join(exprs, ", ")})`;
  }
}

export function genSingleObjectIdValue({
  objectType,
  objectIds,
}: {
  objectType: ObjectType;
  objectIds: string[];
}): ToSQL {
  const objectColumnToIds = expandObjectIdFilters(objectType, objectIds);

  if (!Object.values(objectColumnToIds).every((ids) => ids.length === 1)) {
    throw new BadRequestError("Multiple or missing object ids");
  }

  const objectColumnToId = Object.fromEntries(
    Object.entries(objectColumnToIds).map(([objectColumn, ids]) => [
      objectColumn,
      ids[0],
    ]),
  );

  return sql`${makeObjectIdExprFromValues(objectColumnToId)}`;
}

export function genObjectFilters({
  table,
  objectType,
  objectIds,
}: {
  table: string;
  objectType: ObjectType;
  objectIds: string[];
}): ToSQL {
  const objectColumnToIds = expandObjectIdFilters(objectType, objectIds);

  const objectFilters: ToSQL[] = [];
  if (crossObjectTypeSchema.safeParse(objectType).success) {
    for (const [objectColumn, objectIds] of Object.entries(objectColumnToIds)) {
      objectFilters.push(
        genCrossObjectIdsFilter({ table, objectColumn, objectIds }),
      );
    }
  } else {
    if (!singleObjectTypeSchema.safeParse(objectType).success) {
      throw new BadRequestError(`Bad object type ${objectType}`);
    }
    objectFilters.push(genSingleObjectIdsFilter({ table, objectColumnToIds }));
  }

  if (objectType === "project_prompts" || objectType === "org_prompts") {
    objectFilters.push(genEmptyFunctionDataFilter(table));
  }

  return join(objectFilters, " AND ");
}

function genOrgPromptIndexJoin({
  pgLogsTableName,
  objectType,
  objectIds,
}: {
  pgLogsTableName: string;
  objectType: ObjectType;
  objectIds: string[];
}): ToSQL {
  if (
    !["org_prompts", "org_functions"].includes(objectType) ||
    objectIds.length === 0
  ) {
    return sql``;
  }
  // NOTE: in practice this should only ever get used with PG_LOGS_TABLE. If it
  // gets used against PG_LOGS2_TABLE, then the join will not work.
  const logsTable = ident(pgLogsTableName);
  const orgPromptLogRowsTable = ident(PG_ORG_PROMPT_LOG_ROWS_TABLE);
  const orgIdCond = ((): ToSQL => {
    if (objectIds.length === 1) {
      return sql`${orgPromptLogRowsTable}.org_id = ${objectIds[0]}`;
    } else {
      return sql`${orgPromptLogRowsTable}.org_id IN (${join(
        objectIds.map((x) => sql`${x}`),
        ", ",
      )})`;
    }
  })();
  return sql`JOIN ${orgPromptLogRowsTable} ON ${orgIdCond} AND ${logsTable}.sequence_id = ${orgPromptLogRowsTable}.logs_sequence_id`;
}

export function scanObjectsQuery(args: {
  pgLogsTableName: string;
  objectType: ObjectType;
  objectIds: string[];
  version?: string;
  search?: SearchParams;
  auditLog?: boolean;
  keepJsonb?: boolean;
  usedFields?: Set<string>;
  relaxedSearchMode?: boolean;
}): ToSQL {
  const {
    pgLogsTableName,
    objectType,
    objectIds,
    version,
    search,
    auditLog = false,
    keepJsonb: keepJsonbArg = false,
    usedFields,
    relaxedSearchMode,
  } = args;

  const tableIndependentObjectFilters = [sql`TRUE`];
  const tableDependentObjectFilters: ((table: string) => ToSQL)[] = [];
  const makeAllObjectFilters = (table: string) =>
    tableIndependentObjectFilters.concat(
      tableDependentObjectFilters.map((f) => f(table)),
    );

  const logsTable = ident(pgLogsTableName);
  const commentsTable = ident(PG_COMMENTS_TABLE);
  const xactId = ident(TRANSACTION_ID_FIELD);
  const objectDelete = ident(OBJECT_DELETE_FIELD);
  const created = ident(CREATED_FIELD);
  const paginationCursorXactId = ident(PAGINATION_CURSOR_XACT_ID_FIELD);
  const paginationCursorRootSpanId = ident(
    PAGINATION_CURSOR_ROOT_SPAN_ID_FIELD,
  );

  tableDependentObjectFilters.push((table) =>
    genObjectFilters({ table, objectType, objectIds: objectIds }),
  );

  if (version) {
    const versionNum = normalizeXact(version);
    tableIndependentObjectFilters.push(sql`${xactId} <= ${versionNum}`);
  }

  const orgPromptLogRowsJoinCond = genOrgPromptIndexJoin({
    pgLogsTableName,
    objectType,
    objectIds,
  });

  let preamble = sql`WITH noop AS (SELECT 1)`;
  const allObjectFilters = makeAllObjectFilters(pgLogsTableName);
  const rowFilters = [...allObjectFilters];

  // DEVNOTE: Currently, search assumes that root_span_ids do not change across
  // versions. This is enforced in the `log_pg` function, which strips out
  // "root_span_id" before merging a new row into the existing one.
  if (search && nonEmptySearchParams(search) && !auditLog) {
    // There are three essential filters we use when obtaining the next page of
    // `candidate_root_spans`.
    //
    // 1. Filter out rows whose transaction ID is not the latest for that row.
    // This filter ensures we never return old versions of data.
    //
    // 2. Filter out deleted rows, so that if the set of candidate root spans is
    // nonempty, then the final result set will also be nonempty. This filter is
    // essential for the caller to be able to detect the end of the dataset when
    // the backend returns an empty page.
    //
    // 3. Filter out root spans we have previously returned, so that we don't
    // return the same row (or older versions of the same row) as we paginate.
    const candidateRootSpanFiltersUnordered = new Map<string, ToSQL[]>();

    if (!relaxedSearchMode) {
      candidateRootSpanFiltersUnordered.set("latestRowVersion", [
        sql`_xact_id = (
              SELECT MAX(${xactId})
              FROM ${logsTable} "_other_logs"
              WHERE
                  ${join(tableIndependentObjectFilters, " AND ")}
                  AND ${makeObjectIdExprFromTable(
                    "_other_logs",
                  )} = ${makeObjectIdExprFromTable(pgLogsTableName)}
                  AND "_other_logs".id = ${logsTable}.id
          )`,
      ]);
    }
    candidateRootSpanFiltersUnordered.set("objectDelete", [
      sql`NOT ${objectDelete}`,
    ]);

    const paginationTableExpr = sql`ROW(${xactId}, COALESCE(root_span_id, id))::_xact_id_root_span_id`;
    const paginationParamsExpr = !isEmpty(search.max_xact_id)
      ? sql`(ROW(${search.max_xact_id}, ${search.max_root_span_id})::_xact_id_root_span_id)`
      : // We noticed the postgres optimizer will sometimes use the wrong index
        // in the 'candidate_root_spans' subquery. By adding an extra filter to
        // match the index (which is at BIGINT_MAX), we seem to be able to push
        // it to use the right index. In the future, we may consider using
        // pg_hint_plan.
        sql`(ROW('9223372036854775807', '')::_xact_id_root_span_id)`;

    let addedGinObjectFilters = false;

    if (search.sequenceIdFilter) {
      candidateRootSpanFiltersUnordered.set("sequenceId", [
        search.sequenceIdFilter,
      ]);
    }

    const searchFilter: ToSQL | undefined = search.filter;
    if (searchFilter) {
      candidateRootSpanFiltersUnordered.set("search", [searchFilter]);

      // If we're going to use the gin index and we're only searching, for a
      // single object type and within that a single object ID, we can add a gin
      // lookup for the object ID.
      //
      // This is a big optimization, since it allows us to use the gin index for
      // everything. Unfortunately we will not be able to use indices for the
      // pagination aspect of the search.
      //
      // Note that unlike other cases where we're scanning across the object, we also
      // don't care about filtering out functions that are not prompts here.
      const hasGinLookup = "@>" in searchFilter.toNumericParamQuery();
      const expandedFilters = expandObjectIdFilters(objectType, objectIds);
      if (
        hasGinLookup &&
        Object.values(expandedFilters).every((ids) => ids.length === 1)
      ) {
        candidateRootSpanFiltersUnordered.set("object", [
          ...Object.entries(expandedFilters).map(
            ([objectColumn, objectIds]) =>
              sql`data @> ${JSON.stringify({ [objectColumn]: objectIds[0] })}`,
          ),
        ]);
        addedGinObjectFilters = true;
      }
    }

    if (!addedGinObjectFilters) {
      candidateRootSpanFiltersUnordered.set(
        "object",
        makeAllObjectFilters(pgLogsTableName),
      );
    }

    const outerLimit = isEmpty(search.limit)
      ? undefined
      : Math.min(Math.max(search.limit * 5, 10 + 10), 10000);
    const innerLimit = search.limit;

    // Create separate sets of filters for the "previous_candidate_root_spans"
    // and "candidate_root_spans" subqueries.
    const previousCandidateRootSpanFiltersUnordered = new Map<string, ToSQL[]>(
      candidateRootSpanFiltersUnordered.entries(),
    );

    if (paginationParamsExpr) {
      previousCandidateRootSpanFiltersUnordered.set("paginationCursor", [
        sql`${paginationTableExpr} >= ${paginationParamsExpr}`,
      ]);
      candidateRootSpanFiltersUnordered.set("paginationCursor", [
        sql`${paginationTableExpr} < ${paginationParamsExpr}`,
      ]);
      if (!relaxedSearchMode) {
        candidateRootSpanFiltersUnordered.set(
          "excludePreviousCandidateRootSpans",
          [
            sql`COALESCE(root_span_id, id) NOT IN (SELECT coalesced_root_span_id FROM previous_candidate_root_spans)`,
          ],
        );
      }
    }

    // Order filters from least-to-most likely expensive.
    const FILTER_ORDER = [
      "objectDelete",
      "paginationCursor",
      "sequenceId",
      "object",
      "search",
      "latestRowVersion",
      "excludePreviousCandidateRootSpans",
    ];

    const previousCandidateRootSpanFilters = FILTER_ORDER.flatMap(
      (f) => previousCandidateRootSpanFiltersUnordered.get(f) ?? [],
    );
    const candidateRootSpanFilters = FILTER_ORDER.flatMap(
      (f) => candidateRootSpanFiltersUnordered.get(f) ?? [],
    );

    const previousCandidateRootSpansExpr =
      paginationParamsExpr && !relaxedSearchMode
        ? sql`previous_candidate_root_spans AS (
            SELECT COALESCE(root_span_id, id) coalesced_root_span_id
            FROM ${logsTable} ${orgPromptLogRowsJoinCond}
            WHERE ${join(previousCandidateRootSpanFilters, " AND ")}
        ),`
        : sql``;

    preamble = sql`
          ${preamble},
          ${previousCandidateRootSpansExpr}
          candidate_root_spans AS (
              SELECT COALESCE(root_span_id, id) coalesced_root_span_id, ${xactId}
              FROM ${logsTable} ${orgPromptLogRowsJoinCond}
              WHERE ${join(candidateRootSpanFilters, " AND ")}
              ${
                !isEmpty(outerLimit)
                  ? sql`
                  ORDER BY ${paginationTableExpr} DESC
                  LIMIT ${outerLimit}
              `
                  : sql``
              }
          ),
          paginated_ids AS (
              SELECT coalesced_root_span_id, MAX(${xactId}) AS xact_id_max
              FROM candidate_root_spans
              GROUP BY coalesced_root_span_id
              ${
                !isEmpty(innerLimit)
                  ? sql`
                      ORDER BY xact_id_max DESC, coalesced_root_span_id DESC LIMIT ${innerLimit}
                  `
                  : sql``
              }
          ),
          -- Note that clickhouse (now unused) will attach ORDER BY to
          -- individual parts of the UNION ALL unless we wrap it in a subquery.
          pagination_cursor AS (
              SELECT * FROM (
                  SELECT xact_id_max ${paginationCursorXactId}, coalesced_root_span_id ${paginationCursorRootSpanId}
                  FROM paginated_ids
                  UNION ALL
                  SELECT null ${paginationCursorXactId}, null ${paginationCursorRootSpanId}
              ) "t"
              ORDER BY ${paginationCursorXactId} ASC NULLS LAST, ${paginationCursorRootSpanId} ASC NULLS LAST LIMIT 1
          )
      `;

    rowFilters.push(
      sql`COALESCE(root_span_id, id) IN (SELECT coalesced_root_span_id FROM paginated_ids)`,
    );
  } else {
    if (auditLog && search && nonEmptySearchParams(search)) {
      if (search.max_xact_id || search.max_root_span_id || search.limit) {
        // This should not happen, because we should have stripped these in the
        // caller.
        throw new InternalServerError(
          "Audit log queries do not support cursors or limits",
        );
      }
      if (search.filter) {
        rowFilters.push(search.filter);
      }
      if (search.sequenceIdFilter) {
        rowFilters.push(search.sequenceIdFilter);
      }
    }
    preamble = sql`
            ${preamble},
            pagination_cursor AS (
                SELECT null ${paginationCursorXactId}, null ${paginationCursorRootSpanId}
            )
        `;
  }

  const castText = (expr: ToSQL) => sql`(${expr})::text`;
  const keepJsonb = keepJsonbArg;
  const paginationCursorProjection = sql`
    , ${castText(
      sql`pagination_cursor.${paginationCursorXactId}`,
    )} AS ${paginationCursorXactId}
    , pagination_cursor.${paginationCursorRootSpanId}
  `;
  const paginationCursorJoin = sql` JOIN pagination_cursor ON true`;

  let baseQuery;
  if (auditLog) {
    const commentsWithCreated = sql`${commentsTable}.data || jsonb_build_object(${CREATED_FIELD}::text, ${commentsTable}.row_created::text)`;
    const createEntry = sql`
          (audit_data
          -- Build up the AuditLogRow (should be kept in sync with app/utils/schema.tsx)
          || jsonb_build_object(
              'origin', jsonb_build_object('id', log_table_entries.id, ${TRANSACTION_ID_FIELD}::text, log_table_entries.${xactId}::text)
          ))${keepJsonb ? sql`` : sql`::text`} AS data,
          CONCAT(log_table_entries.id, '_', log_table_entries.${xactId}) AS id,
          log_table_entries.object_id AS object_id,
          ${castText(xactId)} AS ${xactId},
          row_created::text AS ${created},
          ${join(
            ObjectIdFields.map((k) => sql`${ident(k)} AS ${ident(k)}`),
            ", ",
          )}
          `;

    const logTableEntriesSubquery = (() => {
      const logTableEntriesProjection = sql`
        sequence_id,
        id,
        ${makeObjectIdExprFromTable(pgLogsTableName)} object_id,
        row_created,
        ${xactId},
        ${sql`created`},
        ${join(
          ObjectIdFields.map((k) => sql`${k}, ${logsTable}.${ident(k)}`),
          ", ",
        )},
        audit_data
      `;
      return sql`
        , log_table_entries AS (
            SELECT ${logTableEntriesProjection}
            FROM ${logsTable} ${orgPromptLogRowsJoinCond}
            WHERE
                ${join(rowFilters, " AND ")}
        )
      `;
    })();

    baseQuery = sql`
        ${preamble}
        ${logTableEntriesSubquery}
        SELECT
          *
        FROM (
            SELECT DISTINCT ON (object_id, id)
                (${commentsWithCreated})${
                  keepJsonb ? sql`` : sql`::text`
                } AS data, -- Do not deserialize jsonb data
                ${commentsTable}.id,
                ${makeObjectIdExprFromTable("comments")} object_id,
                ${castText(sql`${commentsTable}.${xactId}`)} AS ${xactId},
                ${sql`created`},
                ${join(
                  ObjectIdFields.map((k) => sql`${ident(k)}`),
                  ", ",
                )},
                ${commentsTable}.${objectDelete}
                ${paginationCursorProjection}
            FROM
                ${commentsTable} ${paginationCursorJoin}
            WHERE
                -- This semi join is much faster than a full join, because you can have many versions of a single log table entry,
                -- and many comments on that entry, which results in an unnecessary (and sometimes crazy) explosion.
                (origin_id, ${makeObjectIdExprFromTable(
                  "comments",
                )}) IN (SELECT id, object_id FROM log_table_entries)
            ORDER BY object_id, id, ${xactId} DESC
        ) sub1
        WHERE
            NOT ${objectDelete}
        UNION ALL
        SELECT
            ${createEntry},
            false AS ${objectDelete}
            ${paginationCursorProjection}
        FROM
            log_table_entries
                ${paginationCursorJoin}
        WHERE
            audit_data IS NOT NULL
        `;
  } else {
    const projectData = !usedFields || usedFields.has("data");
    baseQuery = sql`
        ${preamble}
        SELECT
            *
        FROM (
            SELECT DISTINCT ON (object_id, id)
                ${
                  projectData
                    ? sql`data${keepJsonb ? sql`` : sql`::text`} AS data,`
                    : sql``
                }
                id,
                COALESCE(span_id, id) AS "span_id",
                COALESCE(root_span_id, id) AS "root_span_id",
                ${sql`(case when jsonb_typeof(jsonb_extract_path("data", 'span_parents')) = 'array' then jsonb_array_length(jsonb_extract_path("data", 'span_parents')) else 0 end) = 0 as is_root,`}
                ${castText(xactId)} AS ${xactId},
                ${objectDelete},
                scores,
                ${makeObjectIdExprFromTable(pgLogsTableName)} object_id,
                ${logsTable}.org_id,
                project_id,
                experiment_id,
                dataset_id,
                prompt_session_id,
                log_id,
                COALESCE(created, row_created::text) AS created
                ${paginationCursorProjection}
            FROM
                ${logsTable} AS ${ident(pgLogsTableName)}
                    ${orgPromptLogRowsJoinCond}
                    ${paginationCursorJoin}
            WHERE
                ${join(rowFilters, " AND ")}
            ORDER BY object_id, id, ${xactId} DESC
        ) sub1
        WHERE
            NOT ${objectDelete}
        `;
  }

  return baseQuery;
}
