#!/usr/bin/env python3
"""
Examples and usage patterns for the Braintrust Trace Extractor.

This file demonstrates various ways to use the trace extractor for different
analysis scenarios.
"""

import os
from trace_extractor import BraintrustTraceExtractor, TraceExtractionConfig


def example_basic_extraction():
    """Basic example: Extract a single trace by ID."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    trace_id = "your-trace-root-span-id"
    
    extractor = BraintrustTraceExtractor(api_key)
    
    # Extract with default configuration
    trace = extractor.extract_trace_by_id(project_id, trace_id)
    
    print("Extracted trace:")
    print(f"Root span: {trace.get('name', 'unnamed')}")
    print(f"Children: {len(trace.get('children', []))}")
    
    return trace


def example_custom_config():
    """Example with custom configuration for specific analysis needs."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    trace_id = "your-trace-root-span-id"
    
    # Create custom configuration
    config = TraceExtractionConfig(
        # Keep system messages for this analysis
        preserve_system_messages=True,
        
        # Don't strip Braintrust metadata - we want to see it
        strip_braintrust_metadata=False,
        
        # Only include LLM and tool spans
        include_span_types={"llm", "tool"},
        
        # Exclude scoring spans
        exclude_span_types={"score", "eval"}
    )
    
    extractor = BraintrustTraceExtractor(api_key)
    trace = extractor.extract_trace_by_id(project_id, trace_id, config=config)
    
    return trace


def example_batch_extraction():
    """Example: Extract multiple traces with filtering."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    
    extractor = BraintrustTraceExtractor(api_key)
    
    # Extract traces from the last 24 hours
    filter_expr = "created > '2024-01-01T00:00:00Z'"
    
    traces = extractor.extract_traces_by_filter(
        project_id=project_id,
        filter_expr=filter_expr,
        limit=20
    )
    
    print(f"Extracted {len(traces)} traces")
    
    # Analyze patterns
    llm_calls = 0
    tool_calls = 0
    
    def count_spans(span):
        nonlocal llm_calls, tool_calls
        if span.get('type') == 'llm':
            llm_calls += 1
        elif span.get('type') == 'tool':
            tool_calls += 1
        
        for child in span.get('children', []):
            count_spans(child)
    
    for trace in traces:
        count_spans(trace)
    
    print(f"Total LLM calls: {llm_calls}")
    print(f"Total tool calls: {tool_calls}")
    
    return traces


def example_claude_analysis_format():
    """Example: Format traces for Claude analysis."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    
    extractor = BraintrustTraceExtractor(api_key)
    
    # Extract some traces
    traces = extractor.extract_traces_by_filter(
        project_id=project_id,
        filter_expr="created > '2024-01-01'",
        limit=5
    )
    
    # Format for Claude analysis
    conversation_format = extractor.export_for_claude_analysis(traces, "conversation")
    minimal_format = extractor.export_for_claude_analysis(traces, "minimal")
    
    # Save different formats
    with open("traces_conversation.txt", "w") as f:
        f.write(conversation_format)
    
    with open("traces_minimal.json", "w") as f:
        f.write(minimal_format)
    
    print("Saved traces in conversation and minimal formats")
    print("\nConversation format preview:")
    print(conversation_format[:500] + "..." if len(conversation_format) > 500 else conversation_format)


def example_error_analysis():
    """Example: Extract traces with errors for debugging."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    
    # Configuration focused on error analysis
    config = TraceExtractionConfig(
        preserve_errors=True,
        preserve_llm_interactions=True,
        preserve_tool_calls=True,
        # Keep system messages to understand context
        preserve_system_messages=True,
        # Don't strip metadata - might be relevant for debugging
        strip_braintrust_metadata=False
    )
    
    extractor = BraintrustTraceExtractor(api_key)
    
    # Look for traces with errors
    traces = extractor.extract_traces_by_filter(
        project_id=project_id,
        filter_expr="error IS NOT NULL",
        limit=10,
        config=config
    )
    
    print(f"Found {len(traces)} traces with errors")
    
    # Analyze error patterns
    error_types = {}
    
    def analyze_errors(span):
        error = span.get('error')
        if error:
            error_type = type(error).__name__ if hasattr(error, '__class__') else str(error)[:50]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        for child in span.get('children', []):
            analyze_errors(child)
    
    for trace in traces:
        analyze_errors(trace)
    
    print("Error patterns:")
    for error_type, count in error_types.items():
        print(f"  {error_type}: {count}")
    
    return traces


def example_llm_conversation_analysis():
    """Example: Focus on LLM conversations for prompt analysis."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    
    # Configuration optimized for LLM conversation analysis
    config = TraceExtractionConfig(
        preserve_llm_interactions=True,
        preserve_user_messages=True,
        preserve_assistant_messages=True,
        preserve_system_messages=False,  # Skip system - often templates
        preserve_tool_calls=False,  # Focus only on conversations
        strip_braintrust_metadata=True,
        strip_app_templates=True,
        include_span_types={"llm"}  # Only LLM spans
    )
    
    extractor = BraintrustTraceExtractor(api_key)
    
    traces = extractor.extract_traces_by_filter(
        project_id=project_id,
        filter_expr="span_attributes.type = 'llm'",
        limit=15,
        config=config
    )
    
    # Extract just the conversations
    conversations = []
    
    def extract_conversations(span):
        if span.get('type') == 'llm':
            messages = span.get('messages', [])
            if messages:
                conversations.append({
                    'span_name': span.get('name'),
                    'messages': messages,
                    'response': span.get('response')
                })
        
        for child in span.get('children', []):
            extract_conversations(child)
    
    for trace in traces:
        extract_conversations(trace)
    
    print(f"Extracted {len(conversations)} LLM conversations")
    
    # Save for analysis
    import json
    with open("llm_conversations.json", "w") as f:
        json.dump(conversations, f, indent=2)
    
    return conversations


def example_tool_usage_analysis():
    """Example: Analyze tool usage patterns."""
    
    api_key = os.getenv('BRAINTRUST_API_KEY')
    project_id = "your-project-id"
    
    # Configuration for tool analysis
    config = TraceExtractionConfig(
        preserve_tool_calls=True,
        preserve_tool_inputs_outputs=True,
        preserve_llm_interactions=False,  # Focus on tools
        strip_braintrust_tools=True,  # Skip internal tools
        include_span_types={"tool"}
    )
    
    extractor = BraintrustTraceExtractor(api_key)
    
    traces = extractor.extract_traces_by_filter(
        project_id=project_id,
        filter_expr="span_attributes.type = 'tool'",
        limit=20,
        config=config
    )
    
    # Analyze tool usage
    tool_stats = {}
    
    def analyze_tools(span):
        if span.get('type') == 'tool':
            tool_name = span.get('tool_name', 'unknown')
            if tool_name not in tool_stats:
                tool_stats[tool_name] = {
                    'count': 0,
                    'success_count': 0,
                    'error_count': 0
                }
            
            tool_stats[tool_name]['count'] += 1
            
            if span.get('error'):
                tool_stats[tool_name]['error_count'] += 1
            else:
                tool_stats[tool_name]['success_count'] += 1
        
        for child in span.get('children', []):
            analyze_tools(child)
    
    for trace in traces:
        analyze_tools(trace)
    
    print("Tool usage statistics:")
    for tool_name, stats in tool_stats.items():
        success_rate = stats['success_count'] / stats['count'] * 100 if stats['count'] > 0 else 0
        print(f"  {tool_name}: {stats['count']} calls, {success_rate:.1f}% success rate")
    
    return tool_stats


def example_cli_usage():
    """Examples of using the CLI interface."""
    
    print("CLI Usage Examples:")
    print()
    
    print("1. Extract a single trace:")
    print("   python trace_extractor.py PROJECT_ID --trace-id TRACE_ID")
    print()
    
    print("2. Extract recent traces:")
    print("   python trace_extractor.py PROJECT_ID --filter \"created > '2024-01-01'\" --limit 10")
    print()
    
    print("3. Export in conversation format:")
    print("   python trace_extractor.py PROJECT_ID --trace-id TRACE_ID --format conversation")
    print()
    
    print("4. Save to file with custom options:")
    print("   python trace_extractor.py PROJECT_ID --filter \"error IS NOT NULL\" \\")
    print("     --format minimal --output error_traces.json --preserve-system")
    print()
    
    print("5. Include Braintrust internal tools:")
    print("   python trace_extractor.py PROJECT_ID --trace-id TRACE_ID \\")
    print("     --include-braintrust-tools --format structured")


if __name__ == '__main__':
    print("Braintrust Trace Extractor Examples")
    print("===================================")
    print()
    
    # Check if API key is set
    if not os.getenv('BRAINTRUST_API_KEY'):
        print("⚠️  Set BRAINTRUST_API_KEY environment variable to run examples")
        print()
    
    example_cli_usage()
    
    print()
    print("Run individual example functions to see them in action:")
    print("- example_basic_extraction()")
    print("- example_custom_config()")
    print("- example_batch_extraction()")
    print("- example_claude_analysis_format()")
    print("- example_error_analysis()")
    print("- example_llm_conversation_analysis()")
    print("- example_tool_usage_analysis()")
