use std::collections::HashMap;
use std::sync::OnceLock;
use std::time::Duration;

use opentelemetry_sdk::resource::{Resource, ResourceDetector};
use opentelemetry_semantic_conventions::resource as semconv;
use otel_common::opentelemetry::KeyValue;
use serde::Deserialize;

#[derive(Deserialize, Clone)]
struct Identity {
    #[serde(rename = "region")]
    region: String,
    #[serde(rename = "accountId")]
    account_id: String,
    #[serde(rename = "availabilityZone")]
    az: String,
    #[serde(rename = "instanceId")]
    instance_id: String,
    #[serde(rename = "instanceType")]
    instance_type: String,
    #[serde(rename = "imageId")]
    image_id: String,
}

#[derive(Clone)]
struct Ec2Metadata {
    identity: Identity,
    tags: HashMap<String, String>,
}

static EC2_METADATA: OnceLock<Option<Ec2Metadata>> = OnceLock::new();

pub struct Ec2ResourceDetector;

impl ResourceDetector for Ec2ResourceDetector {
    fn detect(&self) -> Resource {
        // This can be called from async code, so just spin up a new thread
        // to check EC2 stuff and cache the result.
        let metadata = EC2_METADATA.get_or_init(|| {
            // Create a dedicated runtime for IMDS calls
            std::thread::spawn(|| {
                let rt = util::tokio::runtime::Builder::new_current_thread()
                    .enable_io()
                    .enable_time()
                    .build()
                    .ok()?;

                rt.block_on(async {
                    let client = aws_config::imds::Client::builder()
                        .connect_timeout(Duration::from_millis(200))
                        .read_timeout(Duration::from_millis(200))
                        .build();

                    // The identity document is a single JSON blob
                    let identity = match client
                        .get("/latest/dynamic/instance-identity/document")
                        .await
                    {
                        Ok(text) => match serde_json::from_str::<Identity>(text.as_ref()) {
                            Ok(doc) => Some(doc),
                            Err(e) => {
                                tracing::warn!("Failed to parse IMDS document: {:?}", e);
                                return None;
                            }
                        },
                        Err(e) => {
                            tracing::debug!("IMDS not available: {:?}", e);
                            return None;
                        }
                    };

                    // Get all available tags
                    let mut tags = HashMap::new();

                    // First, get the list of available tag keys
                    match client.get("/latest/meta-data/tags/instance").await {
                        Ok(tag_keys_text) => {
                            let tag_keys: Vec<&str> = tag_keys_text.as_ref().lines().collect();

                            // Fetch each tag value
                            for tag_key in tag_keys {
                                match client
                                    .get(&format!("/latest/meta-data/tags/instance/{}", tag_key))
                                    .await
                                {
                                    Ok(tag_value) => {
                                        tags.insert(
                                            tag_key.to_string(),
                                            tag_value.as_ref().to_string(),
                                        );
                                    }
                                    Err(e) => {
                                        tracing::debug!("Failed to get tag {}: {:?}", tag_key, e);
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            tracing::debug!("Instance tags not available: {:?}", e);
                        }
                    }

                    identity.map(|identity| Ec2Metadata { identity, tags })
                })
            })
            .join()
            .unwrap_or_default()
        });

        match metadata {
            Some(ec2_metadata) => {
                let mut attributes = vec![
                    KeyValue::new(semconv::CLOUD_PROVIDER, "aws"),
                    KeyValue::new(semconv::CLOUD_PLATFORM, "aws_ec2"),
                    KeyValue::new(semconv::CLOUD_REGION, ec2_metadata.identity.region.clone()),
                    KeyValue::new(
                        semconv::CLOUD_AVAILABILITY_ZONE,
                        ec2_metadata.identity.az.clone(),
                    ),
                    KeyValue::new(
                        semconv::CLOUD_ACCOUNT_ID,
                        ec2_metadata.identity.account_id.clone(),
                    ),
                    KeyValue::new(semconv::HOST_ID, ec2_metadata.identity.instance_id.clone()),
                    KeyValue::new(
                        semconv::HOST_TYPE,
                        ec2_metadata.identity.instance_type.clone(),
                    ),
                    KeyValue::new(
                        semconv::HOST_IMAGE_ID,
                        ec2_metadata.identity.image_id.clone(),
                    ),
                ];

                // Add all instance tags as attributes
                for (tag_key, tag_value) in &ec2_metadata.tags {
                    // Use semantic convention for well-known tags, otherwise use custom attribute
                    match tag_key.as_str() {
                        "Name" => {
                            attributes.push(KeyValue::new(semconv::HOST_NAME, tag_value.clone()));
                        }
                        _ => {
                            // Use a custom attribute with "aws.ec2.tag." prefix for other tags
                            let attribute_key = format!("aws.ec2.tag.{}", tag_key.to_lowercase());
                            attributes.push(KeyValue::new(attribute_key, tag_value.clone()));
                        }
                    }
                }

                Resource::builder_empty()
                    .with_attributes(attributes)
                    .build()
            }
            None => Resource::builder_empty().build(),
        }
    }
}
