use std::time::Duration;

use otel_common::{
    collector::Collector, opentelemetry::metrics::Meter, system_metrics::SystemMetricsCollector,
};

use util::tokio;

pub async fn run_metrics_loop(
    meter: Meter,
    interval_seconds: u64,
    mut extra_collectors: Vec<Box<dyn Collector>>,
) {
    let mut interval = tokio::time::interval(Duration::from_secs(interval_seconds));
    interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);

    // Start comprehensive system metrics collection with the same interval
    let mut system_metrics_collector = SystemMetricsCollector::new(&meter);

    loop {
        let mut futures = Vec::new();
        futures.push(system_metrics_collector.collect());
        futures.extend(extra_collectors.iter_mut().map(|c| c.collect()));

        for result in util::futures::future::join_all(futures).await {
            if let Err(e) = result {
                tracing::error!("Error in metrics loop: {:?}", e);
            }
        }

        interval.tick().await;
    }
}
