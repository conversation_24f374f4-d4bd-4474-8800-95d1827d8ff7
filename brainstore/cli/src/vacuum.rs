use std::sync::Arc;

use crate::base::{
    self, parse_vacuum_object_id_args, AppState, BaseArgs, CLIArgs, VacuumObjectIdArgs,
    VacuumOptions,
};
use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::vacuum_index::{
    vacuum_index, vacuum_index_stateless, VacuumIndexInput, VacuumIndexOptionalInput,
    VacuumIndexOptions,
};
use storage::vacuum_segment_wal::{
    vacuum_segment_wal, VacuumSegmentWalInput, VacuumSegmentWalOptions,
};
use tracing::instrument;
use util::{
    anyhow::{anyhow, Result},
    system_types::{FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum VacuumCommands {
    IndexStateless(CLIArgs<VacuumIndexStatelessFullArgs>),
    Index(CLIArgs<VacuumIndexFullArgs>),
    SegmentWal(CLIArgs<VacuumSegmentWalArgs>),
}

pub fn base_args(args: &VacuumCommands) -> &BaseArgs {
    match args {
        VacuumCommands::IndexStateless(a) => &a.base,
        VacuumCommands::Index(a) => &a.base,
        VacuumCommands::SegmentWal(a) => &a.base,
    }
}

pub async fn main(args: VacuumCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        VacuumCommands::IndexStateless(a) => run_vacuum_index_stateless(app_state, a.args).await,
        VacuumCommands::Index(a) => run_vacuum_index(app_state, a.args).await,
        VacuumCommands::SegmentWal(a) => run_vacuum_segment_wal(app_state, a.args).await,
    }
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumSegmentWalArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub segment_id_args: base::SegmentIdArgs,

    #[arg(long, default_value_t = false)]
    #[serde(default)]
    pub dry_run: bool,

    #[command(flatten)]
    #[serde(default)]
    pub options: VacuumSegmentWalOptions,
}

#[instrument(err, name = "vacuum_segment_wal", skip(app_state))]
pub async fn run_vacuum_segment_wal(
    app_state: Arc<AppState>,
    args: VacuumSegmentWalArgs,
) -> Result<util::Value> {
    let segment_ids = args
        .segment_id_args
        .segment_ids(app_state.config.global_store.as_ref())
        .await?;

    if segment_ids.is_empty() {
        return Err(anyhow!("No segment IDs found to vacuum"));
    }

    let output = vacuum_segment_wal(
        VacuumSegmentWalInput {
            segment_ids: segment_ids.clone(),
            index_store: app_state.config.index.clone(),
            global_store: app_state.config.global_store.clone(),
            locks_manager: app_state.config.locks_manager.as_ref(),
            dry_run: args.dry_run,
        },
        args.options,
    )
    .await?;

    Ok(serde_json::json!(output).into())
}

// VacuumIndexStatelessArgs is the same as VacuumIndexArgs, but with legacy field names
// instead of the new `vacuum_`-prefixed ones. We retain the old field names here so that
// users don't have to update their stateless vacuum commands.
#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexStatelessObjectIdArgs {
    #[arg(help = "Object IDs to vacuum", env = "BRAINSTORE_VACUUM_OBJECT_IDS")]
    #[serde(default)]
    pub object_ids: Vec<String>,

    #[arg(
        long,
        help = "Vacuum all objects",
        env = "BRAINSTORE_VACUUM_OBJECT_ALL"
    )]
    #[serde(default)]
    pub all: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumIndexStatelessArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumIndexStatelessObjectIdArgs,

    #[arg(
        long,
        help = "Segment ID cursor, as returned by a previous partial run. Pass this to resume from the last successfully processed segment ID",
        env = "BRAINSTORE_VACUUM_INDEX_SEGMENT_ID_CURSOR"
    )]
    #[serde(default)]
    pub segment_id_cursor: Option<Uuid>,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumIndexStatelessFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: VacuumIndexStatelessArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: VacuumIndexOptions,
}

#[instrument(err, skip(app_state))]
pub async fn run_vacuum_index_stateless(
    app_state: Arc<AppState>,
    full_args: VacuumIndexStatelessFullArgs,
) -> Result<util::Value> {
    if full_args.args.object_id_args.all && !full_args.args.object_id_args.object_ids.is_empty() {
        util::anyhow::bail!("Cannot specify object IDs and also specify --all");
    }

    let object_ids: Option<Vec<FullObjectIdOwned>> = if full_args.args.object_id_args.all {
        None
    } else {
        Some(
            full_args
                .args
                .object_id_args
                .object_ids
                .into_iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let output = vacuum_index_stateless(
        VacuumIndexInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: app_state.config.index.clone(),
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.clone(),
            dry_run: full_args.args.dry_run,
        },
        VacuumIndexOptionalInput {
            segment_id_cursor: full_args.args.segment_id_cursor,
            ..Default::default()
        },
        full_args.options,
    )
    .await;

    Ok(json!(output))
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: VacuumIndexArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: VacuumIndexOptions,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumObjectIdArgs,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

pub async fn run_vacuum_index(
    app_state: Arc<AppState>,
    full_args: VacuumIndexFullArgs,
) -> Result<util::Value> {
    let object_ids = parse_vacuum_object_id_args(full_args.args.object_id_args)?;
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let output = vacuum_index(
        VacuumIndexInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: app_state.config.index.clone(),
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.clone(),
            dry_run: full_args.args.dry_run,
        },
        VacuumIndexOptionalInput::default(),
        full_args.options,
    )
    .await?;

    Ok(json!(output))
}

pub async fn vacuum_index_worker_task(app_state: Arc<AppState>, args: VacuumOptions) {
    let full_args = VacuumIndexFullArgs {
        args: VacuumIndexArgs {
            object_id_args: args.object_id_args.clone(),
            dry_run: args.vacuum_dry_run,
        },
        options: args.vacuum_index_options.clone(),
    };

    log::info!("Starting vacuum-index worker");

    let mut iterations = 0;
    loop {
        log::debug!("Running vacuum-index loop (iter {})", iterations);

        match run_vacuum_index(app_state.clone(), full_args.clone()).await {
            Ok(_) => {}
            Err(e) => {
                log::warn!("Error running vacuum-index: {:?}", e);
            }
        };

        if let Some(max_iterations) = args
            .background_vacuum_options
            .background_vacuum_max_iterations
        {
            iterations += 1;
            if iterations >= max_iterations {
                log::info!(
                    "Reached maximum number of iterations ({}), exiting vacuum-index loop",
                    max_iterations
                );
                break;
            }
        }

        log::debug!(
            "Sleeping for {} seconds before next vacuum_index iteration",
            args.background_vacuum_options
                .background_vacuum_sleep_seconds
        );

        tokio::time::sleep(std::time::Duration::from_secs(
            args.background_vacuum_options
                .background_vacuum_sleep_seconds,
        ))
        .await;
    }
}
