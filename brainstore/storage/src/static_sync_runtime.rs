use once_cell::sync::Lazy;
use util::tokio;

pub const MAX_STATIC_THREADS: usize = 1024;

// Since the ObjectStore interface is async, directory operations that run inside synchronous
// tantivy functions must be wrapped inside `tokio::runtime::Runtime::block_on`. While we could
// explicitly provide a runtime to each synchronous API, this becomes tricky because the runtime must be
// separate from the main event loop runtime (so that we don't block the main event loop), and it
// must be dropped inside a synchronous context.
//
// To make life simple, we use a lazily-initialized static runtime that is shared across all
// such synchronous APIs in the program. This runtime should be destroyed at the end of
// the program, which is synchronous.
//
// Tokio packages in 512 threads background "blocking" worker threads by default, but when doing
// lots of reads and writes, we might want more, so this is set to 1024. It's also nice to be explicit
// about the value in case tokio changes.
pub static STATIC_SYNC_RUNTIME: Lazy<tokio::runtime::Runtime> = Lazy::new(|| {
    tokio::runtime::Builder::new_multi_thread()
        .max_blocking_threads(MAX_STATIC_THREADS)
        .enable_all()
        .build()
        .unwrap()
});

pub fn drop_in_background<T>(value: T)
where
    T: Send + 'static,
{
    STATIC_SYNC_RUNTIME.spawn_blocking(move || drop(value));
}

#[derive(Debug, Clone)]
pub struct StaticRuntimeMetrics {
    pub num_workers: usize,
    pub num_alive_tasks: usize,
    pub num_blocking_threads: usize,
    pub num_idle_blocking_threads: usize,
    pub blocking_queue_depth: usize,
}

pub fn get_static_runtime_metrics() -> StaticRuntimeMetrics {
    let metrics = STATIC_SYNC_RUNTIME.metrics();

    StaticRuntimeMetrics {
        num_workers: metrics.num_workers(),
        num_alive_tasks: metrics.num_alive_tasks(),
        num_blocking_threads: metrics.num_blocking_threads(),
        num_idle_blocking_threads: metrics.num_idle_blocking_threads(),
        blocking_queue_depth: metrics.blocking_queue_depth(),
    }
}
