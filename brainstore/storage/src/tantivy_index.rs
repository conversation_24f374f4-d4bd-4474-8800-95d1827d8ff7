use std::{
    collections::{BTreeMap, HashMap, HashSet},
    path::{Path, PathBuf},
};

use clap::Parser;
use object_store::ObjectStore;
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use tantivy::{
    columnar::{Column, StrColumn},
    indexer::NoMergePolicy,
    schema::{JsonObjectOptions, OwnedValue, TextOptions, STRING, TEXT},
    DocId, SegmentComponent, SegmentId, SegmentReader, TantivyDocument,
};
use tracing::{instrument, Instrument};
use util::{
    anyhow::{anyhow, bail, Result},
    chrono::{self, DateTime, Utc},
    futures::future::join_all,
    json::set_value_at_path,
    spawn_blocking_util::spawn_blocking_with_async_timeout,
    uuid::Uuid,
    xact::PaginationKey,
};

use crate::{
    config_with_store::StoreInfo,
    directory::{
        async_directory::AsyncDirectory, deferred_ops::DeferredOpsDirectory,
        prefix_directory::PrefixDirectory, AsyncDirectoryArc,
    },
    error::StorageError,
    json_value_store::{read_json_value, write_json_value},
    limits::{default_index_threads_per_operation, default_memory_budget_per_indexing_operation},
    merge_policies::ExactNumberMergePolicy,
    paths::make_segment_directory_path,
    static_sync_runtime::STATIC_SYNC_RUNTIME,
};

#[derive(Debug, Clone)]
pub enum TantivyIndexScope {
    Segment(Uuid),
}

impl TantivyIndexScope {
    pub fn path(&self, store_prefix: &Path) -> PathBuf {
        match self {
            TantivyIndexScope::Segment(segment) => {
                make_segment_directory_path(store_prefix, *segment).join("tantivy")
            }
        }
    }

    pub fn lock_name(&self) -> String {
        match self {
            TantivyIndexScope::Segment(segment) => format!("segment_index_lock_{}", segment),
        }
    }
}

pub struct WritableTantivySchema {
    pub schema: tantivy::schema::Schema,
    pub fields: Vec<Box<dyn FieldAdder>>,
    pub invert_fields: Vec<Box<dyn FieldAdder>>,
}

pub fn make_tantivy_schema(schema: &util::schema::Schema) -> Result<WritableTantivySchema> {
    let mut schema_builder = tantivy::schema::Schema::builder();

    // Add tantivy fields in order of their field_ts. Then collect them into a hashmap of
    // (toplevel_idx, tantivy_idx) -> Field.
    let mut tantivy_fields_to_add: Vec<_> = schema
        .fields()
        .iter()
        .enumerate()
        .flat_map(|(toplevel_idx, field)| {
            field
                .tantivy
                .iter()
                .enumerate()
                .map(move |(tantivy_idx, tantivy_field)| {
                    ((toplevel_idx, tantivy_idx), tantivy_field)
                })
        })
        .collect();
    tantivy_fields_to_add.sort_by_key(|(_, tantivy_field)| tantivy_field.field_ts);
    let idx_to_added_tantivy_field = tantivy_fields_to_add
        .into_iter()
        .map(|(key, field_to_add)| Ok((key, add_tantivy_field(&mut schema_builder, field_to_add)?)))
        .collect::<Result<HashMap<_, _>>>()?;

    let mut fields: Vec<Box<dyn FieldAdder>> = Vec::new();
    let mut invert_fields: Vec<Box<dyn FieldAdder>> = Vec::new();
    for (toplevel_idx, (field, invert_tantivy_field_idx)) in schema
        .fields()
        .iter()
        .zip(schema.invert_tantivy_field_indices().iter())
        .enumerate()
    {
        for (tantivy_idx, tantivy_field_def) in field.tantivy.iter().enumerate() {
            let tantivy_field = idx_to_added_tantivy_field[&(toplevel_idx, tantivy_idx)];
            let path_field_adder = PathFieldAdder {
                path: vec![field.name.clone()],
                field: tantivy_field,
                field_type: tantivy_field_def.field_type.clone(),
                repeated: tantivy_field_def.repeated,
            };
            if tantivy_idx == *invert_tantivy_field_idx {
                invert_fields.push(Box::new(path_field_adder.clone()));
            }
            fields.push(Box::new(path_field_adder));
        }
    }

    let schema = schema_builder.build();

    Ok(WritableTantivySchema {
        schema,
        fields,
        invert_fields,
    })
}

fn add_tantivy_field(
    schema_builder: &mut tantivy::schema::SchemaBuilder,
    field: &util::schema::TantivyField,
) -> Result<tantivy::schema::Field> {
    use util::schema::TantivyType::*;
    let ret = match &field.field_type {
        Str(opts) => {
            let mut text_opts: TextOptions = match opts.tokenize {
                true => TEXT,
                false => STRING,
            };
            if opts.stored {
                text_opts = text_opts.set_stored();
            }
            if opts.fast {
                text_opts = text_opts.set_fast(None);
            }
            schema_builder.add_text_field(&field.name, text_opts)
        }
        U64(opts) => {
            let mut numeric_opts = tantivy::schema::NumericOptions::default().set_indexed();
            if opts.stored {
                numeric_opts = numeric_opts.set_stored();
            }
            if opts.fast {
                numeric_opts = numeric_opts.set_fast();
            }
            schema_builder.add_u64_field(&field.name, numeric_opts)
        }
        I64(opts) => {
            let mut numeric_opts = tantivy::schema::NumericOptions::default().set_indexed();
            if opts.stored {
                numeric_opts = numeric_opts.set_stored();
            }
            if opts.fast {
                numeric_opts = numeric_opts.set_fast();
            }
            schema_builder.add_i64_field(&field.name, numeric_opts)
        }
        F64(opts) => {
            let mut numeric_opts = tantivy::schema::NumericOptions::default().set_indexed();
            if opts.stored {
                numeric_opts = numeric_opts.set_stored();
            }
            if opts.fast {
                numeric_opts = numeric_opts.set_fast();
            }
            schema_builder.add_f64_field(&field.name, numeric_opts)
        }
        Bool(opts) => {
            let mut bool_opts = tantivy::schema::NumericOptions::default().set_indexed();
            if opts.stored {
                bool_opts = bool_opts.set_stored();
            }
            if opts.fast {
                bool_opts = bool_opts.set_fast();
            }
            schema_builder.add_bool_field(&field.name, bool_opts)
        }
        Date(opts) => {
            let mut date_opts = tantivy::schema::DateOptions::default()
                .set_indexed()
                .set_precision(tantivy::DateTimePrecision::Nanoseconds);
            if opts.stored {
                date_opts = date_opts.set_stored();
            }
            if opts.fast {
                date_opts = date_opts.set_fast();
            }
            schema_builder.add_date_field(&field.name, date_opts)
        }
        Facet(opts) => {
            let mut facet_opts = tantivy::schema::FacetOptions::default();
            if opts.stored {
                facet_opts = facet_opts.set_stored();
            }
            schema_builder.add_facet_field(&field.name, facet_opts)
        }
        Bytes(opts) => {
            let mut bytes_opts = tantivy::schema::BytesOptions::default().set_indexed();
            if opts.stored {
                bytes_opts = bytes_opts.set_stored();
            }
            if opts.fast {
                bytes_opts = bytes_opts.set_fast();
            }
            schema_builder.add_bytes_field(&field.name, bytes_opts)
        }
        Json(opts) => {
            let mut json_opts: JsonObjectOptions = match opts.tokenize {
                true => TEXT.into(),
                false => STRING.into(),
            };
            if opts.stored {
                json_opts = json_opts.set_stored();
            }
            if opts.fast {
                json_opts = json_opts.set_fast(None);
            }
            schema_builder.add_json_field(&field.name, json_opts)
        }
        IpAddr(opts) => {
            let mut ip_opts = tantivy::schema::IpAddrOptions::default().set_indexed();
            if opts.stored {
                ip_opts = ip_opts.set_stored();
            }
            if opts.fast {
                ip_opts = ip_opts.set_fast();
            }
            schema_builder.add_ip_addr_field(&field.name, ip_opts)
        }
    };
    Ok(ret)
}

pub fn make_document(
    value: &serde_json::Value,
    fields: &[Box<dyn FieldAdder>],
) -> Result<TantivyDocument> {
    let mut doc = TantivyDocument::default();
    for field in fields {
        field.add_field(&mut doc, value)?;
    }
    Ok(doc)
}

pub fn invert_document(
    doc: &tantivy::TantivyDocument,
    invert_fields: &[Box<dyn FieldAdder>],
) -> Result<serde_json::Value> {
    let mut object = serde_json::Value::Object(serde_json::Map::new());
    for field in invert_fields {
        let json_value = field.extract_field(doc)?;

        match (json_value, field.is_repeated()) {
            (Some(serde_json::Value::Array(vec)), true) => {
                set_value_at_path(&mut object, field.path(), serde_json::Value::Array(vec))?;
            }
            (None | Some(serde_json::Value::Null), true) => {
                set_value_at_path(&mut object, field.path(), serde_json::Value::Array(vec![]))?;
            }
            (Some(v), true) => {
                set_value_at_path(&mut object, field.path(), serde_json::Value::Array(vec![v]))?;
            }
            (Some(json_value), false) => {
                set_value_at_path(&mut object, field.path(), json_value)?;
            }
            (None, _) => {}
        }
    }
    Ok(object)
}

pub fn make_prefix_directory(
    base_directory: AsyncDirectoryArc,
    index_scope: &TantivyIndexScope,
    store_prefix: &Path,
) -> AsyncDirectoryArc {
    AsyncDirectoryArc::new(PrefixDirectory {
        base_directory,
        prefix: index_scope.path(store_prefix),
    })
}

pub fn make_tantivy_index_blocking(
    base_directory: AsyncDirectoryArc,
    schema: tantivy::schema::Schema,
    store_prefix: &Path,
    index_scope: &TantivyIndexScope,
) -> Result<tantivy::Index> {
    let settings = tantivy::IndexSettings {
        docstore_compression: tantivy::store::Compressor::Zstd(tantivy::store::ZstdCompressor {
            compression_level: None, // Defaults to 3
        }),
        ..Default::default()
    };
    let dir = Box::new(DeferredOpsDirectory(make_prefix_directory(
        base_directory,
        index_scope,
        store_prefix,
    )));
    let ret = tantivy::IndexBuilder::new()
        .schema(schema)
        .settings(settings)
        .open_or_create(dir as Box<dyn tantivy::Directory>)?;
    Ok(ret)
}

pub fn make_reader_blocking(index: &tantivy::Index) -> Result<tantivy::IndexReader> {
    let builder = index
        .reader_builder()
        .reload_policy(tantivy::ReloadPolicy::Manual);
    let start = std::time::Instant::now();
    let reader = builder.try_into()?;
    if log::log_enabled!(log::Level::Debug) {
        log::debug!("initializing tanitvy reader took {:?}", start.elapsed());
    }
    Ok(reader)
}

pub fn directory_to_reader_blocking<T: tantivy::Directory>(
    directory: T,
) -> Result<tantivy::IndexReader> {
    let index = tantivy::Index::open(directory)?;
    make_reader_blocking(&index)
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct TantivyIndexWriterOpts {
    // Anecdotally, this scales with the number of segments, rather than CPUs,
    // so 64 seems to work pretty well (with some quick benchmarks).
    #[arg(
        long,
        default_value_t = default_index_threads_per_operation(),
        help = "The number of threads to use for each indexing operation. This roughly corresponds to the number of chunks that will be produced while compacting.",
        env = "BRAINSTORE_NUM_THREADS_PER_INDEX_OPERATION"
    )]
    #[serde(default = "default_index_threads_per_operation")]
    pub num_writer_threads: usize,

    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_memory_budget_per_indexing_operation(),
        help = format!(
            "The memory budget per indexing operation. This is shared across threads and should be pretty high (ideally the machine's memory divided by # of indexing operations). Default: {}",
            util::ByteSize::from(default_memory_budget_per_indexing_operation())
        ),
        env = "BRAINSTORE_MEMORY_BUDGET_PER_INDEX_OPERATION"
    )]
    #[serde(default = "default_memory_budget_per_indexing_operation")]
    pub memory_budget_per_indexing_operation: usize,

    #[arg(
        long,
        default_value_t = default_index_batch_size(),
        help = "The number of documents to index into a single indexing operation (not per commit).",
        env = "BRAINSTORE_INDEX_BATCH_SIZE"
    )]
    #[serde(default = "default_index_batch_size")]
    pub index_batch_size: usize,

    #[arg(
        long,
        help = "Force no merges while writing to the index. This is useful for testing/debugging, but may result in a large number of segments.",
        env = "BRAINSTORE_INDEX_WRITER_FORCE_NO_MERGES"
    )]
    pub index_writer_force_no_merges: bool,

    #[command(flatten)]
    pub validate_opts: ValidateTantivyIndexOptions,
}

impl Default for TantivyIndexWriterOpts {
    fn default() -> Self {
        TantivyIndexWriterOpts {
            num_writer_threads: default_index_threads_per_operation(),
            memory_budget_per_indexing_operation: default_memory_budget_per_indexing_operation(),
            index_batch_size: default_index_batch_size(),
            index_writer_force_no_merges: false,
            validate_opts: ValidateTantivyIndexOptions::default(),
        }
    }
}

fn default_index_batch_size() -> usize {
    1000
}

impl TantivyIndexWriterOpts {
    pub fn memory_budget_in_bytes(&self) -> usize {
        self.memory_budget_per_indexing_operation
    }
}

pub fn make_writer_blocking(
    index: &tantivy::Index,
    writer_opts: &TantivyIndexWriterOpts,
) -> Result<tantivy::IndexWriter<TantivyDocument>> {
    let res = index.writer_with_num_threads::<TantivyDocument>(
        writer_opts.num_writer_threads,
        writer_opts.memory_budget_in_bytes(),
    )?;
    if writer_opts.index_writer_force_no_merges {
        res.set_merge_policy(Box::new(NoMergePolicy::default()));
    } else {
        // While writing, we want to target as few segments as possible, so we use an ExactNumberMergePolicy
        // with a target of 1.
        res.set_merge_policy(Box::new(ExactNumberMergePolicy(1)));
    }
    Ok(res)
}

pub trait FieldAdder: Send + Sync + std::fmt::Debug {
    fn add_field(&self, doc: &mut TantivyDocument, row: &serde_json::Value) -> Result<()>;

    fn extract_field(&self, doc: &TantivyDocument) -> Result<Option<serde_json::Value>>;

    fn is_repeated(&self) -> bool;
    fn path(&self) -> &Vec<String>;
    fn tantivy_field(&self) -> tantivy::schema::Field;
}

#[derive(Clone, Debug)]
pub struct PathFieldAdder {
    path: Vec<String>,
    field: tantivy::schema::Field,
    repeated: bool,
    field_type: util::schema::TantivyType,
}

pub const JSON_ROOT_FIELD: &str = "o";
pub const JSON_PATHS_FIELD: &str = "p";

impl PathFieldAdder {
    fn is_pagination_key_field(&self) -> bool {
        self.path.len() == 1 && self.path[0] == "_pagination_key"
    }

    // We only handle field types which are mapped to in "PathFieldAdder::add_field".
    fn tantivy_value_to_json_value(
        &self,
        orig_tantivy_value: &OwnedValue,
    ) -> Result<serde_json::Value> {
        use serde_json::Value;
        use OwnedValue::*;
        let alt_value = match self.is_pagination_key_field() {
            true => Some(pagination_key_from_raw_tantivy(orig_tantivy_value)?),
            false => None,
        };
        let tantivy_value = alt_value.as_ref().unwrap_or(orig_tantivy_value);
        let value: Value = match tantivy_value {
            Null => Value::Null,
            Str(x) => Value::String(x.clone()),
            PreTokStr(_) => bail!("PreTokStr not supported"),
            U64(x) => Value::Number((*x).into()),
            I64(x) => Value::Number((*x).into()),
            F64(x) => match serde_json::value::Number::from_f64(*x) {
                Some(v) => Value::Number(v),
                None => {
                    bail!("Failed to convert f64 to JSON number: {}", x);
                }
            },
            Bool(x) => Value::Bool(*x),
            Object(x) => {
                let mut object = serde_json::Map::new();
                for (k, v) in x.iter() {
                    object.insert(k.clone(), self.tantivy_value_to_json_value(v)?);
                }
                Value::Object(object)
            }
            Date(x) => Value::String(
                DateTime::<Utc>::from_timestamp_micros(x.into_timestamp_micros())
                    .ok_or_else(|| anyhow!("Invalid DateTime"))?
                    .to_rfc3339_opts(chrono::SecondsFormat::AutoSi, true)
                    .to_string(),
            ),
            Facet(_) => bail!("Facet not supported"),
            Bytes(_) => bail!("Bytes not supported"),
            Array(x) => {
                let mut array = Vec::new();
                for v in x.iter() {
                    array.push(self.tantivy_value_to_json_value(v)?);
                }
                Value::Array(array)
            }
            IpAddr(_) => bail!("IpAddr not supported"),
        };
        Ok(value)
    }

    fn add_value(&self, doc: &mut TantivyDocument, orig_value: &serde_json::Value) -> Result<()> {
        let alt_value = match self.is_pagination_key_field() {
            true => Some(raw_pagination_key_from_json(orig_value)?),
            false => None,
        };
        let value = alt_value.as_ref().unwrap_or(orig_value);

        use util::schema::TantivyType::*;
        macro_rules! add_field_with_type {
            ($as_type:ident) => {
                if value.is_null() {
                    return Ok(());
                } else if let Some(v) = value.$as_type() {
                    doc.add_field_value(self.field, v);
                } else {
                    bail!(StorageError::InvalidTypeAtPath {
                        path: self.path.clone(),
                        value: value.to_string(),
                        expected: self.field_type.clone(),
                    });
                }
            };
        }

        match &self.field_type {
            Str(_) => match value {
                serde_json::Value::String(s) => {
                    doc.add_field_value(self.field, s.clone());
                }
                serde_json::Value::Null => {
                    // Do nothing if it's null
                }
                _ => {
                    // We are more permissive about serializing non-string values to string fields,
                    // since we often want to index data like JSON objects in their raw string
                    // form. This should not break schema invertibility as long as the schema
                    // contains a stored field of the correct type and all stored fields have the
                    // same type (which is checked in schema construction).
                    let s = serde_json::to_string(&value).map_err(|_| {
                        StorageError::InvalidTypeAtPath {
                            path: self.path.clone(),
                            value: value.to_string(),
                            expected: self.field_type.clone(),
                        }
                    })?;
                    doc.add_field_value(self.field, s);
                }
            },
            U64(_) => match value {
                serde_json::Value::Number(_) => {
                    add_field_with_type!(as_u64)
                }
                serde_json::Value::String(s) => {
                    let u64_value =
                        s.parse::<u64>()
                            .map_err(|_| StorageError::InvalidTypeAtPath {
                                path: self.path.clone(),
                                value: s.clone(),
                                expected: self.field_type.clone(),
                            })?;
                    doc.add_field_value(self.field, u64_value);
                }
                _ => bail!(StorageError::InvalidTypeAtPath {
                    path: self.path.clone(),
                    value: value.to_string(),
                    expected: self.field_type.clone(),
                }),
            },
            I64(_) => add_field_with_type!(as_i64),
            F64(_) => add_field_with_type!(as_f64),
            Bool(_) => add_field_with_type!(as_bool),
            Date(_) => {
                if value.is_null() {
                    return Ok(());
                } else if let Ok(v) = serde_json::from_value::<DateTime<Utc>>(value.clone()) {
                    let tantivy_dt = tantivy::DateTime::from_timestamp_micros(v.timestamp_micros());
                    doc.add_field_value(self.field, tantivy_dt);
                } else {
                    bail!(StorageError::InvalidTypeAtPath {
                        path: self.path.clone(),
                        value: value.to_string(),
                        expected: self.field_type.clone(),
                    });
                }
            }
            Facet(_) => add_field_with_type!(as_str),
            Bytes(_) => add_field_with_type!(as_str),
            Json(_) => {
                // Tantivy only wants to consume "objects" in JSON fields, so we always
                // wrap our objects.
                let mut object_wrapper: BTreeMap<String, OwnedValue> = BTreeMap::new();
                object_wrapper.insert(JSON_ROOT_FIELD.to_string(), value.clone().into());
                object_wrapper.insert(
                    JSON_PATHS_FIELD.to_string(),
                    OwnedValue::Array(
                        util::json::compute_non_empty_paths(&value)
                            .iter()
                            .map(|path| OwnedValue::Str(util::json::serialize_path(path)))
                            .collect(),
                    ),
                );

                doc.add_field_value(self.field, OwnedValue::Object(object_wrapper));
            }
            IpAddr(_) => add_field_with_type!(as_str),
        };
        Ok(())
    }
}

impl FieldAdder for PathFieldAdder {
    fn add_field(&self, doc: &mut TantivyDocument, row: &serde_json::Value) -> Result<()> {
        let value = match util::json::value_at_path(row, &self.path) {
            Some(v) => v,
            None => {
                // If the field is missing, we don't add it to the document.
                return Ok(());
            }
        };

        match (value, self.repeated) {
            (_, false) | (serde_json::Value::Null, _) => {
                // For a non-repated field type or null, we just add the value.
                self.add_value(doc, value)
            }
            (serde_json::Value::Array(arr), true) => {
                // If it's an array value but a scalar field, add each element individually.
                // We could make this more explicit in the schema, but for simplicity's sake,
                // we allow this.
                for v in arr {
                    self.add_value(doc, v)?;
                }
                Ok(())
            }
            _ => Err(anyhow!(StorageError::ExpectedRepeatedValueAtPath {
                path: self.path.clone(),
                value: value.to_string(),
                base_type: self.field_type.clone(),
            })),
        }
    }

    fn extract_field(&self, doc: &TantivyDocument) -> Result<Option<serde_json::Value>> {
        let values = doc.get_all(self.field).collect::<Vec<_>>();

        Ok(if values.len() == 0 {
            None
        } else if values.len() == 1 && !self.repeated {
            match values[0] {
                OwnedValue::Object(o) => match o.get(JSON_ROOT_FIELD) {
                    Some(v) => Some(self.tantivy_value_to_json_value(v)?),
                    None => bail!("Missing field: {}", JSON_ROOT_FIELD),
                },
                _ => Some(self.tantivy_value_to_json_value(values[0])?),
            }
        } else {
            Some(serde_json::Value::Array(
                values
                    .into_iter()
                    .map(|v| self.tantivy_value_to_json_value(v))
                    .collect::<Result<Vec<_>>>()?,
            ))
        })
    }

    fn path(&self) -> &Vec<String> {
        &self.path
    }

    fn is_repeated(&self) -> bool {
        self.repeated
    }

    fn tantivy_field(&self) -> tantivy::schema::Field {
        self.field
    }
}

#[inline(always)]
pub fn str_column_read_single_bytes_for_doc(reader: &StrColumn, doc_id: DocId) -> Result<Vec<u8>> {
    let ord = reader
        .term_ords(doc_id)
        .next()
        .ok_or_else(|| anyhow!("Missing field in doc {}", doc_id))?;
    let mut ord_bytes: Vec<u8> = Vec::new();
    if !reader.ord_to_bytes(ord, &mut ord_bytes)? {
        bail!("Invalid ord {}", ord);
    }
    Ok(ord_bytes)
}

#[inline(always)]
pub fn collect_column_raw_value_for_doc<
    'a,
    T: PartialOrd + Copy + std::fmt::Debug + Send + Sync + 'static,
>(
    column_handle: &'a Column<T>,
    doc_id: DocId,
) -> Result<T> {
    let mut iter = column_handle
        .values_for_doc(doc_id)
        .map(|value| value.into());
    iter.next()
        .ok_or_else(|| anyhow!("No value found for column"))
}

pub fn warm_segment_readers(index: &tantivy::Index) -> Result<()> {
    // For our purposes, we don't care about grabbing the lock.
    let searchable_segments = index.searchable_segments()?;

    STATIC_SYNC_RUNTIME.block_on(
        async move {
            join_all(searchable_segments.into_iter().map(|segment| {
                let span = tracing::info_span!("warm_segment_reader");
                spawn_blocking_with_async_timeout(
                    STATIC_SYNC_RUNTIME.handle(),
                    move || {
                        let _guard = span.enter();
                        SegmentReader::open(&segment)
                    },
                    Default::default(),
                    || "warm_segment_readers".into(),
                )
            }))
            .await
        }
        .instrument(tracing::info_span!("warm_segment_readers")),
    );

    Ok(())
}

/// The tantivy IndexMeta struct wraps the meta.json data, but its deserialization is private. We
/// implement a lighter-weight serde wrapper around it for our own purposes.
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct IndexMetaJson {
    pub index_settings: tantivy::index::IndexSettings,
    pub segments: Vec<SegmentMetaJson>,
    pub schema: tantivy::schema::Schema,
    pub opstamp: tantivy::Opstamp,
    pub payload: Option<String>,
    #[serde(default)]
    pub brainstore_meta: serde_json::Map<String, serde_json::Value>,
}

impl Default for IndexMetaJson {
    fn default() -> Self {
        IndexMetaJson {
            index_settings: Default::default(),
            segments: Default::default(),
            schema: tantivy::schema::SchemaBuilder::default().build(),
            opstamp: Default::default(),
            payload: Default::default(),
            brainstore_meta: Default::default(),
        }
    }
}

impl IndexMetaJson {
    pub fn from_tantivy_meta(tantivy_meta: &tantivy::IndexMeta) -> Result<Self> {
        let tantivy_meta_json = serde_json::to_value(&tantivy_meta)?;
        Ok(serde_json::from_value(tantivy_meta_json)?)
    }

    pub fn hash(&self) -> Result<String> {
        let json = serde_json::to_string(self)?;
        let mut hasher = Sha256::new();
        hasher.update(json.as_bytes());
        Ok(format!("{:x}", hasher.finalize()))
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct SegmentMetaJson {
    pub segment_id: tantivy::SegmentId,
    pub deletes: Option<DeleteMetaJson>,
    #[serde(flatten)]
    _other: serde_json::Map<String, serde_json::Value>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct DeleteMetaJson {
    pub num_deleted_docs: u32,
    opstamp: tantivy::Opstamp,
}

impl SegmentMetaJson {
    // These are cloned from the `tantivy::SegmentMeta` impl in tantivy's
    // `index_meta.rs` file.
    pub fn num_deleted_docs(&self) -> u32 {
        self.deletes
            .as_ref()
            .map(|x| x.num_deleted_docs)
            .unwrap_or(0u32)
    }

    pub fn has_deletes(&self) -> bool {
        self.num_deleted_docs() > 0
    }

    pub fn delete_opstamp(&self) -> Option<tantivy::Opstamp> {
        self.deletes.as_ref().map(|delete_meta| delete_meta.opstamp)
    }

    pub fn relative_path(&self, component: SegmentComponent, ext: &'static str) -> PathBuf {
        let mut path = self.segment_id.uuid_string();
        if component == SegmentComponent::Delete {
            let opstamp = self.delete_opstamp().unwrap_or(0);
            path.push_str(&format!(".{}.{}", opstamp, ext));
        } else {
            path.push_str(".");
            path.push_str(ext);
        }
        PathBuf::from(path)
    }
}

pub fn merge_index_meta(lhs: IndexMetaJson, rhs: &IndexMetaJson) -> Result<IndexMetaJson> {
    let mut lhs = lhs;

    // index_settings must be the same.
    if lhs.index_settings != rhs.index_settings {
        return Err(anyhow!("index_settings do not match"));
    }

    // schemas must be the same.
    if lhs.schema != rhs.schema {
        return Err(anyhow!("Schemas do not match"));
    }

    // Concatenate the segment lists.
    {
        let lhs_segment_ids = lhs
            .segments
            .iter()
            .map(|x| &x.segment_id)
            .collect::<HashSet<_>>();
        for rhs_segment in rhs.segments.iter() {
            if lhs_segment_ids.contains(&rhs_segment.segment_id) {
                return Err(anyhow!("Duplicate segment id {}", rhs_segment.segment_id));
            }
        }
        lhs.segments.extend(rhs.segments.clone());
    }

    // Pick the greater of the two opstamps, and prefer the first payload.
    lhs.opstamp = std::cmp::max(lhs.opstamp, rhs.opstamp);
    lhs.payload = lhs.payload.or_else(|| rhs.payload.clone());

    Ok(lhs)
}

pub async fn collect_meta_json<'a, T: AsyncDirectory + ?Sized>(
    directory: &T,
    index_path: &'a Path,
) -> Result<Option<IndexMetaJson>> {
    let index_meta: Option<IndexMetaJson> =
        read_json_value(directory, &index_path.join("meta.json"))
            .await?
            .map(serde_json::from_value)
            .transpose()?;
    Ok(index_meta)
}

pub async fn write_meta_json<'a, T: AsyncDirectory + ?Sized>(
    directory: &T,
    index_path: &'a Path,
    meta: &IndexMetaJson,
) -> Result<()> {
    write_json_value(
        directory,
        &index_path.join("meta.json"),
        &serde_json::to_value(meta)?,
    )
    .await
}

// Given an index meta, checks that the original schema is a prefix of the full schema, and if so,
// updates the schema to the full schema. If not, returns an error.
pub fn check_and_set_meta_schema(
    mut meta: IndexMetaJson,
    full_schema: &tantivy::schema::Schema,
) -> Result<IndexMetaJson> {
    for (full_entry, this_entry) in full_schema.fields().zip(meta.schema.fields()) {
        if full_entry != this_entry {
            bail!(
                "Index schema field {:?} does not match full schema field {:?}",
                this_entry,
                full_entry
            );
        }
    }
    meta.schema = full_schema.clone();
    Ok(meta)
}

// Extract the segment ID from a path in a tantivy index. Segment id paths are expected to be of
// the form "[segment_id]...[ext]". They're usually one path component, except deletes, which have
// two.
pub fn extract_segment_id(path: &Path) -> Option<SegmentId> {
    match path.parent() {
        None => (),
        Some(p) => {
            if !p.as_os_str().is_empty() {
                return None;
            }
        }
    }
    if !path
        .extension()
        .and_then(|x| x.to_str())
        .map(|x| ALL_SEGMENT_COMPONENTS.iter().any(|(_, ext)| *ext == x))
        .unwrap_or(false)
    {
        return None;
    }

    path.to_str()
        .and_then(|x| x.split('.').next())
        .and_then(|x| SegmentId::from_uuid_string(x).ok())
}

const TANTIVY_INDEX_OPSTAMP_SEGMENT_EXTENSIONS: &[&str] = &["del"];

// Extract the opstamp from a path in a tantivy index. Most files don't have opstamps, but those
// that do have the form "[segment_id].[opstamp].[ext]".
pub fn extract_opstamp(path: &Path) -> Option<tantivy::Opstamp> {
    match path.parent() {
        None => (),
        Some(p) => {
            if !p.as_os_str().is_empty() {
                return None;
            }
        }
    }
    if !path
        .extension()
        .and_then(|x| x.to_str())
        .map(|x| TANTIVY_INDEX_OPSTAMP_SEGMENT_EXTENSIONS.contains(&x))
        .unwrap_or(false)
    {
        return None;
    }

    path.to_str()
        .and_then(|x| {
            let mut split_iter = x.split('.');
            split_iter.next();
            split_iter.next()
        })
        .and_then(|x| x.parse::<tantivy::Opstamp>().ok())
}

// The _pagination_key field is serialized as a string in JSON but indexed as a u64 in tantivy.
// Therefore we need to convert between the two representations when converting between JSON
// objects and tantivy documents.

pub fn pagination_key_from_raw_tantivy(raw_tantivy: &OwnedValue) -> Result<OwnedValue> {
    match raw_tantivy {
        OwnedValue::U64(u) => Ok(OwnedValue::Str(PaginationKey(*u).to_string())),
        _ => Err(anyhow!("Expected u64 for raw tantivy pagination key")),
    }
}

fn raw_pagination_key_from_json(json_value: &serde_json::Value) -> Result<serde_json::Value> {
    json_value
        .as_str()
        .map(|s| -> Result<serde_json::Value> {
            Ok(serde_json::Value::Number(
                s.parse::<PaginationKey>()?.0.into(),
            ))
        })
        .transpose()?
        .ok_or_else(|| anyhow!("Expected u64 for json pagination key"))
}

// This method "flashes" the tantivy metadata on the filesystem to the provided
// metadata and schema values. This way, if we happened to try an index
// modification operation (like compaction or merging) which failed partway
// through, or we modified the stored metadata separately (e.g. by clearing it
// to force a recompaction) we can restore the index state to a known valid
// point, and avoid considering any stray segments from the failed compaction.
//
// In some cases, flashing the index requires modifying the input metadata to
// better-ensure that no new files conflict with existing files. Reasons
// include:
//      - Reconcile any new schema with the stored one, in case we added new
//      fields.
pub async fn flash_tantivy_index(
    tantivy_meta: Option<&IndexMetaJson>,
    schema: &util::schema::Schema,
    index_store: &StoreInfo,
    index_scope: &TantivyIndexScope,
    wipe_opstamp_file: Option<&Path>,
) -> Result<Option<IndexMetaJson>> {
    if let Some(tantivy_meta) = tantivy_meta {
        let mut tantivy_meta = tantivy_meta.clone();

        // If there is an opstamp file that we need to wipe, wipe it if its
        // opstamp is greater than the max opstamp in the meta.json.
        if let Some(path) = wipe_opstamp_file {
            if let Some(file_name) = path.file_name() {
                if let Some(opstamp) = extract_opstamp(Path::new(file_name)) {
                    if opstamp > tantivy_meta.opstamp {
                        match index_store.directory.async_delete(path).await {
                            Ok(()) => (),
                            Err(e) => match e {
                                tantivy::directory::error::DeleteError::FileDoesNotExist(_) => {
                                    log::warn!("{:?}. Ignoring", e);
                                }
                                _ => return Err(e.into()),
                            },
                        };
                    }
                }
            }
        }

        // Flash the filesystem meta.json to our snapshotted value. Along the way, update the index
        // schema to the provided one, as long as it is compatible with the existing schema.
        let index_path = index_scope.path(&index_store.prefix);
        let updated_schema = make_tantivy_schema(schema)?;
        tantivy_meta = check_and_set_meta_schema(tantivy_meta, &updated_schema.schema)?;
        write_meta_json(index_store.directory.as_ref(), &index_path, &tantivy_meta)
            .instrument(tracing::info_span!("flash meta.json"))
            .await?;

        Ok(Some(tantivy_meta))
    } else {
        // If we don't have a last_compacted_index_meta, we are starting from
        // scratch. In this case, wipe out any existing meta.json file.
        let index_path = index_scope.path(&index_store.prefix);
        let meta_json_path = index_path.join("meta.json");
        match index_store
            .directory
            .async_delete(&meta_json_path)
            .instrument(tracing::info_span!("clear meta.json"))
            .await
        {
            Ok(_) => (),
            Err(e) => match e {
                tantivy::directory::error::DeleteError::FileDoesNotExist(_) => (),
                _ => {
                    log::warn!("Failed to clear meta.json: {}", e);
                }
            },
        }
        Ok(None)
    }
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct ValidateTantivyIndexOptions {
    #[arg(
        long,
        default_value_t = default_index_writer_validate(),
        help = "Validate the index after writing. This can help guarantee that every file the index expects to exist does indeed exist.",
        env = "BRAINSTORE_INDEX_WRITER_VALIDATE"
    )]
    pub index_writer_validate: bool,

    #[arg(
        long,
        default_value_t = default_index_writer_validate_only_deletes(),
        help = "Only consider deletes when validating the index. This will reduce the cost of the validation at the cost of not checking for other missing files.",
        env = "BRAINSTORE_INDEX_WRITER_VALIDATE_ONLY_DELETES"
    )]
    pub index_writer_validate_only_deletes: bool,
}

fn default_index_writer_validate() -> bool {
    true
}

fn default_index_writer_validate_only_deletes() -> bool {
    true
}

impl Default for ValidateTantivyIndexOptions {
    fn default() -> Self {
        Self {
            index_writer_validate: default_index_writer_validate(),
            index_writer_validate_only_deletes: default_index_writer_validate_only_deletes(),
        }
    }
}

pub const ALL_SEGMENT_COMPONENTS: [(SegmentComponent, &'static str); 7] = [
    (SegmentComponent::Postings, "idx"),
    (SegmentComponent::Positions, "pos"),
    (SegmentComponent::FastFields, "fast"),
    (SegmentComponent::FieldNorms, "fieldnorm"),
    (SegmentComponent::Terms, "term"),
    (SegmentComponent::Store, "store"),
    (SegmentComponent::Delete, "del"),
];

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidateTantivyIndexResult {
    index_meta: IndexMetaJson,
    validated_files: Vec<PathBuf>,
    missing_files: Vec<PathBuf>,
}

impl ValidateTantivyIndexResult {
    pub fn check_success(mut self) -> Result<IndexMetaJson> {
        if self.missing_files.is_empty() {
            self.index_meta.brainstore_meta.insert(
                "validated_files".to_string(),
                serde_json::to_value(&self.validated_files).unwrap(),
            );
            Ok(self.index_meta)
        } else {
            Err(anyhow!(
                "Missing files: {:?}. last_compacted_index_meta: {:?}",
                self.missing_files,
                serde_json::to_string(&self.index_meta)
            ))
        }
    }
}

// This is exercised by unit tests which run process_wal and merge.
#[instrument(err, skip(index_meta, index_store, index_prefix, index_scope, opts), fields(index_scope = ?index_scope))]
pub async fn validate_tantivy_index(
    index_meta: IndexMetaJson,
    index_store: &dyn ObjectStore,
    index_prefix: &Path,
    index_scope: &TantivyIndexScope,
    opts: &ValidateTantivyIndexOptions,
) -> Result<ValidateTantivyIndexResult> {
    if !opts.index_writer_validate {
        return Ok(ValidateTantivyIndexResult {
            index_meta,
            validated_files: Vec::new(),
            missing_files: Vec::new(),
        });
    }

    let index_files: Vec<PathBuf> = index_meta
        .segments
        .iter()
        .flat_map(|meta| {
            ALL_SEGMENT_COMPONENTS
                .iter()
                .filter(|(c, _ext)| {
                    let c = *c;
                    if !meta.has_deletes() && c == SegmentComponent::Delete {
                        false
                    } else if opts.index_writer_validate_only_deletes
                        && c != SegmentComponent::Delete
                    {
                        false
                    } else {
                        true
                    }
                })
                .map(|(component, ext)| meta.relative_path(*component, ext))
        })
        .collect();

    let tantivy_index_path = index_scope.path(index_prefix);
    let existences = join_all(index_files.iter().map(|rel_path| {
        let full_path = tantivy_index_path.join(rel_path);
        async move {
            let full_object_store_path =
                object_store::path::Path::from(full_path.to_str().ok_or(anyhow!("Invalid path"))?);
            Ok(index_store.head(&full_object_store_path).await.is_ok())
        }
    }))
    .await
    .into_iter()
    .collect::<Result<Vec<bool>>>()?;

    let mut res = ValidateTantivyIndexResult {
        index_meta,
        validated_files: Vec::new(),
        missing_files: Vec::new(),
    };
    for (rel_path, exists) in index_files.into_iter().zip(existences) {
        if exists {
            res.validated_files.push(tantivy_index_path.join(rel_path));
        } else {
            res.missing_files.push(tantivy_index_path.join(rel_path));
        }
    }
    Ok(res)
}
