use btql::binder::types::weakest_scalar_type;
use btql::binder::{ast::Literal, Expr};
use btql::schema::ScalarType;
use util::Value;

use super::{
    ast::GroupByQuery,
    context::PlanContext,
    error::{PlannerError, Result},
    plan,
};
use crate::{interpreter::aggregate::DynamicAggregator, optimizer::ast::GroupByPlan};

pub fn plan_aggregate_function(
    function: &btql::binder::ast::Function,
) -> Result<DynamicAggregator> {
    use crate::interpreter::aggregate::*;
    match function.name.as_str() {
        "count" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "count expects 1 argument".to_string(),
                });
            }
            let count_expr = function.args[0].clone();
            Ok(match count_expr.as_ref() {
                Expr::Literal(Literal { value, .. }) if !matches!(value, Value::Null) => {
                    DynamicAggregator::ConstantCount(ConstantCount::default())
                }
                _ => DynamicAggregator::ExprCount(ExprCount::new(&count_expr)),
            })
        }
        "sum" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "sum expects 1 argument".to_string(),
                });
            }
            let sum_expr: Box<Expr> = function.args[0].clone();

            let scalar_type = match sum_expr.as_ref() {
                Expr::Literal(Literal { expr_type, .. }) => *expr_type,
                Expr::Field(field) => weakest_scalar_type(&field.expr_type),
                _ => ScalarType::Unknown,
            };

            match scalar_type {
                ScalarType::Integer | ScalarType::Boolean => {
                    Ok(DynamicAggregator::IntSum(IntSum::new(&sum_expr)))
                }
                _ => Ok(DynamicAggregator::FloatSum(FloatSum::new(&sum_expr))),
            }
        }
        "avg" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "avg expects 1 argument".to_string(),
                });
            }
            let avg_expr = function.args[0].clone();

            let scalar_type = match avg_expr.as_ref() {
                Expr::Literal(Literal { expr_type, .. }) => *expr_type,
                Expr::Field(field) => weakest_scalar_type(&field.expr_type),
                _ => ScalarType::Unknown,
            };

            match scalar_type {
                ScalarType::Integer => Ok(DynamicAggregator::IntAvg(IntAvg::new(&avg_expr))),
                _ => Ok(DynamicAggregator::FloatAvg(FloatAvg::new(&avg_expr))),
            }
        }
        "min" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "min expects 1 argument".to_string(),
                });
            }
            let min_expr = function.args[0].clone();

            let scalar_type = match min_expr.as_ref() {
                Expr::Literal(Literal { expr_type, .. }) => *expr_type,
                Expr::Field(field) => weakest_scalar_type(&field.expr_type),
                _ => ScalarType::Unknown,
            };

            match scalar_type {
                ScalarType::Null => Ok(DynamicAggregator::NullMin(Min::<()>::new(&min_expr))),
                ScalarType::Boolean => Ok(DynamicAggregator::BoolMin(Min::<bool>::new(&min_expr))),
                ScalarType::Integer => Ok(DynamicAggregator::IntMin(Min::<i128>::new(&min_expr))),
                ScalarType::Number => Ok(DynamicAggregator::FloatMin(Min::<f64>::new(&min_expr))),
                ScalarType::String
                | ScalarType::Date
                | ScalarType::DateTime
                | ScalarType::Interval => {
                    Ok(DynamicAggregator::StringMin(Min::<String>::new(&min_expr)))
                }
                ScalarType::Array | ScalarType::Object => {
                    Ok(DynamicAggregator::JsonMin(JsonMin::new(&min_expr)))
                }
                ScalarType::Unknown => {
                    Ok(DynamicAggregator::UnknownMin(UnknownMin::new(&min_expr)))
                }
            }
        }
        "max" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "max expects 1 argument".to_string(),
                });
            }
            let max_expr = function.args[0].clone();

            let scalar_type = match max_expr.as_ref() {
                Expr::Literal(Literal { expr_type, .. }) => *expr_type,
                Expr::Field(field) => weakest_scalar_type(&field.expr_type),
                _ => ScalarType::Unknown,
            };

            match scalar_type {
                ScalarType::Null => Ok(DynamicAggregator::NullMax(Max::<()>::new(&max_expr))),
                ScalarType::Boolean => Ok(DynamicAggregator::BoolMax(Max::<bool>::new(&max_expr))),
                ScalarType::Integer => Ok(DynamicAggregator::IntMax(Max::<i128>::new(&max_expr))),
                ScalarType::Number => Ok(DynamicAggregator::FloatMax(Max::<f64>::new(&max_expr))),
                ScalarType::String
                | ScalarType::Date
                | ScalarType::DateTime
                | ScalarType::Interval => {
                    Ok(DynamicAggregator::StringMax(Max::<String>::new(&max_expr)))
                }
                ScalarType::Array | ScalarType::Object => {
                    Ok(DynamicAggregator::JsonMax(JsonMax::new(&max_expr)))
                }
                ScalarType::Unknown => {
                    Ok(DynamicAggregator::UnknownMax(UnknownMax::new(&max_expr)))
                }
            }
        }
        "percentile" => {
            if function.args.len() != 2 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "percentile expects 2 arguments: expression and percentile value"
                        .to_string(),
                });
            }
            let expr = function.args[0].clone();
            let percentile = match function.args[1].as_ref() {
                Expr::Literal(Literal {
                    value: Value::Number(n),
                    ..
                }) => {
                    if let Some(p) = n.as_f64() {
                        if p < 0.0 || p > 100.0 {
                            return Err(PlannerError::InvalidOptimizedExpr {
                                msg: "percentile value must be between 0 and 100".to_string(),
                            });
                        }
                        p
                    } else {
                        return Err(PlannerError::InvalidOptimizedExpr {
                            msg: "percentile value must be a number".to_string(),
                        });
                    }
                }
                _ => {
                    return Err(PlannerError::InvalidOptimizedExpr {
                        msg: "percentile value must be a numeric literal".to_string(),
                    })
                }
            };

            Ok(DynamicAggregator::Percentile(Percentile::new(
                &expr, percentile,
            )))
        }
        _ => Err(PlannerError::InvalidOptimizedExpr {
            msg: format!("unknown aggregate function: {}", function.name),
        }),
    }
}

pub fn plan_groupby_query(ctx: &PlanContext, query: &GroupByPlan) -> Result<GroupByQuery> {
    Ok(GroupByQuery {
        from: plan(ctx, &query.from)?,
        dimensions: query.dimensions.clone(),
        pivot: query.pivot.clone(),
        measures: query.measures.clone(),
        aggregates: query
            .aggregates
            .iter()
            .map(|(alias, func)| Ok((alias.clone(), plan_aggregate_function(func)?)))
            .collect::<Result<Vec<_>>>()?,
    })
}
