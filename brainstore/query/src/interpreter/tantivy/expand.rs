use async_stream::stream;
use btql::{
    interpreter::expr::interpret_expr_vec,
    interpreter::expr::perform_sha256_hash,
    util::{Cursor, CursorDirection, CursorValue},
};
use itertools::izip;
use lazy_static::lazy_static;
use std::{
    borrow::Cow,
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tantivy::query::EnableScoring;
use tokio::sync::mpsc;
use util::{
    anyhow::Context,
    futures::StreamExt,
    json::PathPiece,
    spawn_blocking_util::spawn_blocking_with_async_timeout,
    tracer::{trace_if, EnterTraceGuard, TracedNode},
    xact::PaginationKey,
    Value,
};

use crate::{
    interpreter::{
        context::{InterpreterContextState, EMPTY_CURSOR_VALUE},
        error::{InterpreterError, Result},
        limiter::project_batch,
        local::interpret_expr_parallel,
        op::{send_row, ShouldAbort},
        tantivy::{
            search::{extract_docs_into_rows_columnar, PAGINATION_KEY_FLAT, XACT_ID_FLAT},
            summary::{aggregate_realtime_summary, SummaryCollector},
        },
        InterpreterContext, StreamValue,
    },
    optimizer::{
        ast::{CursorField, CursorFilter, TantivyProjectedField},
        tantivy::{make_field_name, AVERAGE_SPANS_PER_TRACE},
    },
    planner::ast::{PlannedExpansionProjection, TantivyExpandTracesQuery},
    Operator,
};
use btql::typesystem::CastInto;

use super::{
    search::{collect_cursor_values, extract_cursor_value_from_row, extract_docs_into_rows},
    summary::{merge_summary_fruits, RootDoc, SummaryFruit, SummaryIntoRowInput},
};

pub const ROOT_SPAN_ID_FIELD: &str = "root_span_id";
pub const SPAN_ID_FIELD: &str = "span_id";
pub const ID_FIELD: &str = "id";
pub const IS_ROOT_FIELD: &str = "is_root";
pub const READER_BATCH_FIELD: &str = "_reader_batch";
pub const CREATED_FIELD: &str = "created";

lazy_static! {
    static ref ROOT_SPAN_ID_FLAT: String =
        make_field_name(&[PathPiece::Key(ROOT_SPAN_ID_FIELD.to_string())]);
}

#[async_trait::async_trait]
impl Operator for TantivyExpandTracesQuery {
    fn name(&self) -> &'static str {
        "Expand traces"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let TantivyExpandTracesQuery {
            from,
            projection,
            limit,
            cursor: cursor_filter,
        } = self;

        let mut index_state_ptr: Option<Arc<InterpreterContextState>> = None;

        let handle = ctx.handle.clone();

        let from_stream = from.interpret_node(ctx.clone(), tracer.clone())?;

        let batch_size = limit.map_or_else(
            || ctx.opts.batch_size(),
            |limit| limit * AVERAGE_SPANS_PER_TRACE,
        );

        let mut batched_stream = util::futures::StreamExt::ready_chunks(from_stream, batch_size);

        let consistent_reader_batch_idx_stream = stream! {
            let mut current_batch = Vec::new();
            let mut current_reader_batch_idx = 0;
            while let Some(rows) = batched_stream.next().await {
                for row in rows {
                    match row {
                        Ok(row) => {
                            let batch_idx = row.as_object().expect("row is not an object").get(READER_BATCH_FIELD).expect("row is missing reader batch field").as_u64().expect("reader batch field is not a number");
                            if batch_idx != current_reader_batch_idx {
                                if current_batch.len() > 0 {
                                    yield (current_batch, current_reader_batch_idx);
                                }
                                current_batch = Vec::new();
                                current_reader_batch_idx = batch_idx;
                            }
                            current_batch.push(Ok(row));
                        }
                        Err(e) => {
                            current_batch.push(Err(e));
                        }
                    }
                }
                if current_batch.len() > 0 {
                    yield (current_batch, current_reader_batch_idx);
                }
                current_batch = Vec::new();
            }
        };
        tokio::pin!(consistent_reader_batch_idx_stream);

        let effective_limit = match limit {
            Some(limit) => limit as i64,
            None => i64::MAX,
        };

        let mut remaining_rows = effective_limit;
        let mut last_cursor_value = EMPTY_CURSOR_VALUE;
        let mut seen_root_span_ids = HashSet::new();

        let mut realtime_traces = HashMap::new();

        'outer_loop: while let Some((rows, reader_batch_idx)) =
            consistent_reader_batch_idx_stream.next().await
        {
            let span = tracing::Span::current();
            let rows = rows.into_iter().collect::<Result<Vec<_>>>()?;

            let index_state = match &index_state_ptr {
                Some(index_state)
                    if index_state.tantivy_readers.len() > reader_batch_idx as usize =>
                {
                    index_state.clone()
                }
                _ => {
                    let index_state = ctx
                        .must_get_index_state()
                        .context("should have initialized index context")?;
                    index_state_ptr = Some(index_state.clone());
                    index_state
                }
            };

            if realtime_traces.len() == 0 && index_state.index_wal_reader.in_memory_docs().len() > 0
            {
                for (i, row) in index_state
                    .index_wal_reader
                    .in_memory_docs()
                    .iter()
                    .enumerate()
                {
                    realtime_traces
                        .entry(
                            row.as_object()
                                .unwrap()
                                .get(ROOT_SPAN_ID_FIELD)
                                .unwrap()
                                .as_str()
                                .unwrap()
                                .to_string(),
                        )
                        .or_insert_with(Vec::new)
                        .push(i);
                }
            }

            let mut new_root_span_ids = Vec::new();
            for row in rows {
                let root_span_id = row
                    .as_object()
                    .ok_or_else(|| InterpreterError::InternalError("expected object".to_string()))?
                    .get(&*ROOT_SPAN_ID_FLAT)
                    .ok_or_else(|| {
                        InterpreterError::InternalError(
                            "expected row to project root_span_id".to_string(),
                        )
                    })?
                    .as_str()
                    .ok_or_else(|| {
                        InterpreterError::InternalError(
                            "expected root_span_id to be a string".to_string(),
                        )
                    })?;

                let cursor_value = match &cursor_filter {
                    Some(CursorFilter {
                        field: CursorField::PaginationKey,
                        ..
                    }) => {
                        let pagination_key: PaginationKey = row
                            .as_object()
                            .ok_or_else(|| {
                                InterpreterError::InternalError("expected object".to_string())
                            })?
                            .get(&*PAGINATION_KEY_FLAT)
                            .ok_or_else(|| {
                                InterpreterError::InternalError(
                                    "expected row to project pagination_key".to_string(),
                                )
                            })?
                            .as_str()
                            .ok_or_else(|| {
                                InterpreterError::InternalError(
                                    "expected pagination_key to be a str".to_string(),
                                )
                            })?
                            .parse()?;
                        Some(pagination_key.0)
                    }
                    Some(CursorFilter {
                        field: CursorField::TransactionId,
                        ..
                    }) => Some(
                        row.as_object()
                            .ok_or_else(|| {
                                InterpreterError::InternalError("expected object".to_string())
                            })?
                            .get(&*XACT_ID_FLAT)
                            .ok_or_else(|| {
                                InterpreterError::InternalError(
                                    "expected row to project xact_id".to_string(),
                                )
                            })?
                            .as_u64()
                            .ok_or_else(|| {
                                InterpreterError::InternalError(
                                    "expected xact_id to be a number".to_string(),
                                )
                            })?,
                    ),
                    _ => None,
                };

                // Since we fetch the full list of matching spans (projecting their root_span_id and _pagination_key
                // fields), we can compute the pagination key in terms of these matching spans, and the next
                // time around skip any spans that are later than the pagination key.

                match &cursor_filter {
                    Some(CursorFilter {
                        dir,
                        value: Some(query_cursor),
                        ..
                    }) => {
                        let cursor_value = cursor_value.unwrap();
                        match dir {
                            CursorDirection::Max => {
                                if cursor_value <= query_cursor.cursor_value {
                                    seen_root_span_ids.insert(root_span_id.to_string());
                                    continue;
                                }
                            }
                            CursorDirection::Min => {
                                if cursor_value >= query_cursor.cursor_value {
                                    seen_root_span_ids.insert(root_span_id.to_string());
                                    continue;
                                }
                            }
                        }
                    }
                    _ => {}
                }

                if !seen_root_span_ids.contains(root_span_id) {
                    seen_root_span_ids.insert(root_span_id.to_string());
                    new_root_span_ids.push((root_span_id.to_string(), cursor_value));
                }
            }
            if new_root_span_ids.len() == 0 {
                continue;
            }

            // If we don't need to run anymore filters, we can truncate the list of root span ids to the remaining rows.
            // We max(1) so that if we have run out of loop iterations but are exhausting a tied cursor, we try to send
            // at least one row.
            if remaining_rows.max(1) < new_root_span_ids.len() as i64
                && match &projection {
                    PlannedExpansionProjection::Summary {
                        post_aggregation_filter,
                        ..
                    } => post_aggregation_filter.is_none(),
                    _ => true,
                }
            {
                truncate_with_cursor(&mut new_root_span_ids, remaining_rows.max(1) as usize);
            }

            let mut projected_rows = Vec::new();
            let mut realtime_segment = HashMap::new();

            if realtime_traces.len() > 0 {
                let realtime_docs = index_state.index_wal_reader.in_memory_docs();

                let mut cow_docs = Vec::new();
                for (root_span_id, _) in &new_root_span_ids {
                    if let Some(realtime_indices) = realtime_traces.get(root_span_id) {
                        for realtime_idx in realtime_indices {
                            cow_docs.push(Cow::Borrowed(&realtime_docs[*realtime_idx]));
                        }
                    }
                }

                match &projection {
                    PlannedExpansionProjection::Spans {
                        realtime_projection,
                        ..
                    } => {
                        let cursor_values = match &cursor_filter {
                            Some(CursorFilter { field, .. }) => {
                                extract_cursor_values_from_rows(&cow_docs, *field)
                            }
                            _ => vec![None; cow_docs.len()],
                        };

                        projected_rows.extend(
                            project_batch(&ctx.expr_ctx, &realtime_projection, cow_docs)?
                                .into_iter()
                                .zip(cursor_values.into_iter()),
                        );
                    }
                    PlannedExpansionProjection::Summary { .. } => {
                        realtime_segment = aggregate_realtime_summary(
                            cow_docs,
                            ctx.model_costs.as_ref().as_ref(),
                        )?;
                    }
                }
            }

            let root_span_id_field = index_state.tantivy_schema.get_field(ROOT_SPAN_ID_FIELD)?;
            let num_root_spans = new_root_span_ids.len();
            let query =
                index_state.index_wal_reader.wrap_exclude_query(Box::new(
                    tantivy::query::TermSetQuery::new(new_root_span_ids.iter().map(|(id, _)| {
                        tantivy::Term::from_field_text(root_span_id_field.clone(), id)
                    })),
                ));

            projected_rows.extend(
                spawn_blocking_with_async_timeout(
                    &handle,
                    {
                        let ctx = ctx.clone();
                        let tracer = tracer.clone();
                        let projection = projection.clone();
                        let index_state = index_state.clone();
                        let new_root_span_ids =
                            new_root_span_ids.iter().map(|key| key.clone()).collect();
                        let cursor_field = cursor_filter.as_ref().map(|filter| filter.field);
                        move || {
                            let _guard = span.enter();

                            let searcher =
                                index_state.tantivy_readers[reader_batch_idx as usize].searcher();

                            let docs = execute_projection(
                                &projection,
                                new_root_span_ids,
                                num_root_spans,
                                &query,
                                &searcher,
                                cursor_field,
                                &index_state.tantivy_schema,
                                &ctx,
                                tracer,
                                realtime_segment,
                            )?;

                            Ok::<_, InterpreterError>(docs)
                        }
                    },
                    Default::default(),
                    || "execute tantivy expand traces".into(),
                )
                .await
                .with_context(|| "failed to join expansion search task".to_string())???,
            );

            let mut num_rows: i64 = 0;

            match &projection {
                PlannedExpansionProjection::Spans { .. } => {
                    // Group the docs by root_span_id
                    let mut docs_by_root_span_id = HashMap::new();
                    for (doc, _) in projected_rows {
                        docs_by_root_span_id
                            .entry(
                                doc.as_object()
                                    .unwrap()
                                    .get(&*ROOT_SPAN_ID_FLAT)
                                    .unwrap()
                                    .as_str()
                                    .unwrap()
                                    .to_string(),
                            )
                            .or_insert_with(Vec::new)
                            .push(doc);
                    }

                    // Sort each root_span_id by id to determinize the output order
                    for root_span_id in docs_by_root_span_id.values_mut() {
                        root_span_id.sort_by_key(|doc| {
                            doc.as_object()
                                .unwrap()
                                .get("id")
                                .unwrap()
                                .as_str()
                                .unwrap()
                                .to_string()
                        });
                    }

                    for (root_span_id, cursor_value) in &new_root_span_ids {
                        match docs_by_root_span_id.remove(root_span_id) {
                            Some(rows) => {
                                num_rows += 1;

                                if num_rows > remaining_rows
                                    && *cursor_value != Some(last_cursor_value)
                                {
                                    break 'outer_loop;
                                }

                                match (cursor_value, &cursor_filter) {
                                    (
                                        Some(cursor_value),
                                        Some(CursorFilter {
                                            dir, collect: true, ..
                                        }),
                                    ) => {
                                        let cursor = Cursor::new(*cursor_value);
                                        ctx.update_cursor(&cursor, *dir);
                                    }
                                    _ => {}
                                }

                                for row in rows {
                                    match send_row(&tx, row).await {
                                        ShouldAbort::Continue => {}
                                        ShouldAbort::Abort => {
                                            // TODO: The better thing to do here is to check a cancelled signal to make sure
                                            // the query actually ended, rather than some error case.
                                            log::debug!(
                                                "Aborting tantivy search since the parent stream was cancelled"
                                            );
                                            return Ok(());
                                        }
                                    }
                                }
                            }
                            None => {}
                        }

                        last_cursor_value = cursor_value.unwrap_or(EMPTY_CURSOR_VALUE);
                    }
                }
                PlannedExpansionProjection::Summary { .. } => {
                    for (row, cursor_value) in projected_rows {
                        num_rows += 1;
                        if num_rows > remaining_rows && cursor_value != Some(last_cursor_value) {
                            break 'outer_loop;
                        }
                        last_cursor_value = cursor_value.unwrap_or(EMPTY_CURSOR_VALUE);

                        match (cursor_value, &cursor_filter) {
                            (
                                Some(cursor_value),
                                Some(CursorFilter {
                                    dir, collect: true, ..
                                }),
                            ) => {
                                let cursor = Cursor::new(cursor_value);
                                ctx.update_cursor(&cursor, *dir);
                            }
                            _ => {}
                        }

                        match send_row(&tx, row).await {
                            ShouldAbort::Continue => {}
                            ShouldAbort::Abort => {
                                return Ok(());
                            }
                        }
                    }
                }
            }

            remaining_rows = remaining_rows.saturating_sub(num_rows);
            if remaining_rows <= 0 {
                break;
            }
        }

        Ok(())
    }
}

fn execute_projection(
    projection: &PlannedExpansionProjection,
    new_root_span_ids: Vec<(String, Option<u64>)>,
    num_root_spans: usize,
    query: &Box<dyn tantivy::query::Query>,
    searcher: &tantivy::Searcher,
    cursor_field: Option<CursorField>,
    schema: &tantivy::schema::Schema,
    ctx: &InterpreterContext,
    tracer: Option<Arc<TracedNode>>,
    realtime_segment: HashMap<String, SummaryFruit>,
) -> Result<Vec<(Value, Option<CursorValue>)>> {
    let enabled_scoring = EnableScoring::disabled_from_searcher(&searcher);

    let docs = match &projection {
        PlannedExpansionProjection::Spans {
            is_root_projection,
            root_projection,
            root_is_columnar,
            span_projection,
            span_is_columnar,
            ..
        } => {
            let collector = tantivy::collector::DocSetCollector;
            let doc_addrs = trace_if(log::Level::Info, &tracer, "Expansion search", |child| {
                child.increment_counter("num_spans", num_root_spans as u64);
                searcher.search_with_executor(query, &collector, &ctx.executor, enabled_scoring)
            })?;

            let cursor_values = match cursor_field {
                Some(field) => {
                    collect_cursor_values(doc_addrs.iter().map(|doc| *doc), searcher, field)?
                }
                None => vec![None; doc_addrs.len()],
            };

            let docs = trace_if(log::Level::Info, &tracer, "Retrieve documents", |child| {
                extract_expanded_rows(
                    child,
                    &ctx.executor,
                    &is_root_projection,
                    &root_projection,
                    *root_is_columnar,
                    &span_projection,
                    *span_is_columnar,
                    schema,
                    doc_addrs.into_iter(),
                    &searcher,
                )
            })?;

            docs.into_iter()
                .zip(cursor_values.into_iter())
                .collect::<Vec<_>>()
        }
        PlannedExpansionProjection::Summary {
            root_projection,
            comparison_key,
            weighted_scores,
            custom_columns,
            post_aggregation_filter,
            preview_length,
        } => {
            let collector = SummaryCollector {
                model_costs: ctx.model_costs.clone(),
            };
            let mut docs = trace_if(log::Level::Info, &tracer, "Expansion search", |child| {
                child.increment_counter("num_spans", num_root_spans as u64);
                searcher.search_with_executor(query, &collector, &ctx.executor, enabled_scoring)
            })?;

            merge_summary_fruits(&mut docs, realtime_segment)?;

            let mut docs: Vec<SummaryFruit> = new_root_span_ids
                .iter()
                .filter_map(|(id, _)| docs.remove(id))
                .collect();

            let mut root_docs = vec![Value::Null; docs.len()];

            let mut root_addrs = Vec::new();
            let mut addr_to_idx = Vec::new();

            for (i, fruit) in docs.iter_mut().enumerate() {
                if let Some(root_doc) = fruit.root_doc.take() {
                    match root_doc {
                        RootDoc::Address(addr) => {
                            root_addrs.push(addr);
                            addr_to_idx.push(i);
                        }
                        RootDoc::Doc(doc) => root_docs[i] = doc,
                    }
                }
            }

            for (i, row) in trace_if(log::Level::Info, &tracer, "Retrieve root docs", |child| {
                child.increment_counter("num_root_spans", root_addrs.len() as u64);
                extract_docs_into_rows(
                    child,
                    &ctx.executor,
                    &root_projection,
                    &schema,
                    root_addrs,
                    &searcher,
                )
            })?
            .into_iter()
            .enumerate()
            {
                root_docs[addr_to_idx[i]] = row;
            }

            trace_if(
                log::Level::Debug,
                &tracer,
                "Construct summary rows",
                |child| {
                    let doc_cows = root_docs
                        .iter()
                        .map(|v| Cow::Borrowed(v))
                        .collect::<Vec<_>>();

                    let comparison_keys = trace_if(
                        log::Level::Debug,
                        &child,
                        "Compute comparison keys",
                        |_child| {
                            Ok::<_, InterpreterError>(
                                interpret_expr_vec(
                                    &ctx.expr_ctx,
                                    comparison_key.as_ref(),
                                    doc_cows,
                                )?
                                .iter()
                                .map(|value| perform_sha256_hash(value.as_ref().clone()))
                                .collect::<Vec<_>>(),
                            )
                        },
                    )?;
                    let has_errors = root_docs
                        .iter()
                        .map(|doc| match doc.get("error").unwrap_or(&Value::Null) {
                            Value::Null => false,
                            _ => true,
                        })
                        .collect::<Vec<_>>();

                    // Dropping large `Value` objects can be very slow, and it's done synchronously
                    // here. By accumulating them and then dropping them in a background thread, we
                    // can save several seconds (around 80% of the time it takes to do the summary,
                    // or ~4s for a fairly large experiment).
                    let mut trash_can = Vec::new();

                    let unfiltered: Vec<Cow<Value>> =
                        izip!(docs, comparison_keys, has_errors, root_docs)
                            .map(|(row, comparison_key, has_error, root_doc)| {
                                trace_if(log::Level::Debug, &child, "Construct row", |child| {
                                    row.into_row(
                                        child,
                                        SummaryIntoRowInput {
                                            expr_ctx: &ctx.expr_ctx,
                                            comparison_key,
                                            root_doc,
                                            has_error,
                                            weighted_scores,
                                            custom_columns,
                                            preview_length: *preview_length,
                                        },
                                    )
                                })
                            })
                            .filter_map(|row| match row {
                                Ok((row, trash)) => {
                                    trash_can.extend(trash);
                                    Ok(row).transpose()
                                }
                                Err(e) => {
                                    return Some(Err(e));
                                }
                            })
                            .map(|row| Ok(Cow::Owned(row?)))
                            .collect::<Result<Vec<_>>>()?;

                    storage::drop_in_background(trash_can);

                    // We can't use perform_filter here because we need to compute cursor values of the filtered
                    // docs as well.
                    let rows_with_cursor_values = match post_aggregation_filter {
                        None => unfiltered
                            .into_iter()
                            .zip(new_root_span_ids)
                            .map(|(row, (_, cursor_value))| (row.into_owned(), cursor_value))
                            .collect::<Vec<_>>(),
                        Some(row_filter) => {
                            let filtered_rows =
                                interpret_expr_parallel(&ctx.expr_ctx, &row_filter, &unfiltered)?;

                            // These casts can technically fail, and if they do, we should just return false.
                            let filtered_bools: Vec<bool> = filtered_rows
                                .into_iter()
                                .map(|row| row.as_ref().cast().unwrap_or(false))
                                .collect::<Vec<_>>();

                            unfiltered
                                .into_iter()
                                .zip(filtered_bools)
                                .enumerate()
                                .filter(|(_, (_, matched))| *matched)
                                .map(|(i, (row, _))| (row.into_owned(), new_root_span_ids[i].1))
                                .collect::<Vec<_>>()
                        }
                    };

                    Ok::<_, InterpreterError>(rows_with_cursor_values)
                },
            )?
        }
    };

    Ok(docs)
}

#[derive(Debug, Clone)]
enum Index {
    Root(usize),
    Span(usize),
}

fn extract_expanded_rows<I: IntoIterator<Item = tantivy::DocAddress>>(
    tracer: Option<Arc<TracedNode>>,
    executor: &tantivy::Executor,
    is_root_projection: &Vec<TantivyProjectedField>,
    root_projection: &Vec<TantivyProjectedField>,
    root_is_columnar: bool,
    span_projection: &Vec<TantivyProjectedField>,
    span_is_columnar: bool,
    schema: &tantivy::schema::Schema,
    docs: I,
    searcher: &tantivy::Searcher,
) -> Result<Vec<Value>> {
    // First, extract the is_root field for each doc. Then, use the appropriate expansion mechanism for
    // the root and span projections.
    let docs = docs.into_iter().collect::<Vec<_>>();
    let mut indices = vec![Index::Root(0); docs.len()];

    let root_values = trace_if(
        log::Level::Info,
        &tracer,
        "Retrieve is_root columnar values",
        |child| {
            extract_docs_into_rows_columnar(
                child,
                executor,
                is_root_projection,
                docs.clone().into_iter(), // This is cheap -- it's just a vec of numbers
                searcher,
            )
        },
    )?;

    let mut root_docs = Vec::new();
    let mut span_docs = Vec::new();
    for (i, (root_value, doc)) in root_values.iter().zip(docs.into_iter()).enumerate() {
        let is_root = CastInto::<bool>::cast(
            root_value
                .as_object()
                .unwrap()
                .get(&*is_root_projection[0].alias)
                .unwrap(),
        )?;
        if is_root {
            root_docs.push(doc);
            indices[i] = Index::Root(root_docs.len() - 1);
        } else {
            span_docs.push(doc);
            indices[i] = Index::Span(span_docs.len() - 1);
        }
    }

    let mut root_rows = if root_is_columnar {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve root columnar documents",
            |child| {
                child.increment_counter("num_root_spans", root_docs.len() as u64);
                extract_docs_into_rows_columnar(
                    child,
                    executor,
                    root_projection,
                    root_docs,
                    searcher,
                )
            },
        )?
    } else {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve root full documents",
            |child| {
                child.increment_counter("num_root_spans", root_docs.len() as u64);
                extract_docs_into_rows(
                    child,
                    executor,
                    root_projection,
                    schema,
                    root_docs,
                    searcher,
                )
            },
        )?
    };

    let mut span_rows = if span_is_columnar {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve span columnar documents",
            |child| {
                child.increment_counter("num_spans", span_docs.len() as u64);
                extract_docs_into_rows_columnar(
                    child,
                    executor,
                    span_projection,
                    span_docs,
                    searcher,
                )
            },
        )?
    } else {
        trace_if(
            log::Level::Info,
            &tracer,
            "Retrieve span full documents",
            |child| {
                child.increment_counter("num_spans", span_docs.len() as u64);
                extract_docs_into_rows(
                    child,
                    executor,
                    span_projection,
                    schema,
                    span_docs,
                    searcher,
                )
            },
        )?
    };

    let mut rows = Vec::new();
    for idx in indices {
        match idx {
            Index::Root(i) => {
                rows.push(std::mem::replace(&mut root_rows[i], Value::Null));
            }
            Index::Span(i) => {
                rows.push(std::mem::replace(&mut span_rows[i], Value::Null));
            }
        }
    }

    Ok(rows)
}

fn extract_cursor_values_from_rows<'a>(
    docs: impl IntoIterator<Item = &'a Cow<'a, Value>>,
    cursor_field: CursorField,
) -> Vec<Option<CursorValue>> {
    docs.into_iter()
        .map(|doc| extract_cursor_value_from_row(doc, cursor_field, false).ok())
        .collect::<Vec<_>>()
}

pub fn truncate_with_cursor<T>(rows: &mut Vec<(T, Option<CursorValue>)>, limit: usize) {
    if limit == 0 {
        rows.truncate(0);
        return;
    } else if limit > rows.len() {
        return;
    }

    let mut truncation_idx = limit;
    let truncation_cursor = rows[truncation_idx - 1].1;

    while truncation_idx < rows.len() && rows[truncation_idx].1 == truncation_cursor {
        truncation_idx += 1;
    }

    rows.truncate(truncation_idx);
}
