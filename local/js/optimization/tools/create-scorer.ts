import { Tool } from "./types";
import { z } from "zod";

export const PLACEHOLDER_TS = `// Enter handler function that returns a numeric score between 0 and 1,
// or null to skip scoring
function handler({
  input,
  output,
  expected,
  metadata,
}: {
  input: any;
  output: any;
  expected: any;
  metadata: Record<string, any>;
}): {score: number | null, name: string, metadata: Record<string, any>} {
  return {
    score: expected === null ? null : output === expected ? 1 : 0,
    name: "My scorer",
    metadata: {
      stringEquality: output.toString() === expected.toString(),
    },
  };
}`;

export const PLACEHOLDER_PY = `from typing import Any

# Enter handler function that returns a numeric score between 0 and 1,
# or None to skip scoring
def handler(input: Any, output: Any, expected: Any, metadata: dict[str, Any]):
    return {
        "score": 1.0 if output == expected else 0.0 if expected is not None else None,
        "name": "My scorer",
        "metadata": {
            "stringEquality": str(output) == str(expected),
        },
    }`;

export const createCodeScorerToolResultSchema = z.object({
  ok: z.boolean(),
});
export type CreateCodeScorerToolResult = z.infer<
  typeof createCodeScorerToolResultSchema
>;

export const createCodeScorerParamsSchema = z.object({
  id: z
    .string()
    .optional()
    .describe("Optionally specify the id of an existing scorer to edit it"),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
  runtime: z.enum(["typescript", "python"]),
  code: z.string().describe("The code of the scorer."),
});
export type CreateCodeScorerParams = z.infer<
  typeof createCodeScorerParamsSchema
>;

export const CreateCodeScorerTool: Tool<
  CreateCodeScorerParams,
  CreateCodeScorerToolResult
> = {
  parameters: createCodeScorerParamsSchema,
  description: `Create a new, or edit an existing, scorer written in code. Code scorers are useful for tasks that
require heuristic checks (like comparing strings or validating JSON data), or for very complex
LLM-based scorers that require creating dynamic prompts and parsing them (in which case, you should
use the openai library in Python). You should only create scorers if explicitly asked by the user or
the functionality is not already covered by an existing scorer from get_available_scorers().

DO NOT try to create a code scorer without a very precise understanding of the shape of the user's data. Use
the get_results tool to get a sample of the data.

You can either write a scorer in Typescript or Python. A scorer is a function that takes input, output, expected,
and metadata as arguments, and returns a score object which contains a name (should be the same as the function
name), metadata (which can contain information useful for debugging), and a score, which is a number between 0 and 1.

Here are some examples of scorers. The function must always be named "handler".

\`\`\`typescript
${PLACEHOLDER_TS}
\`\`\`

and an example Python function:

\`\`\`python
${PLACEHOLDER_PY}
\`\`\`

Here are some rules for writing scorers:
- It is generally better to write a concise scorer that is simpler rather than overly complex and comprehensive. A user will
ask you if they need it to cover more cases or add more complex logic.
- If you are unsure of the shape of the data, use the get_results tool to get a sample of the data.
- In Typescript, you cannot import any libraries. In Python, you can import: openai, anthropic, requests, and any of the
built-in libraries.
- If you are editing an existing scorer, use the get_available_scorers tool to get the id of the scorer you want to edit.`,
};
