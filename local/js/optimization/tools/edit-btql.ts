import { z } from "zod";
import { type Tool } from "./types";

//This whole tool is just a placeholder really -- please feel free to change anything.
//UI can be modified to suit the optimal tool implementation.
export const editBTQLToolParams = z.object({
  query: z.string({
    description: "The generated full BTQL query",
  }),
  filter: z
    .array(
      z.string({
        description: "The filter clause of the generated BTQL query",
      }),
    )
    .nullish(),
  sort: z
    .array(
      z.string({
        description: "The sort clause of the generated BTQL query",
      }),
    )
    .nullish(),
});
export type EditBTQLToolParameters = z.infer<typeof editBTQLToolParams>;

export const editBTQLToolResultSchema = z.object({
  ok: z.boolean(),
});
export type EditBTQLToolResult = z.infer<typeof editBTQLToolResultSchema>;

export const EditBTQLTool: Tool<EditBTQLToolParameters, EditBTQLToolResult> = {
  description:
    "Edit a full BTQL query or the filter condition within a BTQL query",
  parameters: editBTQLToolParams,
};
