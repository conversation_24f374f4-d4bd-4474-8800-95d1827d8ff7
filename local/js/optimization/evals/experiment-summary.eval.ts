import { current<PERSON>pan, Eval, init, NOOP_SPAN, Span } from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import { makeEvalChat, SpanChatLogger } from "./eval-vars";
import {
  scoreSummaryCompleteness,
  scoreSummaryConciseness,
  scoreToolUsage,
  SummaryQualityClassifier,
} from "./summarize-scorer";
import { EVALS } from "./tasks";

Eval(PROJECT_NAME, {
  data: [
    {
      input: "Give me a summary",
    },
    {
      input: "Find an insight her",
    },
    {
      input: "Can you summarize the experiment?",
    },
    {
      input: "What is the best way to summarize the experiment?",
    },
    {
      input: "Can you give me a summary of the experiment?",
    },
    {
      input: "Can you give me a detailed breakdown of the experiment?",
    },
    {
      input: "Can you give me a breakdown of the experiment?",
    },
  ],
  task: async (input) => {
    console.log("input", input);
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;
    try {
      /*  const initialExperiment = initExperiment(
         "Optimization evals eval logs",
         {
           experiment: "exp-control",
         },
       ); */
      // Create readonly experiment for makeEvalChat (needs asDataset() method)

      // Create regular experiment for getting summary data (has summarize() method)
      const experiment = init({
        project: "Optimization evals eval logs",
        experiment: "exp-control",
        update: true,
      });
      console.log("experiment", await experiment.summarize());
      // Create the chat and tools first
      const {
        chat,
        consoleLogger: cl,
        rawOutput: ro,
      } = await makeEvalChat("movie-matcher", {
        allowed_tools: ["get_summary", "get_results", "continue_execution"],
        page: "experiment",
        initialExperiment: experiment,
      });

      consoleLogger = cl;
      rawOutput = ro;

      /*  // Run the experiment through the tools so they have access to the data
       const experimentResult = await traced(
         async (span) => {
           const ret = await tools.tools.run_task(
             {
               index: 0,
             },
             span,
           );
           span.log({ output: ret });
           return ret;
         },
         {
           name: "run_experiment_for_summary",
         },
       );

       console.log("experimentResult", experimentResult); */

      // Get experiment data from the completed experiment
      const task = { ...EVALS["movie-matcher"] };

      const experimentSummary = await experiment.summarize(); // Use regular experiment for summary
      const experimentData = {
        taskName: task.name,
        scores: experimentSummary.scores,
        metrics: experimentSummary.metrics,
      };

      // Ask the model to summarize the experiment
      const turnResult = await chat.turn(input);

      const summary = turnResult.text || "";
      const { toolCallsByType: _, ...numericMetrics } = chat.metrics;
      currentSpan().log({
        metrics: numericMetrics,
      });

      return {
        expected: `This experiment is showing scores of ${Object.entries(
          experimentData.scores,
        )
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
          .map(([key, value]) => `${key}: ${(value as any).score ?? "N/A"}`) // Fixed: use .score instead of .avg
          .join(", ")}. And metrics of ${Object.entries(
          experimentData.metrics || {},
        ) // Added fallback for undefined metrics
          .map(
            ([key, value]) =>
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
              `${key}: ${typeof value === "object" && value !== null ? ((value as any).metric ?? JSON.stringify(value)) : value}`,
          )
          .join(", ")}.`,
        summary,
        metrics: chat.metrics,
        experimentData,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [
    scoreSummaryCompleteness,
    scoreSummaryConciseness,
    scoreToolUsage,
    (args) =>
      SummaryQualityClassifier({
        ...args,
        expected: args.output.expected,
        output: args.output.summary,
      }),
  ],
});
