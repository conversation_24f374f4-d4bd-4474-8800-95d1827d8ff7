# Braintrust Trace Extractor

A utility to extract complete trace hierarchies from Braintrust and clean them for LLM analysis by removing Braintrust-specific metadata while preserving essential LLM interaction data.

## Problem Solved

When analyzing Braintrust traces, you often encounter:
- Root spans contain all the information, but child spans don't have complete data
- Traces include Braintrust-specific prompts, tools, and app template injection
- You want to see just the LLM interactions for analysis by <PERSON> or other LLMs
- Need complete trace trees with proper parent-child relationships

This utility solves these issues by:
1. Using BTQL `traces` queries to get complete trace hierarchies
2. Intelligently filtering out Braintrust metadata while preserving essential data
3. Providing multiple export formats optimized for LLM analysis

## Installation

```bash
# Clone or download the files
# trace_extractor.py - Main utility
# trace_extractor_examples.py - Usage examples
# README_trace_extractor.md - This documentation

# Install dependencies
pip install requests
```

## Quick Start

### Set up environment
```bash
export BRAINTRUST_API_KEY="your-api-key"
```

### Extract a single trace
```bash
python trace_extractor.py PROJECT_ID --trace-id TRACE_ID --format conversation
```

### Extract multiple traces with filtering
```bash
python trace_extractor.py PROJECT_ID --filter "created > '2024-01-01'" --limit 10 --output traces.json
```

## Key Features

### Complete Trace Extraction
- Uses BTQL `traces` queries with `brainstore_default_traces: true` to get complete hierarchies
- Preserves parent-child relationships between spans
- Handles orphaned spans gracefully

### Intelligent Cleaning
- **Preserves**: LLM interactions, tool calls, user/assistant messages, timestamps, errors
- **Strips**: Braintrust metadata, internal prompts, app templates, evaluation data, scores
- **Configurable**: Fine-grained control over what to keep vs. remove

### Multiple Export Formats
- **JSON**: Full structured data
- **Conversation**: Human-readable conversation flows
- **Structured**: Organized JSON for programmatic analysis  
- **Minimal**: Only essential LLM interactions

## Configuration Options

```python
config = TraceExtractionConfig(
    # What to preserve
    preserve_llm_interactions=True,
    preserve_tool_calls=True,
    preserve_tool_inputs_outputs=True,
    preserve_user_messages=True,
    preserve_assistant_messages=True,
    preserve_system_messages=False,  # Often contains templates
    preserve_timestamps=True,
    preserve_errors=True,
    
    # What to strip
    strip_braintrust_metadata=True,
    strip_internal_prompts=True,
    strip_app_templates=True,
    strip_braintrust_tools=True,
    strip_eval_metadata=True,
    
    # Span filtering
    include_span_types={"llm", "tool", "task"},
    exclude_span_types={"score", "eval"}
)
```

## Usage Examples

### Basic Python Usage

```python
from trace_extractor import BraintrustTraceExtractor, TraceExtractionConfig

# Initialize
extractor = BraintrustTraceExtractor(api_key="your-key")

# Extract single trace
trace = extractor.extract_trace_by_id("project-id", "trace-id")

# Extract multiple traces
traces = extractor.extract_traces_by_filter(
    "project-id", 
    "created > '2024-01-01'", 
    limit=10
)

# Export for Claude analysis
conversation_text = extractor.export_for_claude_analysis(traces, "conversation")
```

### CLI Usage

```bash
# Basic extraction
python trace_extractor.py PROJECT_ID --trace-id TRACE_ID

# Filter recent traces
python trace_extractor.py PROJECT_ID --filter "created > '2024-01-01'" --limit 5

# Export in conversation format
python trace_extractor.py PROJECT_ID --trace-id TRACE_ID --format conversation

# Save minimal format for analysis
python trace_extractor.py PROJECT_ID --filter "error IS NOT NULL" \
  --format minimal --output error_traces.json

# Include system messages and Braintrust tools
python trace_extractor.py PROJECT_ID --trace-id TRACE_ID \
  --preserve-system --include-braintrust-tools
```

## Output Formats

### Conversation Format
```
=== TRACE 1 ===
Trace ID: abc123

🤖 LLM Call: chat_completion
  👤 User: What's the weather like?
  🤖 Assistant: I'll check the weather for you.

🔧 Tool Call: get_weather
  📥 Input: {"location": "current"}
  📤 Output: {"temp": 72, "condition": "sunny"}

🤖 LLM Call: chat_completion
  🤖 Assistant: It's 72°F and sunny today!
```

### Minimal Format
```json
{
  "format": "minimal_traces",
  "traces": [
    {
      "type": "llm",
      "conversation": [
        {"role": "user", "content": "What's the weather like?"},
        {"role": "assistant", "content": "I'll check the weather for you."}
      ],
      "children": [
        {
          "type": "tool",
          "tool_name": "get_weather",
          "input": {"location": "current"},
          "output": {"temp": 72, "condition": "sunny"}
        }
      ]
    }
  ]
}
```

## Common Use Cases

### 1. LLM Conversation Analysis
Extract only LLM interactions for prompt engineering analysis:

```python
config = TraceExtractionConfig(
    include_span_types={"llm"},
    preserve_system_messages=False,
    strip_app_templates=True
)
```

### 2. Tool Usage Analysis
Focus on tool calls and their success rates:

```python
config = TraceExtractionConfig(
    include_span_types={"tool"},
    preserve_tool_inputs_outputs=True,
    strip_braintrust_tools=True
)
```

### 3. Error Debugging
Keep all context for error analysis:

```python
config = TraceExtractionConfig(
    preserve_system_messages=True,
    strip_braintrust_metadata=False,
    preserve_errors=True
)
```

### 4. Claude Code Analysis
Export clean traces for Claude to analyze:

```bash
python trace_extractor.py PROJECT_ID --filter "created > '2024-01-01'" \
  --format conversation --output traces_for_claude.txt
```

## BTQL Filter Examples

```bash
# Recent traces
--filter "created > '2024-01-01T00:00:00Z'"

# Traces with errors
--filter "error IS NOT NULL"

# LLM spans only
--filter "span_attributes.type = 'llm'"

# Traces from specific time range
--filter "created BETWEEN '2024-01-01' AND '2024-01-02'"

# Traces with specific metadata
--filter "metadata LIKE '%gpt-4%'"
```

## Advanced Features

### Custom Span Filtering
```python
# Only include specific span types
config.include_span_types = {"llm", "tool"}

# Exclude evaluation spans
config.exclude_span_types = {"score", "eval", "feedback"}
```

### Template Detection
The utility automatically detects and filters out:
- Braintrust template variables (`{{variable}}`)
- Internal evaluation prompts
- App template injection
- Braintrust-specific tool calls

### Error Handling
- Gracefully handles missing parent spans
- Preserves error information when configured
- Continues processing even if individual spans fail

## Troubleshooting

### Common Issues

1. **"No trace found"**: Check that the trace ID is the root span ID, not a child span ID
2. **Empty results**: Verify your BTQL filter syntax and that traces exist in the time range
3. **Missing data**: Ensure you're using `traces` not `spans` in BTQL queries
4. **API errors**: Check your API key and project permissions

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

Feel free to extend the utility with additional:
- Export formats
- Cleaning rules
- Span type handlers
- Analysis functions

The code is designed to be modular and extensible.
