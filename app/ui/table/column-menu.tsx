import React, { memo, type PropsWithChildren, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import {
  ArrowDown10,
  ArrowDownAZ,
  ArrowUp10,
  ArrowUpDown,
  ArrowUpZA,
  ChevronDown,
  EyeOff,
  ListFilter,
  PencilLineIcon,
  Trash,
} from "lucide-react";
import { type ScoreSummaryExperiment } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type ScoreSummary } from "@braintrust/local/query";
import { ExperimentColorSwatch } from "#/ui/charts/colors";
import { type SortComparison } from "#/utils/search/search";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";

export const ColumnMenu = memo(
  ({
    isReadOnly,
    isNumeric,
    onClickAsc,
    onClickDesc,
    onFilter,
    onHide,
    onDelete,
    onEdit,
    onEditTask,
    summaryData,
    onComparisonSort,
    groupBy,
    isSortable,
    children,
  }: PropsWithChildren<{
    isReadOnly?: boolean;
    isNumeric?: boolean;
    onClickAsc?: VoidFunction;
    onClickDesc?: VoidFunction;
    onFilter?: VoidFunction;
    onHide?: VoidFunction;
    onDelete?: VoidFunction;
    onEdit?: VoidFunction;
    onEditTask?: VoidFunction;
    summaryData?:
      | { scores?: ScoreSummary; experiment: ScoreSummaryExperiment }[]
      | null;
    onComparisonSort?: (comparison: SortComparison, desc: boolean) => void;
    groupBy?: string;
    isSortable?: boolean;
  }>) => {
    const [open, setOpen] = useState(false);

    const comparisonOptions = [
      ...(isNumeric && groupBy && groupBy !== GROUP_BY_NONE_VALUE
        ? [
            {
              type: "regression" as const,
              label: "Regressions per group",
            },
          ]
        : []),
      {
        type: "value" as const,
        label: "Value",
      },
      ...(isNumeric
        ? [
            {
              type: "score" as const,
              label: "Difference",
            },
          ]
        : []),
    ];

    const iconClassName = "size-3 flex-none";

    return (
      <div className="pointer-events-auto absolute inset-y-0 right-1 z-20 flex items-start pt-0.5 opacity-0 transition-opacity group-hover:opacity-100">
        <DropdownMenu open={open} onOpenChange={(o) => setOpen(o)}>
          <DropdownMenuTrigger asChild>
            <Button
              size="icon"
              variant="default"
              className="size-5"
              Icon={ChevronDown}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              {children}
              {onEditTask && !isReadOnly && (
                <DropdownMenuItem
                  onClick={() => {
                    onEditTask();
                    setOpen(false);
                  }}
                >
                  <PencilLineIcon className={iconClassName} />
                  Edit task
                </DropdownMenuItem>
              )}
              {isSortable && (
                <>
                  <DropdownMenuItem
                    disabled={isReadOnly}
                    onClick={() => {
                      onClickAsc?.();
                      setOpen(false);
                    }}
                  >
                    {isNumeric ? (
                      <ArrowDown10 className={iconClassName} />
                    ) : (
                      <ArrowDownAZ className={iconClassName} />
                    )}
                    Sort ascending{isNumeric ? "" : " (A→Z)"}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    disabled={isReadOnly}
                    onClick={() => {
                      onClickDesc?.();
                      setOpen(false);
                    }}
                  >
                    {isNumeric ? (
                      <ArrowUp10 className={iconClassName} />
                    ) : (
                      <ArrowUpZA className={iconClassName} />
                    )}
                    Sort descending{isNumeric ? "" : " (Z→A)"}
                  </DropdownMenuItem>
                </>
              )}
              {summaryData &&
              summaryData.length > 1 &&
              onComparisonSort &&
              comparisonOptions.length ? (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger disabled={isReadOnly}>
                    <ArrowUpDown className={iconClassName} />
                    Sort by comparison
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent>
                    {summaryData.slice(1).map((s, i) => (
                      <DropdownMenuSub key={i}>
                        <DropdownMenuSubTrigger>
                          <ExperimentColorSwatch index={i + 1}>
                            {s.experiment.name}
                          </ExperimentColorSwatch>
                        </DropdownMenuSubTrigger>
                        <DropdownMenuSubContent>
                          {comparisonOptions.map(({ type, label }) => {
                            return (
                              <React.Fragment key={type}>
                                <DropdownMenuItem
                                  onClick={() => {
                                    onComparisonSort(
                                      {
                                        experimentId: s.experiment.id,
                                        type,
                                      },
                                      false,
                                    );
                                    setOpen(false);
                                  }}
                                >
                                  {label}
                                  {" ascending"}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    onComparisonSort(
                                      {
                                        experimentId: s.experiment.id,
                                        type,
                                      },
                                      true,
                                    );
                                    setOpen(false);
                                  }}
                                >
                                  {label}
                                  {" descending"}
                                </DropdownMenuItem>
                              </React.Fragment>
                            );
                          })}
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>
                    ))}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              ) : null}
              {onFilter && (
                <DropdownMenuItem
                  disabled={isReadOnly}
                  onClick={() => {
                    onFilter();
                    setOpen(false);
                  }}
                >
                  <ListFilter className={iconClassName} />
                  Filter by this column
                </DropdownMenuItem>
              )}
              {onHide && (
                <DropdownMenuItem
                  onClick={() => {
                    onHide();
                    setOpen(false);
                  }}
                >
                  <EyeOff className={iconClassName} />
                  Hide column
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem
                  disabled={isReadOnly}
                  onClick={() => {
                    onEdit();
                    setOpen(false);
                  }}
                >
                  <PencilLineIcon className={iconClassName} />
                  Edit column
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  disabled={isReadOnly}
                  onClick={() => {
                    onDelete();
                    setOpen(false);
                  }}
                >
                  <Trash className={iconClassName} />
                  Delete column
                </DropdownMenuItem>
              )}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  },
);

ColumnMenu.displayName = "ColumnMenu";
