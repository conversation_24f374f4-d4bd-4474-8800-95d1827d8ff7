import { type ScaleLinear } from "d3";
import * as d3 from "d3";
import { type TimeseriesData } from "#/ui/charts/timeseries-data/chart-data.types";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import { cn } from "#/utils/classnames";
import {
  type HighlightState,
  isLineHighlighted,
  isLineSelected,
} from "#/ui/charts/highlight";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { type CSSProperties, useMemo } from "react";
import { getPointSVG } from "./gen-point-svg";

interface VizLinesProps<P, S> {
  style: CSSProperties;
  className: string;
  timeseriesData: TimeseriesData<P, S>;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  highlightState: HighlightState;
  nearestPoint: { seriesIndex: number; timeIndex: number } | null;
  clipPathId: string;
}

const toD3Points = (seriesValues: Float64Array, timestamps: number[]) => {
  const d3Points: { x: number; y: number; timeIndex: number }[] = [];
  for (let i = 0; i < timestamps.length; i++) {
    const y = seriesValues[i];
    const x = timestamps[i];
    if (!Number.isFinite(y)) {
      continue;
    }
    d3Points.push({
      x,
      y,
      timeIndex: i,
    });
  }
  return d3Points;
};

export const VizLines = <P, S extends { selectionType: SelectionType }>(
  props: VizLinesProps<P, S>,
) => {
  const {
    style,
    className,
    xScale,
    yScale,
    timeseriesData,
    highlightState,
    nearestPoint,
    clipPathId,
  } = props;

  const { timestamps, seriesMetadata } = timeseriesData;

  const { seriesIndex: nearestSeriesIndex, timeIndex: nearestTimeIndex } =
    nearestPoint ?? { seriesIndex: -1, timeIndex: -1 };

  const lineGenerator = useMemo(
    () =>
      d3
        .line<{ x: number; y: number }>()
        .x((d) => xScale(d.x))
        .y((d) => yScale(d.y))
        .curve(d3.curveMonotoneX),
    [xScale, yScale],
  );

  const seriesPoints = useMemo(() => {
    return timeseriesData.seriesValues.map((series) => {
      return toD3Points(series, timestamps);
    });
  }, [timeseriesData.seriesValues, timestamps]);

  const seriesSVGPath = useMemo(() => {
    return seriesPoints.map((point) => lineGenerator(point));
  }, [seriesPoints, lineGenerator]);

  // every series svg
  const baseSeriesPathsSVG = useMemo(() => {
    return seriesSVGPath.map((path, i) => {
      const colorClassName = COLOR_CLASSNAMES[i];
      const lineClassName = cn(DEFAULT_COLOR_CLASSNAME, colorClassName);
      return (
        <g clipPath={`url(#${clipPathId})`} key={i}>
          <path
            className={cn(lineClassName, "stroke-2 fill-none")}
            d={path ?? ""}
            strokeLinejoin="round"
          />
        </g>
      );
    });
  }, [seriesSVGPath, clipPathId]);

  // filter to only what's selected
  const selectedPathsSVG = useMemo(() => {
    return baseSeriesPathsSVG.filter((_, i) => {
      const s = seriesMetadata[i];
      return isLineSelected(s.selectionType, highlightState);
    });
  }, [baseSeriesPathsSVG, seriesMetadata, highlightState]);

  // all series points svg
  const baseSeriesPointsSVG = useMemo(() => {
    return seriesMetadata.map((_, seriesIndex) => {
      const points = seriesPoints[seriesIndex];
      const colorClassName = COLOR_CLASSNAMES[seriesIndex];
      const pointClassName = cn(DEFAULT_COLOR_CLASSNAME, colorClassName);

      return points.map((point) => {
        return getPointSVG({
          point,
          xScale,
          yScale,
          nearestTimeIndex: -1,
          isSeriesNearest: false,
          className: pointClassName,
        });
      });
    });
  }, [seriesMetadata, seriesPoints, xScale, yScale]);

  // mask to only what's selected and highlighted
  const highlightedMask = useMemo(() => {
    return seriesMetadata.map((s, i) => {
      if (!isLineSelected(s.selectionType, highlightState)) {
        return false;
      }
      if (nearestSeriesIndex === i) {
        return true;
      }
      return (
        Boolean(highlightState && highlightState.highlighted) &&
        isLineHighlighted(s.selectionType, highlightState)
      );
    });
  }, [seriesMetadata, highlightState, nearestSeriesIndex]);

  const selectedBasePointsSVG = useMemo(() => {
    return baseSeriesPointsSVG.filter((_, i) => {
      const s = seriesMetadata[i];
      return isLineSelected(s.selectionType, highlightState);
    });
  }, [baseSeriesPointsSVG, seriesMetadata, highlightState]);

  const baseSeriesSVG = useMemo(() => {
    return seriesMetadata
      .map((_, index) => {
        if (!selectedPathsSVG[index]) {
          return null;
        }
        return (
          <g className="pointer-events-none" key={`viz-line-${index}`}>
            {selectedPathsSVG[index]}
            <g>{selectedBasePointsSVG[index]}</g>
          </g>
        );
      })
      .toReversed();
  }, [seriesMetadata, selectedPathsSVG, selectedBasePointsSVG]);

  // separate highlight points because points can be different style for nearest point
  const highlightPointsSVG = useMemo(() => {
    return seriesMetadata.map((s, index) => {
      if (!highlightedMask[index]) {
        return null;
      }

      const colorClassName = COLOR_CLASSNAMES[index];
      const pointClassName = cn(DEFAULT_COLOR_CLASSNAME, colorClassName);
      const points = seriesPoints[index];
      const isSeriesNearest = index === nearestSeriesIndex;

      return points.map((point) => {
        return getPointSVG({
          point,
          xScale,
          yScale,
          nearestTimeIndex,
          isSeriesNearest,
          className: pointClassName,
        });
      });
    });
  }, [
    seriesMetadata,
    seriesPoints,
    nearestTimeIndex,
    nearestSeriesIndex,
    highlightedMask,
    xScale,
    yScale,
  ]);

  const highlightSeriesSVG = useMemo(() => {
    return seriesMetadata
      .map((_, index) => {
        if (!highlightedMask[index]) {
          return null;
        }
        return (
          <g className="pointer-events-none" key={`viz-line-${index}`}>
            {baseSeriesPathsSVG[index]}
            <g>{highlightPointsSVG[index]}</g>
          </g>
        );
      })
      .toReversed();
  }, [seriesMetadata, baseSeriesPathsSVG, highlightedMask, highlightPointsSVG]);

  const inVizHighlightState =
    Boolean(highlightState && highlightState.highlighted) ||
    nearestTimeIndex >= 0;

  return useMemo(() => {
    return (
      <>
        <div
          style={style}
          key="base"
          className={cn(className, "transition-opacity opacity-100", {
            "opacity-30": inVizHighlightState,
          })}
        >
          <svg className="size-full">{baseSeriesSVG}</svg>
        </div>
        <div
          style={style}
          key="highlight"
          className={cn(className, "transition-opacity opacity-100", {
            "opacity-0": !inVizHighlightState,
          })}
        >
          <svg className="size-full">{highlightSeriesSVG}</svg>
        </div>
      </>
    );
  }, [
    baseSeriesSVG,
    highlightSeriesSVG,
    style,
    className,
    inVizHighlightState,
  ]);
};
