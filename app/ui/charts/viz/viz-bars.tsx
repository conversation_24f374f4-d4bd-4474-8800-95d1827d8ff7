import { type ScaleLinear } from "d3";
import { type TimeseriesData } from "#/ui/charts/timeseries-data/chart-data.types";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import {
  type HighlightState,
  isLineHighlighted,
  isLineSelected,
} from "#/ui/charts/highlight";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { type CSSProperties, Fragment, useMemo } from "react";
import type { StackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";
import { cn } from "#/utils/classnames";
import { genSeriesBarPaths, genStackTotalRect } from "./gen-series-bar-paths";

interface VizBarsProps<P, S> {
  style: CSSProperties;
  className: string;
  timeseriesData: TimeseriesData<P, S>;
  stackedData: StackedTimeBuckets;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  highlightState: HighlightState;
  nearestPoint: {
    seriesIndex: number;
    timeIndex: number;
    isStackedTotal?: boolean;
  } | null;
  clipPathId: string;
}

export const VizBars = <P, S extends { selectionType: SelectionType }>(
  props: VizBarsProps<P, S>,
) => {
  const {
    style,
    className,
    xScale,
    yScale,
    timeseriesData,
    highlightState,
    nearestPoint,
    clipPathId,
    stackedData,
  } = props;

  const { seriesMetadata } = timeseriesData;

  const {
    seriesIndex: nearestSeriesIndex,
    timeIndex: nearestTimeIndex,
    isStackedTotal,
  } = nearestPoint ?? { seriesIndex: -1, timeIndex: -1, isStackedTotal: false };

  const seriesPaths = useMemo(() => {
    return seriesMetadata.map((_, i) => {
      return genSeriesBarPaths({ stackedData, seriesIndex: i, xScale, yScale });
    });
  }, [stackedData, seriesMetadata, xScale, yScale]);

  const seriesClassNames = useMemo(() => {
    return seriesMetadata.map((s, seriesIndex) => {
      const colorClassName = COLOR_CLASSNAMES[seriesIndex];
      const lineClassName = cn(DEFAULT_COLOR_CLASSNAME, colorClassName);
      return lineClassName;
    });
  }, [seriesMetadata]);

  const baseSeriesSVG = useMemo(() => {
    return seriesPaths.map((paths, i) => {
      return (
        <Fragment key={i}>
          <g className={cn(seriesClassNames[i], "stroke-none")}>
            {paths.map((path, j) => (
              <path style={{ filter: "opacity(60%)" }} d={path ?? ""} key={j} />
            ))}
          </g>
          <g className={cn(seriesClassNames[i], "stroke-1 fill-none")}>
            {paths.map((path, j) => (
              <path
                d={path ?? ""}
                strokeLinejoin="miter"
                style={{ filter: "opacity(60%)" }}
                key={j}
              />
            ))}
          </g>
        </Fragment>
      );
    });
  }, [seriesPaths, seriesClassNames]);

  const selectedBaseSeriesSVG = useMemo(() => {
    return baseSeriesSVG
      .filter((_, i) => {
        const s = seriesMetadata[i];
        return isLineSelected(s.selectionType, highlightState);
      })
      .toReversed();
  }, [baseSeriesSVG, seriesMetadata, highlightState]);

  const highlightSeriesSVG = useMemo(() => {
    return baseSeriesSVG
      .filter((_, i) => {
        const s = seriesMetadata[i];
        if (!isLineSelected(s.selectionType, highlightState)) {
          return false;
        }

        const isNearestSeries = nearestSeriesIndex === i;
        const nearestHighlight = isNearestSeries;
        if (nearestHighlight) {
          return true;
        }

        const isHighlighted =
          Boolean(highlightState && highlightState.highlighted) &&
          isLineHighlighted(s.selectionType, highlightState);

        return isHighlighted;
      })
      .toReversed();
  }, [baseSeriesSVG, seriesMetadata, nearestSeriesIndex, highlightState]);

  const stackedTotalRect = useMemo(() => {
    if (!isStackedTotal) {
      return null;
    }

    const rect = genStackTotalRect({
      stackedData,
      timeIndex: nearestTimeIndex,
      xScale,
      yScale,
    });
    if (!rect) {
      return null;
    }

    const { x, y, width, height } = rect;
    return (
      <g className={cn(DEFAULT_COLOR_CLASSNAME, "fill opacity-50")}>
        <rect x={x} y={y} width={width} height={height} />
      </g>
    );
  }, [stackedData, nearestTimeIndex, xScale, yScale, isStackedTotal]);

  const inVizHighlightState =
    Boolean(
      highlightState &&
        highlightState.highlighted &&
        highlightState.highlighted.groupVal,
    ) || nearestTimeIndex >= 0;

  return useMemo(() => {
    return (
      <>
        <div
          style={style}
          key="base"
          className={cn(className, "transition-opacity opacity-100", {
            "opacity-30": inVizHighlightState,
          })}
        >
          <svg className="size-full">
            <g className="pointer-events-none" clipPath={`url(#${clipPathId})`}>
              {selectedBaseSeriesSVG}
            </g>
          </svg>
        </div>
        <div
          style={style}
          key="highlight"
          className={cn(className, "transition-opacity opacity-100", {
            "opacity-0": !inVizHighlightState,
          })}
        >
          <svg className="size-full">
            <g className="pointer-events-none" clipPath={`url(#${clipPathId})`}>
              {isStackedTotal ? stackedTotalRect : highlightSeriesSVG}
            </g>
          </svg>
        </div>
      </>
    );
  }, [
    selectedBaseSeriesSVG,
    highlightSeriesSVG,
    style,
    className,
    clipPathId,
    inVizHighlightState,
    isStackedTotal,
    stackedTotalRect,
  ]);
};
