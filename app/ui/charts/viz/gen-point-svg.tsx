import { cn } from "#/utils/classnames";
import type { ScaleLinear } from "d3";
import * as d3 from "d3";

interface GetPointSVGProps {
  className: string;
  point: { x: number; y: number; timeIndex: number };
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  nearestTimeIndex: number;
  isSeriesNearest: boolean;
}

const circlePath = d3.symbol(d3.symbolsFill[0])() ?? "";

export const getPointSVG = ({
  className,
  point,
  xScale,
  yScale,
  nearestTimeIndex,
  isSeriesNearest,
}: GetPointSVGProps) => {
  if (point == null) {
    return null;
  }

  const x = xScale(point.x);
  const y = yScale(point.y);

  // exclude points out of viz dimensions
  const rangeX = xScale.range();
  const minX = Math.min(rangeX[0], rangeX[1]);
  const maxX = Math.max(rangeX[0], rangeX[1]);
  if (x < minX || x > maxX) {
    return;
  }

  const rangeY = yScale.range();
  const minY = Math.min(rangeY[0], rangeY[1]);
  const maxY = Math.max(rangeY[0], rangeY[1]);
  if (y < minY || y > maxY) {
    return;
  }

  const isNearestPoint =
    point.timeIndex === nearestTimeIndex && isSeriesNearest;

  // todo other symbol support - this is inlined ChartSymbol for perf
  const size = (isNearestPoint ? Math.max(4 + 5, 8) : 4) + 8;
  return (
    <g
      className={cn(className, "stroke-[3] stroke-background", {
        "stroke-[4]": isNearestPoint,
      })}
      transform={`translate(${x}, ${y}) scale(${size / 20})`}
      style={{ paintOrder: "stroke" }}
      key={point.timeIndex}
    >
      <path d={circlePath} />
    </g>
  );
};
