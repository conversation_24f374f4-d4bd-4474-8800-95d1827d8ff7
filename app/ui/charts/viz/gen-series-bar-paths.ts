import { type ScaleLinear } from "d3";
import { type StackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";

interface GenSeriesBarPathsProps {
  stackedData: StackedTimeBuckets;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  seriesIndex: number;
}

interface PathPoint {
  x: number;
  y: number;
  yBottom: number;
}

export const genSeriesBarPaths = (props: GenSeriesBarPathsProps) => {
  const { stackedData, xScale, yScale, seriesIndex } = props;
  const {
    timestamps,
    bucketDuration,
    seriesStackedSum,
    seriesIndexToStackedIndex,
  } = stackedData;

  const paths: string[] = [];
  let currentTopPath: PathPoint[] = [];

  // at end of continuous path, generate an svg path string from points
  const endPath = () => {
    if (currentTopPath.length === 0) {
      return;
    }
    const path = genSVGPath(currentTopPath, yScale);
    if (path) {
      paths.push(path);
      currentTopPath = [];
    }
  };

  // add a time slice value to the current path
  const pushPath = (x1: number, x2: number, yTop: number, yBottom: number) => {
    currentTopPath.push({ x: x1, y: yTop, yBottom });
    currentTopPath.push({ x: x2, y: yTop, yBottom });
  };

  for (let j = 0; j < timestamps.length; j++) {
    const start = timestamps[j];
    const end = start + bucketDuration;
    const x1 = xScale(start);
    const x2 = xScale(end);

    const stackedBucket = seriesStackedSum[j];
    const stackedIdx = seriesIndexToStackedIndex[j][seriesIndex];

    // end and bail if no current stacked value
    if (stackedIdx === undefined) {
      endPath();
      continue;
    }

    const bottomValue = stackedBucket[stackedIdx - 1] ?? 0;
    const topValue = stackedBucket[stackedIdx];

    const y = yScale(topValue);
    const yBottom = yScale(bottomValue);

    // end if disjoint, but keep going
    const prevPoint = currentTopPath.at(-1);
    if (prevPoint && (prevPoint.y > yBottom || prevPoint.yBottom < y)) {
      endPath();
    }

    pushPath(x1, x2, y, yBottom);

    // end path if next bin is not very close to current bin end
    if (j < timestamps.length - 1 && Math.abs(timestamps[j + 1] - end) > 0.01) {
      endPath();
      continue;
    }
  }
  endPath();
  return paths;
};

interface GetStackTotalPathProps {
  stackedData: StackedTimeBuckets;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  timeIndex: number;
}

export const genStackTotalRect = ({
  stackedData,
  xScale,
  yScale,
  timeIndex,
}: GetStackTotalPathProps) => {
  const { timestamps, seriesStackedSum, bucketDuration } = stackedData;
  const start = timestamps[timeIndex];
  const end = start + bucketDuration;
  const stackTotal = seriesStackedSum[timeIndex]?.at(-1);
  if (!stackTotal) {
    return null;
  }
  return {
    x: xScale(start),
    width: xScale(end) - xScale(start),
    y: yScale(stackTotal),
    height: yScale(0) - yScale(stackTotal),
  };
};

const genSVGPath = (
  topPoints: PathPoint[],
  yScale: ScaleLinear<number, number>,
) => {
  if (topPoints.length === 0) {
    return "";
  }

  const completePath = topPoints.map((p) => ({ x: p.x, y: p.y }));

  for (let i = topPoints.length - 1; i >= 0; i--) {
    const { x, yBottom } = topPoints[i];
    completePath.push({
      x,
      y: yBottom,
    });
  }

  const y0 = yScale(0);

  return completePath
    .map((p, i) => {
      const { x, y } = p;
      const pointString = `${x} ${y >= y0 ? y0 + 2 : y}`;
      if (i === 0) {
        return `M ${pointString}`;
      }
      if (i === completePath.length - 1) {
        return `L ${pointString} Z`;
      }
      return `L ${pointString}`;
    })
    .join(" ");
};
