import { useMemo, useState } from "react";

import {
  type HighlightState,
  type HighlightedGroup,
} from "#/ui/charts/highlight";
import {
  type SelectionType,
  type SelectionTypesEnum,
} from "#/ui/charts/selectionTypes";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type TimeseriesAggregates } from "#/ui/charts/timeseries-data/chart-data.types";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

import { LEGEND_ENTRY_HEIGHT } from "./timeseries-legend.constants";
import { toLegendLabelData } from "./timeseries-legend-data";
import { TimeseriesLegendLabels } from "./timeseries-legend-labels";

export interface MonitorChartLegendProps {
  highlightState: HighlightState;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  clearSelected: () => void;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: () => void;
  seriesColorMap: Record<string, Record<SelectionTypesEnum, number>>;
  seriesAggregates: TimeseriesAggregates[];
  seriesMetadata: { selectionType: SelectionType; name: string }[];
  aggregateType: "sum" | "average";
  aggregateFormatter: (value: number) => string;
  height: number;
  width: number;
  vizType?: TimeseriesVizType;
  isStackedTotal?: boolean;
  nearestIndex?: number;
}

const MonitorChartLegend = ({
  highlightState,
  seriesColorMap,
  seriesAggregates,
  seriesMetadata,
  addSelected,
  removeSelected,
  clearSelected,
  setSelected,
  highlight,
  clearHighlight,
  aggregateType,
  aggregateFormatter,
  height,
  width,
  vizType = "lines",
  isStackedTotal,
  nearestIndex,
}: MonitorChartLegendProps) => {
  const linesWithData = Object.fromEntries(
    seriesMetadata.map((data) => [
      data.selectionType.value,
      data.selectionType.type,
    ]),
  );

  const [isLegendHovered, setIsLegendHovered] = useState<boolean>(false);

  const aggregatedValues = useMemo(() => {
    return seriesMetadata.map((data, i) => {
      const { sum, count } = seriesAggregates[i];
      if (aggregateType === "sum") {
        return {
          selectionType: data.selectionType,
          value: sum,
          formatted: aggregateFormatter(sum),
        };
      }

      if (count === 0) {
        return {
          selectionType: data.selectionType,
          value: 0,
        };
      }
      const average = sum / count;

      return {
        selectionType: data.selectionType,
        value: average,
        formatted: aggregateFormatter(average),
      };
    });
  }, [seriesMetadata, seriesAggregates, aggregateType, aggregateFormatter]);

  const totalSum = useMemo(() => {
    return seriesAggregates.reduce((acc, aggr) => acc + aggr.sum, 0);
  }, [seriesAggregates]);

  const valueMap = useMemo(() => {
    return Object.fromEntries(
      aggregatedValues.map(({ selectionType, value }) => [
        `${selectionType.value}:${selectionType.type}`,
        {
          value: value.toLocaleString(),
          formatted: aggregateFormatter(value),
        },
      ]),
    );
  }, [aggregatedValues, aggregateFormatter]);

  const legendLabelsData = toLegendLabelData(seriesColorMap, "points")
    .filter(
      (legendLabel) =>
        linesWithData[legendLabel.groupVal.value] === legendLabel.groupVal.type,
    )
    .map((legendLabel) => {
      const key = `${legendLabel.groupVal.value}:${legendLabel.groupVal.type}`;
      const aggregate = valueMap[key];

      return {
        ...legendLabel,
        label: legendLabel.groupVal.value,
        aggregatedValue: aggregate?.formatted ?? aggregate?.value,
      };
    });

  const itemsOffset = vizType === "bars" ? -1 : 0; // bars needs row on top for total
  const legendEntriesAvailable =
    Math.floor(height / LEGEND_ENTRY_HEIGHT) + itemsOffset;

  // default we have enough space to show all
  const numLegendLabels = legendLabelsData.length;
  const showShowNumMore = legendEntriesAvailable <= numLegendLabels - 1;

  // remember we need to cut off an extra row for 'show more' row
  const numMore = showShowNumMore
    ? Math.min(numLegendLabels, numLegendLabels - legendEntriesAvailable + 1)
    : 0;

  return (
    <Tooltip open={numMore > 0 ? undefined : false} delayDuration={50}>
      <TooltipTrigger asChild>
        <div
          className={cn("flex-none w-full")}
          style={{ height: `${height}px` }}
          onMouseEnter={() => setIsLegendHovered(true)}
          onMouseLeave={() => setIsLegendHovered(false)}
        >
          <TimeseriesLegendLabels
            legendLabelData={legendLabelsData.slice(
              0,
              numLegendLabels - numMore,
            )}
            highlightState={highlightState}
            addSelected={addSelected}
            removeSelected={removeSelected}
            setSelected={setSelected}
            highlight={highlight}
            clearHighlight={clearHighlight}
            clearSelected={clearSelected}
            isLegendHovered={isLegendHovered}
            vizType={vizType}
            totalSumString={aggregateFormatter(totalSum)}
            isStackedTotal={isStackedTotal}
            nearestIndex={nearestIndex}
            isTooltip={false}
          />
          {showShowNumMore && (
            <div className="pt-2 text-xs text-primary-500">{numMore} more</div>
          )}
        </div>
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          className="rounded-md pr-0 pt-0 shadow-md duration-0 bg-primary-50 data-[side=bottom]:duration-0"
          style={{ width: `${width + 22 + 4}px` }}
          side="bottom"
          align="end"
          alignOffset={-10 - 4 + 1}
          sideOffset={-height - 1}
          avoidCollisions={false}
          onMouseEnter={() => setIsLegendHovered(true)}
          onMouseLeave={() => setIsLegendHovered(false)}
        >
          <TimeseriesLegendLabels
            className="pr-3"
            legendLabelData={legendLabelsData}
            highlightState={highlightState}
            addSelected={addSelected}
            removeSelected={removeSelected}
            setSelected={setSelected}
            clearSelected={clearSelected}
            highlight={highlight}
            clearHighlight={clearHighlight}
            isLegendHovered={isLegendHovered}
            vizType={vizType}
            totalSumString={aggregateFormatter(totalSum)}
            isStackedTotal={isStackedTotal}
            nearestIndex={nearestIndex}
            isTooltip={true}
          />
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};

MonitorChartLegend.displayName = "MonitorChartLegend";
export { MonitorChartLegend };
