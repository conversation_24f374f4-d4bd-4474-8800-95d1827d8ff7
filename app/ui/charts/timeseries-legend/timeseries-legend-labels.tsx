import { useRef, type MouseEvent } from "react";
import { cn } from "#/utils/classnames";
import {
  type HighlightState,
  type HighlightedGroup,
  isLineHighlighted,
  isLineSelected,
} from "#/ui/charts/highlight";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

import { TimeseriesLegendLabel } from "./timeseries-legend-label";
import { type TimeseriesLegendLabelData } from "./timeseries-legend.types";
import { useVirtualizer } from "@tanstack/react-virtual";
import { LEGEND_ENTRY_HEIGHT } from "./timeseries-legend.constants";

export const TimeseriesLegendLabels = ({
  className,
  legendLabelData,
  highlightState,
  addSelected,
  removeSelected,
  clearSelected,
  setSelected,
  highlight,
  clearHighlight,
  isLegendHovered,
  vizType,
  totalSumString,
  nearestIndex,
  isStackedTotal,
  isTooltip,
}: {
  className?: string;
  legendLabelData: TimeseriesLegendLabelData[];
  highlightState: HighlightState;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  clearSelected: () => void;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: () => void;
  isLegendHovered: boolean;
  vizType: TimeseriesVizType;
  totalSumString: string;
  isStackedTotal?: boolean;
  nearestIndex?: number;
  isTooltip: boolean;
}) => {
  const inSelectedState = highlightState.selected.length > 0; // at least one series selected
  const totalHighlighted = isLegendHovered && !highlightState?.highlighted;
  const legendClassName = "pt-2 cursor-pointer select-none transition-opacity";
  const totalContentClassName = cn({
    "opacity-40":
      (isLegendHovered && !totalHighlighted) ||
      (inSelectedState && !totalHighlighted) ||
      (nearestIndex !== undefined && !isStackedTotal),
  });

  const scrollMargin = vizType === "bars" ? LEGEND_ENTRY_HEIGHT : 0;
  const containerRef = useRef<HTMLDivElement>(null);
  const virtualizer = useVirtualizer({
    count: legendLabelData.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => LEGEND_ENTRY_HEIGHT,
    scrollMargin,
    overscan: 4,
  });

  const topMargin =
    virtualizer.getVirtualItems().length === 0
      ? 0
      : virtualizer.getVirtualItems()[0].start - scrollMargin;
  const bottomMargin =
    virtualizer.getVirtualItems().length === 0
      ? 0
      : virtualizer.getTotalSize() -
        (virtualizer.getVirtualItems().at(-1)?.end ?? 0);

  const height = virtualizer.getTotalSize() + scrollMargin;
  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        height: `${height}px`,
        maxHeight: isTooltip
          ? `calc(max(${5 * LEGEND_ENTRY_HEIGHT}px, var(--radix-popper-available-height) - 32px))`
          : "100%",
        position: "relative",
        overflow: isTooltip ? "auto" : "none",
      }}
    >
      {vizType === "bars" && (
        <TimeseriesLegendLabel
          className={legendClassName}
          contentClassName={totalContentClassName}
          onClick={clearSelected}
          onMouseEnter={clearHighlight}
          vizType={vizType}
          groupVal={{
            type: "score",
            value: totalSumString,
          }}
          aggregatedValue={totalSumString}
          label="Total"
          type="points"
          colorIndex={-1}
        />
      )}

      {/* Top padding element */}
      {topMargin > 0 && <div style={{ height: `${topMargin}px` }} />}

      {virtualizer.getVirtualItems().map((virtualItem) => {
        const { index } = virtualItem;
        const line = legendLabelData[index];

        // if hover over legend, fade if not highlighted
        // else fade if not selected
        const isHighlighted = isLineHighlighted(line.groupVal, highlightState);
        const isSelected =
          inSelectedState && isLineSelected(line.groupVal, highlightState);
        const labelFade = isLegendHovered
          ? !isHighlighted && !isSelected
          : inSelectedState && !isSelected;
        const contentClassName = cn({
          "opacity-40":
            labelFade || (nearestIndex !== undefined && nearestIndex !== index),
        });

        return (
          <TimeseriesLegendLabel
            key={index}
            className={legendClassName}
            contentClassName={contentClassName}
            onClick={(event: MouseEvent) => {
              const v = {
                groupVal: line.groupVal,
                type: line.type,
              };

              if (event.ctrlKey || event.metaKey) {
                removeSelected(v);
              } else if (event.shiftKey) {
                addSelected(v);
              } else {
                setSelected(v);
              }
            }}
            onMouseEnter={() => {
              highlight({
                groupVal: line.groupVal,
                type: line.type,
              });
            }}
            onMouseLeave={() => {
              clearHighlight();
            }}
            vizType={vizType}
            {...line}
          />
        );
      })}

      {/* Bottom padding element */}
      {bottomMargin > 0 && <div style={{ height: `${bottomMargin}px` }} />}
    </div>
  );
};
