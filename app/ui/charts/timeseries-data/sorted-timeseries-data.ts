import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";
import { type TimeseriesData } from "./chart-data.types";

interface SeriesComparisonMeta {
  name: string;
  aggr: number;
  originalIndex: number;
}
const seriesComparator = (a: SeriesComparisonMeta, b: SeriesComparisonMeta) => {
  // first sort by value (larger first)
  if (a.aggr > b.aggr) {
    return -1;
  }
  if (a.aggr < b.aggr) {
    return 1;
  }

  // when values are equal, sort based on name (alpha)
  return a.name.localeCompare(b.name);
};

/**
 * Return shallow copy timeseries data that is series sorted by aggregate values
 * Uses sum for bars and true avg for lines
 */
export const sortedTimeseriesData = <T, S extends { name: string }>(
  timeseriesData: TimeseriesData<T, S & { name: string }>,
  vizType: TimeseriesVizType,
): TimeseriesData<T, S & { name: string }> => {
  const { aggregates } = timeseriesData;

  const { seriesMetadata, seriesValues, timeMetadata, timestamps } =
    timeseriesData;

  // create array of meta we need to sort on
  const compMeta = seriesMetadata.map((s, i) => {
    // sum for bars, 'true' avg for lines
    const { sum, count } = aggregates[i];
    const aggr = vizType === "bars" ? sum : sum / (count || 1);
    return {
      name: s.name,
      aggr,
      originalIndex: i,
    };
  });

  // sort and get array of new order indices
  compMeta.sort(seriesComparator);
  const newOrder = compMeta.map((m) => m.originalIndex);

  // return with series arrays remapped to new order
  return {
    seriesMetadata: newOrder.map((i) => seriesMetadata[i]),
    seriesValues: newOrder.map((i) => seriesValues[i]),
    aggregates: newOrder.map((i) => aggregates[i]),
    timeMetadata,
    timestamps,
    timeBucketDuration: timeseriesData.timeBucketDuration,
  };
};
