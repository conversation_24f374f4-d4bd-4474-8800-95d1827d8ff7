"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";

import {
  useEntityStorage,
  useEntityStorageAccess,
} from "#/lib/clientDataStorage";
import { apiFetchGet } from "#/utils/btapi/fetch";
import * as semver from "semver";
import { Button, buttonVariants } from "#/ui/button";
import { TriangleAlert } from "lucide-react";
import { WEBAPP_VERSION } from "./api-version";
import { MultiTenantApiURL } from "#/utils/user-types";
import Link from "next/link";
import { useOrg } from "#/utils/user";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { useSessionToken } from "#/utils/auth/session-token";
import { z } from "zod";
import { useWarmupCodeExecution } from "./warmup-code-execution";
import { useAnalytics } from "#/ui/use-analytics";
import { useQuery } from "@tanstack/react-query";

export const apiVersionSchema = z.object({
  version: z.string(),
  commit: z.string().optional(),
  deployment_mode: z.string().optional(),
  universal: z.boolean().default(false),
  code_execution: z.boolean().default(false),
  brainstore_default: z.boolean().default(false),
  brainstore_can_contain_row_refs: z.boolean().default(false),
  has_logs2: z.boolean().default(false),
});
export type APIVersionInfo = z.infer<typeof apiVersionSchema>;

// Timeout (in milliseconds) for the API version fetch.
// Keep this as a single source of truth so it can be reused both for the
// AbortController timeout and the resulting error message.
const API_VERSION_FETCH_TIMEOUT_MS = 10_000;

export function useAPIVersion() {
  const org = useOrg();
  // Get non-reactive access to the API version in local storage
  const { get, set } = useEntityStorageAccess<"org", "apiVersion3">({
    entityType: "org",
    entityIdentifier: org.id || "",
  });
  // Get the initial value of the API version from local storage
  const initialLocalApiVersion = useMemo(() => get("apiVersion3"), [get]);

  const { getOrRefreshToken } = useSessionToken();
  const { analytics } = useAnalytics();

  const {
    data: serverApiVersion,
    isLoading,
    refetch,
    error,
  } = useQuery({
    queryKey: ["apiVersion3", org.api_url, org.id],
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();

      const controller = new AbortController();
      const timeout = setTimeout(
        () => controller.abort(),
        API_VERSION_FETCH_TIMEOUT_MS,
      );
      let response: Response;
      try {
        response = await apiFetchGet(
          `${org.api_url}/version`,
          sessionToken,
          controller.signal,
        );
      } catch (err) {
        // If the request was aborted, surface a clearer timeout error.
        if (err instanceof Error && err.name === "AbortError") {
          throw new Error(
            `Fetching API version timed out after ${
              API_VERSION_FETCH_TIMEOUT_MS / 1000
            }s. The API is likely unreachable.`,
          );
        }
        throw err;
      } finally {
        clearTimeout(timeout);
      }
      if (!response.ok) {
        throw new Error(
          `Failed to fetch API version (${response.status} ${response.statusText}): ${await response.text()}`,
        );
      }
      const ret = apiVersionSchema.parse(await response.json());

      analytics?.track("api-version-check", {
        api_version: ret,
        org_name: org.name,
        api_url: org.api_url,
        webapp_version: WEBAPP_VERSION,
      });

      set("apiVersion3", ret);

      return ret;
    },
    staleTime: Infinity, // this can stay cached for the duration of the session
    meta: {
      disableGlobalErrorToast: true,
    },
  });

  const apiVersion = serverApiVersion ?? initialLocalApiVersion;

  const refresh = useCallback(async () => {
    refetch();
  }, [refetch]);

  return { ...apiVersion, refresh, isLoading, error };
}

export const CheckApiVersion = () => {
  const org = useOrg();
  const [storedWebappVersion, setStoredWebappVersion] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.id || "",
    key: "webappVersion",
  });
  const [closed, setClosed] = useState(storedWebappVersion == WEBAPP_VERSION);

  const { version: apiVersion, isLoading } = useAPIVersion();

  const [loaded, setLoaded] = useState(false);
  useEffect(() => {
    setLoaded(true);
  }, []);

  // This warms up code execution. It's cheap if it's already done, but also cached.
  useWarmupCodeExecution();

  if (
    closed ||
    !loaded ||
    org.api_url === MultiTenantApiURL ||
    !(apiVersion && semver.lt(apiVersion, WEBAPP_VERSION)) ||
    isLoading
  ) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="xs"
          className="mr-1 h-8 gap-1.5 bg-amber-50 border-amber-200 hover:bg-amber-100 dark:bg-amber-950 dark:border-amber-900 dark:hover:bg-amber-900"
        >
          <TriangleAlert size={14} />
          Update API
        </Button>
      </PopoverTrigger>
      <PopoverContent className="text-xs" align="end">
        <div className="mb-2">
          Your API version <span className="font-mono">{apiVersion}</span> is
          behind webapp version{" "}
          <span className="font-mono">{WEBAPP_VERSION}</span>. Please update
          your API to the latest version.
        </div>
        <div className="flex gap-2">
          <Link
            className={buttonVariants({ size: "xs" })}
            href="/docs/self-hosting"
          >
            View docs
          </Link>
          <Button
            size="xs"
            onClick={() => {
              setStoredWebappVersion(WEBAPP_VERSION);
              setClosed(true);
            }}
          >
            Ignore
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
