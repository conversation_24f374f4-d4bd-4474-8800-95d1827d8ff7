import { cn } from "#/utils/classnames";
import { <PERSON>Alert, X } from "lucide-react";
import { Button } from "./button";
import * as Sentry from "@sentry/nextjs";
import { useEffect, isValidElement, useMemo } from "react";
import { useOrg } from "#/utils/user";
import { MultiTenantApiURL } from "#/utils/user-types";
import { renderToStaticMarkup } from "react-dom/server";

const childrenToString = (children: React.ReactNode): string => {
  if (typeof children === "string") return children;
  if (typeof children === "number") return children.toString();
  if (Array.isArray(children)) {
    return children.map((child) => childrenToString(child)).join(" ");
  }
  if (isValidElement(children)) {
    return renderToStaticMarkup(children);
  }
  return "Unknown error";
};

export const ErrorBanner = ({
  children,
  className,
  onClose,
  skipErrorReporting,
  iconClassName,
}: {
  children: React.ReactNode;
  className?: string;
  onClose?: () => void;
  skipErrorReporting?: boolean | ((value: string) => boolean);
  iconClassName?: string;
}) => {
  const org = useOrg();
  const isOnPrem = useMemo(
    () => org.api_url !== MultiTenantApiURL,
    [org.api_url],
  );
  const errorName = childrenToString(children);

  useEffect(() => {
    if (
      typeof skipErrorReporting === "function"
        ? skipErrorReporting(errorName)
        : skipErrorReporting
    ) {
      return;
    }

    const error = new Error(errorName);
    Sentry.captureException(error, (scope) => {
      scope.setExtra("isOnPremShare", isOnPrem);
      scope.setTag("page", "error-banner");
      scope.setTransactionName(errorName);
      scope.setFingerprint([errorName]);
      return scope;
    });
  }, [errorName, isOnPrem, skipErrorReporting]);

  return (
    <div
      className={cn(
        "my-2 flex items-center gap-2 rounded-md border p-2 text-xs bg-bad-50/50 border-bad-50 text-bad-700",
        className,
      )}
    >
      <TriangleAlert className={cn("size-3 flex-none", iconClassName)} />
      <div className="flex-1 break-all">{children}</div>
      {onClose && (
        <Button variant="ghost" size="inline" onClick={onClose}>
          <X className="size-3 flex-none" />
        </Button>
      )}
    </div>
  );
};
