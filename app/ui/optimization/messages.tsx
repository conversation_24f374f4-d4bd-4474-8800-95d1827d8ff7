import {
  type UserConsentConfirmationData,
  type TaskEditConfirmationData,
  type DatasetEditConfirmationData,
  type EditScorersConfirmationData,
  type CreateCodeScorerConfirmationData,
} from "#/utils/optimization/provider";
import {
  type ParsedMessage,
  type ToolInteraction,
  type SystemMessage,
} from "./use-global-chat-context";
import { But<PERSON> } from "#/ui/button";
import {
  Blend,
  MessageCircle,
  Percent,
  CircleArrowUp,
  Database,
  TextSearch,
  MessageSquareWarning,
  ChartColumnStacked,
} from "lucide-react";
import { ContextObjectBadge } from "#/ui/optimization/context-object-badge";
import { type ContextObject } from "#/ui/optimization/use-global-chat-context";

import { type UserMessage as UserMessageType } from "./use-global-chat-context";
import { MarkdownViewer } from "#/ui/markdown";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { ErrorBanner } from "#/ui/error-banner";
import { InfoBanner } from "#/ui/info-banner";
import { Feedback } from "#/ui/optimization/feedback";
import {
  EditTaskToolDisplay,
  EditDataToolDisplay,
  EditScorersToolDisplay,
  RunTaskToolDisplay,
  EvalResultsToolDisplay,
  PlaygroundSummaryToolDisplay,
  AvailableScorersToolDisplay,
  UserConsentMessage,
  CollapsibleAction,
  Title,
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // EditBTQLToolDisplay,
  CreateCodeScorerToolDisplay,
} from "#/ui/optimization/tool-message-ui";
import { type ChatContext, type PageKey } from "@braintrust/local/optimization";
import { ObjectRowLink, evalRowUrlTransform } from "./object-row-link";

export const Messages = ({
  parsedMessages,
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
  pageKey,
  setUserMessage,
  handleSendMessage,
  chat,
  hasMultipleSelectedExperiments = false,
}: {
  parsedMessages: ParsedMessage[];
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    userConsentConfirmationData: UserConsentConfirmationData | null,
  ) => void;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    editScorersConfirmationData: EditScorersConfirmationData | null,
  ) => void;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
  pageKey: PageKey;
  setUserMessage: (userMessage: string) => void;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  chat: ChatContext | null;
  hasMultipleSelectedExperiments?: boolean;
}) => {
  return (
    <>
      {parsedMessages.length > 0 ? (
        parsedMessages.map((msg, index) => (
          <div key={msg.id} className="mb-2">
            {msg.type === "tool_interaction" && msg.toolCallId && (
              <RenderToolMessage
                msg={msg}
                editTaskConfirmationData={editTaskConfirmationData}
                setEditTaskConfirmationData={setEditTaskConfirmationData}
                editDatasetConfirmationData={editDatasetConfirmationData}
                setEditDatasetConfirmationData={setEditDatasetConfirmationData}
                userConsentConfirmationData={userConsentConfirmationData}
                setUserConsentConfirmationData={setUserConsentConfirmationData}
                setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
                allowRunningWithoutConsent={allowRunningWithoutConsent}
                editScorersConfirmationData={editScorersConfirmationData}
                setEditScorersConfirmationData={setEditScorersConfirmationData}
                createCodeScorerConfirmationData={
                  createCodeScorerConfirmationData
                }
                setCreateCodeScorerConfirmationData={
                  setCreateCodeScorerConfirmationData
                }
              />
            )}
            {msg.type === "llm_message" && (
              <MarkdownViewer
                className="px-2 py-0 text-xs"
                value={msg.llmContent}
                components={{
                  a: ObjectRowLink,
                }}
                urlTransform={evalRowUrlTransform}
              />
            )}
            {msg.type === "system_message" && <SystemMessage msg={msg} />}
            {msg.type === "user_message" && <UserMessage msg={msg} />}
            {(msg.type === "tool_interaction" ||
              msg.type === "llm_message" ||
              msg.type === "system_message") &&
              msg.isLastMessageOfTurn &&
              chat && (
                <Feedback
                  sendFeedback={chat.recordFeedback.bind(chat)}
                  isActive={index === parsedMessages.length - 1}
                />
              )}
          </div>
        ))
      ) : (
        <EmptyState
          page={pageKey}
          handleSendMessage={handleSendMessage}
          setUserMessage={setUserMessage}
          hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
        />
      )}
    </>
  );
};

const RenderToolMessage = ({
  msg,
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
}: {
  msg: ToolInteraction;
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    userConsentConfirmationData: UserConsentConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    editScorersConfirmationData: EditScorersConfirmationData | null,
  ) => void;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
}) => {
  switch (msg.functionName) {
    case "edit_task":
      return (
        <EditTaskToolDisplay
          msg={msg}
          editTaskConfirmationData={editTaskConfirmationData}
          setEditTaskConfirmationData={setEditTaskConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );

    case "edit_data":
      return (
        <EditDataToolDisplay
          msg={msg}
          editDatasetConfirmationData={editDatasetConfirmationData}
          setEditDatasetConfirmationData={setEditDatasetConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );

    case "edit_scorers":
      return (
        <EditScorersToolDisplay
          msg={msg}
          editScorersConfirmationData={editScorersConfirmationData}
          setEditScorersConfirmationData={setEditScorersConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );

    case "create_code_scorer":
      return (
        <CreateCodeScorerToolDisplay
          msg={msg}
          createCodeScorerConfirmationData={createCodeScorerConfirmationData}
          setCreateCodeScorerConfirmationData={
            setCreateCodeScorerConfirmationData
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "run_task":
      return (
        <RunTaskToolDisplay
          msg={msg}
          userConsentConfirmationData={userConsentConfirmationData}
          setUserConsentConfirmationData={setUserConsentConfirmationData}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
        />
      );

    case "get_results":
      return <EvalResultsToolDisplay msg={msg} />;
    case "get_summary":
      return <PlaygroundSummaryToolDisplay msg={msg} />;
    case "continue_execution":
      if (msg.status === "pending_output" && userConsentConfirmationData) {
        return (
          <UserConsentMessage
            userConsentConfirmationData={userConsentConfirmationData}
            setUserConsentConfirmationData={setUserConsentConfirmationData}
            setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
            allowRunningWithoutConsent={allowRunningWithoutConsent}
          />
        );
      }
      return null;
    case "get_available_scorers":
      return <AvailableScorersToolDisplay msg={msg} />;

    // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
    // case "edit_btql":
    //   return <EditBTQLToolDisplay msg={msg} />;
    default:
      break;
  }

  // Default case for all other function names and statuses
  return (
    <CollapsibleAction title={<Title msg={msg} />} defaultCollapsed={true}>
      <SyntaxHighlight
        language="json"
        className="break-all rounded-b-md border-x border-b p-2 bg-primary-100"
        content={JSON.stringify(
          msg.status === "completed" ||
            msg.status === "error_executing_tool" ||
            msg.status === "rejected"
            ? msg.toolOutput
            : msg.arguments,
          null,
          2,
        )}
      />
    </CollapsibleAction>
  );
};

const SystemMessage = ({ msg }: { msg: SystemMessage }) => {
  if (msg.variant === "error") {
    return (
      <ErrorBanner
        className="items-start overflow-x-auto"
        iconClassName="mt-0.5"
      >
        {msg.message}
      </ErrorBanner>
    );
  }
  return (
    <InfoBanner className="items-start" iconClassName="mt-0.5">
      {msg.message}
    </InfoBanner>
  );
};

const UserMessage = ({ msg }: { msg: UserMessageType }) => {
  return (
    <div className="flex min-h-7 flex-col justify-center gap-1 rounded-md border p-2 bg-background border-primary-300 text-primary-900">
      {msg.contextObjects && Object.keys(msg.contextObjects).length > 0 && (
        <div className="flex flex-wrap gap-1">
          {Object.values(msg.contextObjects)
            .flat()
            .map((contextObjectItem: ContextObject, index: number) => {
              return (
                <ContextObjectBadge
                  key={index}
                  contextObjectItem={contextObjectItem}
                />
              );
            })}
        </div>
      )}
      <div className="overflow-hidden break-words text-xs">{msg.message}</div>
    </div>
  );
};

const EmptyState = ({
  page,
  handleSendMessage,
  setUserMessage,
  hasMultipleSelectedExperiments = false,
}: {
  page: PageKey;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  setUserMessage: (userMessage: string) => void;
  hasMultipleSelectedExperiments?: boolean;
}) => {
  if (page === "experiments") {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to get detailed breakdowns and insights about
          your experiments.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button-1 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={TextSearch}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Give me a detailed breakdown and insights about ${hasMultipleSelectedExperiments ? "selected experiments" : "in the base experiment"}`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Summarize{" "}
            {hasMultipleSelectedExperiments
              ? "selected experiments"
              : "the base experiment"}
            <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-1:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button-2 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            Icon={MessageSquareWarning}
            iconClassName="text-primary-500 size-3.5"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find the biggest problems in ${hasMultipleSelectedExperiments ? "selected experiments" : "in the experiment"}`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Highlight problems in{" "}
            {hasMultipleSelectedExperiments
              ? "selected experiments"
              : "in the base experiment"}
            <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-2:opacity-100" />
          </Button>
          {hasMultipleSelectedExperiments && (
            <Button
              size="xs"
              variant="border"
              className="group/button-2 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
              Icon={ChartColumnStacked}
              iconClassName="text-primary-500 size-3.5"
              onClick={() => {
                handleSendMessage(
                  {
                    id: crypto.randomUUID(),
                    type: "user_message",
                    message: "Find the biggest problems in the experiment.",
                  },
                  {
                    clearContextObjects: true,
                    clearUserMessage: true,
                  },
                );
              }}
            >
              Which experiment performed the best?
              <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-2:opacity-100" />
            </Button>
          )}
        </div>
      </div>
    );
  } else {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to optimize prompts, generate dataset rows, and
          automate eval development.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button-1 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500"
            Icon={MessageCircle}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Optimize the prompts in this playground",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Optimize prompts
            <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-1:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button-2 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            Icon={Percent}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message:
                    "Create a code scorer for the task in this playground",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Create code scorer
            <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-2:opacity-100" />
          </Button>

          <Button
            size="xs"
            variant="border"
            className="group/button-4 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            Icon={Database}
            iconClassName="text-primary-500"
            onClick={() => {
              setUserMessage(
                `Please generate [Enter number of rows] rows of data for the dataset. If more than 10, generate in batches of at most 10 rows.
 [optional additional instructions]`,
              );
            }}
          >
            Generate dataset rows
          </Button>

          <Button
            size="xs"
            Icon={Percent}
            variant="border"
            className="group/button-2 justify-start gap-2 font-normal bg-background text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Choose scorers for the task in this playground",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Choose scorers
            <CircleArrowUp className="ml-auto size-3 opacity-0 transition-opacity text-primary-500 group-hover/button-3:opacity-100" />
          </Button>
        </div>
      </div>
    );
  }
};
