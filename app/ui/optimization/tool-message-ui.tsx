import { Label } from "#/ui/label";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  CircleDotDashed,
  SquareSlash,
  type LucideIcon,
  Percent,
  PencilLine,
  Plus,
  Minus,
  ArrowUpRight,
  Blend,
  Ellipsis,
  Clipboard,
} from "lucide-react";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { PromptDiff } from "#/app/app/[org]/p/[project]/prompts/prompt-diff";
import { Spinner } from "#/ui/icons/spinner";
import {
  type EditDataToolParameters,
  getResultsToolResultSchema,
  getAvailableScorersResultSchema,
  getSummaryToolResultSchema,
  getGlobalScorers,
  editTaskToolParams,
  editDataToolParams,
  editScorersParamsSchema,
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // editBTQLToolParams,
  rerunTaskToolResultSchema,
  getResultsToolParams,
  createCodeScorerParamsSchema,
} from "@braintrust/local/optimization/tools";
import { type ReactNode, useCallback, useMemo, useState } from "react";
import { DataTextEditor } from "#/ui/data-text-editor";
import { type ToolInteraction, useGlobalChat } from "./use-global-chat-context";
import { Switch } from "#/ui/switch";
import { cn } from "#/utils/classnames";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { BasicTooltip } from "#/ui/tooltip";
import { z } from "zod";
import { jsonToYaml } from "#/utils/parse";
import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
} from "#/ui/charts/colors";
import {
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  //  type EditBTQLConfirmationData,
  type DatasetEditConfirmationData,
  type EditScorersConfirmationData,
  type TaskEditConfirmationData,
  type UserConsentConfirmationData,
  type CreateCodeScorerConfirmationData,
} from "#/utils/optimization/provider";
import { CodeEditor } from "#/ui/prompts/function-editor/code-editor";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";

const SWATCH_CLASSES = [
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
  ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
];

// This is to handle the old data model prior to adding the tooloutput field. This safely handles the old data model
function getToolOutputSafely(msg: ToolInteraction): unknown {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
  return (msg as any).toolOutput ?? msg.arguments ?? {};
}

// This is to handle the old data model prior to adding the error field. This safely handles the old data model
function getToolErrorSafely(msg: ToolInteraction): string | null {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
  return (msg as any).error?.error ?? null;
}
/*
export const EditBTQLMessage = ({
  editBTQLConfirmationData,
  setEditBTQLConfirmationData,
}: {
  editBTQLConfirmationData: EditBTQLConfirmationData;
  setEditBTQLConfirmationData: (data: EditBTQLConfirmationData | null) => void;
}) => {
  const handleCancel = useCallback(() => {
    editBTQLConfirmationData.onCancel();
    setEditBTQLConfirmationData(null);
  }, [editBTQLConfirmationData, setEditBTQLConfirmationData]);

  const handleConfirm = useCallback(() => {
    editBTQLConfirmationData.onConfirm();
    setEditBTQLConfirmationData(null);
  }, [editBTQLConfirmationData, setEditBTQLConfirmationData]);

  return (
    <CollapsibleAction
      Icon={SquareSlash}
      iconClassName="text-accent-500"
      defaultCollapsed={false}
      title="BTQL change suggestions"
    >
      <div className="flex flex-col gap-1 border-x p-2 bg-primary-100">
        <DataTextEditor
          rowId="btql-editor"
          value={{
            btql: editBTQLConfirmationData.query,
            filter: editBTQLConfirmationData.filter?.join(" AND "),
            sort: editBTQLConfirmationData.sort?.join(" AND "),
          }}
          allowedRenderOptions={["json"]}
          hideLineNumbers
          readOnly
          diffValue={""}
        />
      </div>
      <div className="flex items-center justify-end gap-2 rounded-b-md border-x border-b p-2 bg-primary-100">
        <RejectButton onClick={handleCancel} />
        <AcceptButton onClick={handleConfirm} />
      </div>
    </CollapsibleAction>
  );
}; */

export const EditScorersMessage = ({
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  editScorersConfirmationData: EditScorersConfirmationData;
  setEditScorersConfirmationData: (
    data: EditScorersConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const autoevals = getGlobalScorers();

  const enrichedCurrentScorers = useMemo(() => {
    return editScorersConfirmationData.currentScorers.map((scorer) => {
      if (scorer.type === "function") {
        return {
          id: scorer.id,
          name:
            editScorersConfirmationData.availableScorers[scorer.id]?.name ??
            scorer.id,
          description:
            editScorersConfirmationData.availableScorers[scorer.id]
              ?.description ?? "",
          type: "function" as const,
        };
      }
      return {
        id: scorer.name,
        name: scorer.name,
        description:
          autoevals.find((a) => a.name === scorer.name)?.description ??
          "No description available",
        type: "global" as const,
      };
    });
  }, [
    editScorersConfirmationData.currentScorers,
    editScorersConfirmationData.availableScorers,
    autoevals,
  ]);

  const { newlyAdded, removing, remainingExisting } = useMemo(() => {
    const newlyAdded = editScorersConfirmationData.scorers.filter(
      (scorer) =>
        scorer.enabled &&
        !enrichedCurrentScorers.find((s) => s.id === scorer.id),
    );

    const removing = editScorersConfirmationData.scorers.filter(
      (scorer) =>
        !scorer.enabled &&
        enrichedCurrentScorers.find((s) => s.id === scorer.id),
    );

    const remainingExisting = enrichedCurrentScorers.filter(
      (scorer) => !removing.find((r) => r.id === scorer.id),
    );

    return { newlyAdded, removing, remainingExisting };
  }, [editScorersConfirmationData.scorers, enrichedCurrentScorers]);

  const handleCancel = useCallback(() => {
    editScorersConfirmationData.onCancel();
    setEditScorersConfirmationData(null);
  }, [editScorersConfirmationData, setEditScorersConfirmationData]);

  const handleConfirm = useCallback(() => {
    editScorersConfirmationData.onConfirm();
    setEditScorersConfirmationData(null);
  }, [editScorersConfirmationData, setEditScorersConfirmationData]);

  return (
    <CollapsibleAction
      Icon={SquareSlash}
      iconClassName="text-accent-500"
      defaultCollapsed={false}
      title="Scorer change suggestions"
    >
      <div className="flex flex-col gap-1 border-x p-2 bg-primary-100">
        <div className="flex flex-wrap gap-1">
          {newlyAdded.length > 0 &&
            newlyAdded.map((scorer) => (
              <BasicTooltip
                key={`new-${scorer.name}`}
                tooltipContent={
                  <div className="flex flex-col gap-1">
                    <span className="text-xs font-medium">{scorer.name}</span>
                    <span className="text-xs text-primary-500">
                      {scorer.description}
                    </span>
                  </div>
                }
              >
                <div
                  className="rounded-md border px-2 py-1 text-xs font-medium bg-background border-good-500 text-good-700"
                  key={`new-${scorer.name}`}
                >
                  {scorer.name}
                </div>
              </BasicTooltip>
            ))}
          {remainingExisting.length > 0 &&
            remainingExisting.map((scorer) => (
              <BasicTooltip
                key={`existing-${scorer.name}`}
                tooltipContent={
                  <div className="flex flex-col gap-1">
                    <span className="text-xs font-medium">{scorer.name}</span>
                    <span className="text-xs text-primary-500">
                      {scorer.description}
                    </span>
                  </div>
                }
              >
                <div
                  className="rounded-md border px-2 py-1 text-xs font-medium bg-background text-primary-700"
                  key={`existing-${scorer.name}`}
                >
                  {scorer.name}
                </div>
              </BasicTooltip>
            ))}
          {removing.length > 0 &&
            removing.map((scorer) => (
              <BasicTooltip
                key={`removing-${scorer.name}`}
                tooltipContent={
                  <div className="flex flex-col gap-1">
                    <span className="text-xs font-medium">{scorer.name}</span>
                    <span className="text-xs text-primary-500">
                      {scorer.description}
                    </span>
                  </div>
                }
              >
                <div className="rounded-md border px-2 py-1 text-xs font-medium line-through bg-background border-bad-500 text-bad-700">
                  {scorer.name}
                </div>
              </BasicTooltip>
            ))}
        </div>
      </div>
      <ConfirmationFooter
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
      />
    </CollapsibleAction>
  );
};

export const EditTaskMessage = ({
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  editTaskConfirmationData: TaskEditConfirmationData;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  return (
    <CollapsibleAction
      Icon={SquareSlash}
      iconClassName="text-accent-500"
      defaultCollapsed={false}
      title="Task change suggestions"
    >
      <div className="border-x p-2 bg-primary-100">
        <PromptDiff
          promptData={editTaskConfirmationData.newDefinition}
          diffPromptData={editTaskConfirmationData.originalDefinition}
          rowId={editTaskConfirmationData.index.toString()}
          hideDiffDisclaimer
          hideLineNumbers
        />
      </div>
      <ConfirmationFooter
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
        onCancel={() => {
          editTaskConfirmationData.onCancel();
          setEditTaskConfirmationData(null);
        }}
        onConfirm={() => {
          editTaskConfirmationData.onConfirm();
          setEditTaskConfirmationData(null);
        }}
      />
    </CollapsibleAction>
  );
};

export const EditDataRowMessage = ({
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  editDatasetConfirmationData: DatasetEditConfirmationData;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  return (
    <CollapsibleAction
      defaultCollapsed={false}
      title="Dataset change suggestions"
      Icon={SquareSlash}
      iconClassName="text-accent-500"
    >
      <div className="flex flex-col gap-2 border-x p-2 bg-primary-100">
        {editDatasetConfirmationData.edits.map((edit, index) => (
          <EditDataRow
            key={index}
            edit={edit}
            rowId={index.toString()}
            existing={editDatasetConfirmationData.existing}
          />
        ))}
      </div>
      <ConfirmationFooter
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
        onCancel={() => {
          editDatasetConfirmationData.onCancel();
          setEditDatasetConfirmationData(null);
        }}
        onConfirm={() => {
          editDatasetConfirmationData.onConfirm();
          setEditDatasetConfirmationData(null);
        }}
      />
    </CollapsibleAction>
  );
};

export const CreateCodeScorerMessage = ({
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const { handleSendMessage } = useGlobalChat();

  return (
    <CollapsibleAction
      defaultCollapsed={false}
      title="Code scorer suggestion"
      Icon={SquareSlash}
      iconClassName="text-accent-500"
    >
      <div className="flex flex-col gap-1 border-x p-2 bg-primary-100">
        <div className="flex flex-col">
          <div className="flex w-full items-center justify-between">
            <span className="flex flex-1 items-center gap-2 text-xs font-normal text-primary-900">
              <Percent className="size-3 text-primary-500" />{" "}
              {createCodeScorerConfirmationData.scorer.name}
            </span>
            <span className="text-xs text-primary-500">
              {createCodeScorerConfirmationData.scorer.runtime === "python"
                ? "Python"
                : "Typescript"}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="xs"
                  Icon={Ellipsis}
                  className="-mr-2 transition-all text-primary-500"
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => {
                    handleSendMessage(
                      {
                        id: crypto.randomUUID(),
                        type: "user_message",
                        message: `Generate a ${createCodeScorerConfirmationData.scorer.runtime === "python" ? "Typescript" : "Python"} version of this code scorer`,
                        contextObjects: {},
                      },
                      {
                        clearUserMessage: false,
                        clearContextObjects: false,
                      },
                    );
                  }}
                >
                  <Blend className="size-3" /> Generate as{" "}
                  {createCodeScorerConfirmationData.scorer.runtime === "python"
                    ? "Typescript"
                    : "Python"}
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <CopyToClipboardButton
                    variant="ghost"
                    textToCopy={createCodeScorerConfirmationData.scorer.code}
                    className="flex w-full items-center justify-start gap-2 font-normal transition-all text-primary-900"
                  >
                    {" "}
                    <Clipboard className="size-3" /> Copy to clipboard
                  </CopyToClipboardButton>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <CodeEditor
          savedCode={createCodeScorerConfirmationData.scorer.code}
          language={
            createCodeScorerConfirmationData.scorer.runtime === "python"
              ? "py"
              : "ts"
          }
          hideLineNumbers
          hideBanner
          readOnly
          className="-mt-2 text-xs"
          //Check if it's a code-base scorer and also if its not bundled.
          diffValue={
            createCodeScorerConfirmationData.existingScorer?.function_data
              ?.type === "code" &&
            createCodeScorerConfirmationData.existingScorer?.function_data?.data
              ?.type === "inline"
              ? createCodeScorerConfirmationData.existingScorer.function_data
                  .data.code
              : ""
          }
        />
      </div>
      <ConfirmationFooter
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
        onCancel={() => {
          createCodeScorerConfirmationData.onCancel();
          setCreateCodeScorerConfirmationData(null);
        }}
        onConfirm={() => {
          createCodeScorerConfirmationData.onConfirm();
          setCreateCodeScorerConfirmationData(null);
        }}
      />
    </CollapsibleAction>
  );
};

export const CollapsibleAction = ({
  defaultCollapsed,
  className,
  title,
  children,
  Icon = CircleDotDashed,
  iconClassName,
  nested = false,
  pending = false,
}: {
  defaultCollapsed: boolean;
  className?: string;
  title: ReactNode;
  iconClassName?: string;
  Icon?: LucideIcon;
  children: ReactNode;
  nested?: boolean;
  pending?: boolean;
}) => {
  if (pending) {
    return (
      <div className="flex flex-1 items-center gap-2 rounded-md p-2 text-primary-600">
        {Icon && !nested && (
          <Icon className={cn("size-3 text-stone-500", iconClassName)} />
        )}{" "}
        {title}
      </div>
    );
  }

  return (
    <CollapsibleSection
      className={cn(
        "rounded-md border-transparent border-x border-t p-2 bg-primary-50",
        className,
      )}
      collapsedClassName={
        nested ? "bg-primary-100 p-0 hover:p-2" : "bg-primary-50"
      }
      expandedClassName={cn(
        "bg-primary-100 hover:bg-primary-200 mb-0 hover:px-2 rounded-b-none border-primary-200",
        nested && "bg-background",
      )}
      title={
        <div className="flex flex-1 items-center gap-2 text-primary-600">
          {Icon && !nested && (
            <Icon className={cn("size-3 text-stone-500", iconClassName)} />
          )}{" "}
          {title}
        </div>
      }
      defaultCollapsed={defaultCollapsed}
    >
      {children}
    </CollapsibleSection>
  );
};

export const SkipButton = ({
  onClick,
  className,
}: {
  onClick: () => void;
  className?: string;
}) => {
  return (
    <Button
      variant="ghost"
      size="xs"
      className={cn("h-6", className)}
      onClick={onClick}
    >
      Skip
    </Button>
  );
};

export const AcceptButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Button variant="primary" className="h-6" size="xs" onClick={onClick}>
      Accept
    </Button>
  );
};

export function EditDataRow({
  edit,
  rowId,
  existing,
}: {
  edit: EditDataToolParameters["edits"][number];
  rowId: string;
  existing: Record<
    string,
    {
      id: string;
      input: unknown;
      expected: unknown;
      metadata: unknown;
    }
  >;
}) {
  const existingRow = useMemo(() => {
    if (!edit.id || !existing[edit.id]) {
      return undefined;
    }
    const { id: _, ...rest } = existing[edit.id];
    return rest;
  }, [existing, edit.id]);
  const updatedValue = useMemo(() => {
    if (edit.delete) {
      return null;
    }
    return {
      input: edit.input ?? existingRow?.input,
      expected: edit.expected ?? existingRow?.expected,
      metadata: edit.metadata ?? existingRow?.metadata,
    };
  }, [edit, existingRow]);
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col">
        <div className="text-xs font-medium ">{edit.id ?? "New row"}</div>
        <div className="text-xs text-primary-500">{edit.purpose}</div>
        <DataTextEditor
          className="mt-2 rounded-md border px-2 py-1 bg-primary-100 border-primary-300/50"
          allowedRenderOptions={["json", "yaml"]}
          value={edit.delete ? null : updatedValue}
          diffValue={existingRow}
          rowId={rowId}
          readOnly
          hideLineNumbers
        />
      </div>
    </div>
  );
}

export const pendingToolLabels: Record<string, string> = {
  get_summary: "Summarizing...",
  get_results: "Getting eval results...",
  edit_task: "Editing prompt...",
  run_task: "Running eval...",
  edit_data: "Editing data...",
  continue_execution: "Continuing execution...",
  get_available_scorers: "Getting scorers...",
  edit_scorers: "Editing scorers...",
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // edit_btql: "Editing BTQL...",
  create_code_scorer: "Creating code scorer...",
};

const completedToolLabels: Record<string, string> = {
  get_summary: "Summarized",
  get_results: "Fetched eval results",
  edit_task: "Edited prompt",
  run_task: "Ran eval",
  edit_data: "Edited data",
  continue_execution: "Continued execution",
  get_available_scorers: "Fetched scorers",
  edit_scorers: "Edited scorers",
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // edit_btql: "Edited BTQL",
  create_code_scorer: "Created code scorer",
};

function toolLabel(msg: ToolInteraction): ReactNode {
  if (!msg.functionName) {
    return msg.toolCallId;
  }

  if (msg.status === "pending_output") {
    return (
      <span className="animate-textShimmer bg-gradient-to-r from-primary-300 via-primary-600 to-primary-300 bg-clip-text text-transparent">
        {pendingToolLabels[msg.functionName] || "Calling tool..."}
      </span>
    );
  }

  return completedToolLabels[msg.functionName] || "Completed tool call";
}

// Generic typeguard function
function isValidToolData<T>(schema: z.ZodType<T>) {
  return (data: unknown): data is T => {
    const result = schema.safeParse(data);
    if (!result.success) {
      return false;
    }
    return true;
  };
}

function toString(value: unknown): string {
  switch (typeof value) {
    case "string":
      return value;
    case "number":
    case "boolean":
    case "bigint":
      return String(value);
    case "undefined":
      return "undefined";
    case "symbol":
      return value.toString();
    case "function":
      return value.name || "function";
    case "object":
      if (value === null) return "null";
      if (Array.isArray(value)) return `[${value.map(toString).join(",")}]`;
      if (value instanceof Date) return value.toISOString();
      return "[object Object]";
    default:
      return String(value);
  }
}

const TaskHeader = ({
  index,
  taskName,
  className,
}: {
  index: number;
  taskName?: string;
  className?: string;
}) => {
  return (
    <div className={cn("flex items-center gap-1 w-full", className)}>
      <div
        className={`m-px mr-1 size-[10px] rounded-full ${SWATCH_CLASSES[index]}`}
      />
      <span className="text-xs font-normal text-primary-900">
        {index === 0 ? "Base task" : "Comparison task"}
      </span>
      {taskName && (
        <span className="min-w-0 flex-1 truncate text-right text-xs font-normal text-primary-500">
          {taskName}
        </span>
      )}
    </div>
  );
};
/*
// BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
export const EditBTQLToolDisplay = ({ msg }: { msg: ToolInteraction }) => {
  const validatedBTQLArguments = useMemo(() => {
    return msg.functionName === "edit_btql" &&
      (msg.status === "completed" || msg.status === "rejected")
      ? isValidToolData(editBTQLToolParams)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);

  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex max-h-[248px] flex-col gap-1 overflow-y-auto rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {(msg.status === "completed" || msg.status === "rejected") &&
          (validatedBTQLArguments ? (
            <div className="text-xs font-normal text-primary-500">
              {validatedBTQLArguments?.filter}
            </div>
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
            />
          ))}
      </div>
    </CollapsibleAction>
  );
}; */

export const EditScorersToolDisplay = ({
  msg,
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  msg: ToolInteraction;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    editScorersConfirmationData: EditScorersConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const validatedScorersArguments = useMemo(() => {
    return msg.functionName === "edit_scorers" &&
      (msg.status === "completed" || msg.status === "rejected")
      ? isValidToolData(editScorersParamsSchema)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);

  if (msg.status === "pending_output" && editScorersConfirmationData) {
    return (
      <EditScorersMessage
        editScorersConfirmationData={editScorersConfirmationData}
        setEditScorersConfirmationData={setEditScorersConfirmationData}
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
      />
    );
  }
  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col gap-1 rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {(msg.status === "completed" || msg.status === "rejected") &&
          (validatedScorersArguments ? (
            validatedScorersArguments.scorers.length > 0 ? (
              validatedScorersArguments.scorers.map((scorer) => (
                <div className="flex flex-col" key={`new-${scorer.id}`}>
                  <CollapsibleAction
                    nested
                    key={`new-${scorer.name}`}
                    title={
                      <div
                        className={cn(
                          "flex justify-between w-full items-center",
                          !scorer.enabled && "line-through",
                        )}
                      >
                        {scorer.enabled ? (
                          <Plus className="mr-2 size-3 text-primary-500" />
                        ) : (
                          <Minus className="mr-2 size-3 text-primary-500" />
                        )}
                        <span className="max-w-[256px] flex-1 truncate text-xs font-normal text-primary-900">
                          {scorer.name}
                        </span>
                        <span className="text-xs font-normal text-primary-500">
                          {scorer.id === scorer.name ? "AutoEvals" : "Custom"}
                        </span>
                      </div>
                    }
                    defaultCollapsed={true}
                  >
                    <div className="rounded-b-md border-x border-b p-2 text-xs bg-background border-primary-200/80 text-primary-700">
                      {scorer.description}
                    </div>
                  </CollapsibleAction>
                </div>
              ))
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No scorers were suggested
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
            />
          ))}
      </div>
    </CollapsibleAction>
  );
};

export const AvailableScorersToolDisplay = ({
  msg,
}: {
  msg: ToolInteraction;
}) => {
  const validatedAvailableScorersOutput = useMemo(() => {
    if (
      msg.functionName === "get_available_scorers" &&
      msg.status === "completed"
    ) {
      return isValidToolData(z.array(getAvailableScorersResultSchema))(
        msg.toolOutput,
      )
        ? msg.toolOutput
        : null;
    }
    return null;
  }, [msg]);

  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex max-h-[248px] flex-col gap-1 overflow-y-auto rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}

        {msg.status === "completed" &&
          (validatedAvailableScorersOutput ? (
            validatedAvailableScorersOutput.length > 0 ? (
              validatedAvailableScorersOutput.map((scorer) => (
                <div key={scorer.id} className="flex flex-col">
                  <CollapsibleAction
                    nested
                    key={`new-${scorer.id}`}
                    title={
                      <div className="flex w-full items-center justify-start">
                        <Percent className="mr-2 size-3 text-primary-500" />
                        <span className="max-w-[212px] flex-1 truncate text-xs font-normal text-primary-900">
                          {scorer.name}
                        </span>
                        <span className="ml-auto text-xs font-normal text-primary-500">
                          {scorer.id === scorer.name ? "AutoEvals" : "Custom"}
                        </span>
                      </div>
                    }
                    defaultCollapsed={true}
                  >
                    <div className="rounded-b-md border-x border-b p-2 text-xs bg-background border-primary-200/80 text-primary-700">
                      {scorer.description && scorer.description.length > 0
                        ? scorer.description
                        : "No description available"}
                    </div>
                  </CollapsibleAction>
                </div>
              ))
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No scorers were found
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
              className="bg-primary-100"
            />
          ))}
        {msg.status === "rejected" && (
          <div className="text-xs font-normal text-primary-500">
            {getToolErrorSafely(msg) || "Tool was rejected"}
          </div>
        )}
      </div>
    </CollapsibleAction>
  );
};

export const PlaygroundSummaryToolDisplay = ({
  msg,
}: {
  msg: ToolInteraction;
}) => {
  const validatedPlaygroundSummaryOutput = useMemo(() => {
    if (msg.functionName === "get_summary" && msg.status === "completed") {
      return isValidToolData(z.array(getSummaryToolResultSchema))(
        msg.toolOutput,
      )
        ? msg.toolOutput
        : null;
    }
    return null;
  }, [msg]);

  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col gap-1 break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {msg.status === "completed" &&
          (validatedPlaygroundSummaryOutput ? (
            validatedPlaygroundSummaryOutput.length > 0 ? (
              validatedPlaygroundSummaryOutput.map((summary) => (
                <div key={summary.taskName}>
                  <CollapsibleAction
                    nested
                    title={
                      <TaskHeader
                        index={summary.index}
                        taskName={summary.taskName}
                      />
                    }
                    defaultCollapsed={true}
                  >
                    <SyntaxHighlight
                      language="yaml"
                      content={jsonToYaml(summary)}
                      className="rounded-b-md border-x border-b p-2 bg-background border-primary-200/80"
                    />
                  </CollapsibleAction>
                </div>
              ))
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No playground summary was found
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
              className="bg-primary-100"
            />
          ))}
        {msg.status === "rejected" && (
          <span className="text-xs font-normal text-primary-500">
            {getToolErrorSafely(msg) || "Tool was rejected"}
          </span>
        )}
      </div>
    </CollapsibleAction>
  );
};

export const EvalResultsToolDisplay = ({ msg }: { msg: ToolInteraction }) => {
  const validatedResultsOutput = useMemo(() => {
    if (msg.functionName === "get_results" && msg.status === "completed") {
      return isValidToolData(z.array(getResultsToolResultSchema))(
        msg.toolOutput,
      )
        ? msg.toolOutput
        : null;
    }
    return null;
  }, [msg]);

  const validatedResultsArguments = useMemo(() => {
    return msg.functionName === "get_results"
      ? isValidToolData(getResultsToolParams)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);

  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex max-h-[248px] flex-col gap-1 overflow-y-auto break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {msg.status === "completed" &&
          (validatedResultsOutput ? (
            validatedResultsOutput.length > 0 ? (
              <div className="flex flex-col gap-1">
                {validatedResultsArguments && (
                  <TaskHeader
                    index={validatedResultsArguments.index}
                    className="mb-1"
                  />
                )}
                <div className="ml-[6px] flex flex-col gap-1 border-l pl-2 border-primary-300/80">
                  {validatedResultsOutput.map((result) => (
                    <div key={result.id} className="flex flex-col">
                      <CollapsibleAction
                        nested
                        title={
                          <div className="flex items-center gap-2">
                            <ArrowUpRight className="size-3 text-primary-500" />
                            <span className="min-w-0 max-w-[256px] flex-1 truncate text-xs font-normal text-primary-900">
                              {toString(result.output)}
                            </span>
                          </div>
                        }
                        defaultCollapsed={true}
                      >
                        <SyntaxHighlight
                          className="rounded-b-md border-x border-b p-2 bg-background border-primary-200/80"
                          language="yaml"
                          content={jsonToYaml(result)}
                        />
                      </CollapsibleAction>
                    </div>
                  ))}
                </div>
                <div className="flex w-full justify-end pt-2 text-xs font-normal text-primary-500">
                  <span>At most 100 rows are sampled at a time</span>
                </div>
              </div>
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No evaluations results were found
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="yaml"
              content={jsonToYaml(getToolOutputSafely(msg))}
              className="bg-primary-100"
            />
          ))}
        {msg.status === "rejected" && (
          <div className="text-xs font-normal text-primary-500">
            {getToolErrorSafely(msg) || "Tool was rejected"}
          </div>
        )}
      </div>
    </CollapsibleAction>
  );
};

export const EditDataToolDisplay = ({
  msg,
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  msg: ToolInteraction;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const validatedDataArguments = useMemo(() => {
    return msg.functionName === "edit_data" &&
      (msg.status === "completed" || msg.status === "rejected")
      ? isValidToolData(editDataToolParams)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);
  if (msg.status === "pending_output" && editDatasetConfirmationData) {
    return (
      <EditDataRowMessage
        editDatasetConfirmationData={editDatasetConfirmationData}
        setEditDatasetConfirmationData={setEditDatasetConfirmationData}
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
      />
    );
  }
  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col gap-1 break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {(msg.status === "completed" || msg.status === "rejected") &&
          (validatedDataArguments ? (
            validatedDataArguments.edits.length > 0 ? (
              <div className="flex flex-col gap-1">
                {validatedDataArguments.edits.map((edit, index) => (
                  <div key={`edit-${index}`}>
                    <CollapsibleAction
                      nested
                      title={
                        <div className="flex items-center gap-2 text-xs font-normal">
                          <PencilLine className="size-3 text-primary-500" />
                          <span className="min-w-0 max-w-[280px] flex-1 truncate text-primary-900">
                            {edit.input ? toString(edit.input) : "Row deleted"}
                          </span>
                        </div>
                      }
                      defaultCollapsed={true}
                    >
                      <DataTextEditor
                        key={`edit-${index}`}
                        className="rounded-b-md border-x border-b px-2 bg-background border-primary-200/80"
                        allowedRenderOptions={["json", "yaml"]}
                        value={
                          edit.delete
                            ? { deleted_row: edit.id }
                            : {
                                input: edit.input,
                                expected: edit.expected,
                                metadata: edit.metadata,
                                purpose: edit.purpose,
                              }
                        }
                        diffValue={
                          edit.delete
                            ? { deleted_row: edit.id }
                            : {
                                input: edit.input,
                                expected: edit.expected,
                                metadata: edit.metadata,
                                purpose: edit.purpose,
                              }
                        }
                        rowId={edit.id}
                        readOnly
                        hideLineNumbers
                      />
                    </CollapsibleAction>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No dataset edits were suggested
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
              className="bg-primary-100"
            />
          ))}
      </div>
    </CollapsibleAction>
  );
};

export const RunTaskToolDisplay = ({
  msg,
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
}: {
  msg: ToolInteraction;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    userConsentConfirmationData: UserConsentConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
}) => {
  const validatedRunTaskOutput = useMemo(() => {
    if (msg.functionName === "run_task" && msg.status === "completed") {
      return isValidToolData(rerunTaskToolResultSchema)(msg.toolOutput)
        ? msg.toolOutput
        : null;
    }
    return null;
  }, [msg]);

  if (msg.status === "pending_output" && userConsentConfirmationData) {
    return (
      <UserConsentMessage
        userConsentConfirmationData={userConsentConfirmationData}
        setUserConsentConfirmationData={setUserConsentConfirmationData}
        setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
        allowRunningWithoutConsent={allowRunningWithoutConsent}
      />
    );
  }
  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {msg.status === "completed" &&
          (validatedRunTaskOutput ? (
            <CollapsibleAction
              nested
              title={
                <TaskHeader
                  index={validatedRunTaskOutput.summary.index}
                  taskName={validatedRunTaskOutput.summary.taskName}
                />
              }
              defaultCollapsed={true}
            >
              <SyntaxHighlight
                language="yaml"
                content={jsonToYaml(validatedRunTaskOutput.summary)}
                className="rounded-b-md border-x border-b p-2 bg-background border-primary-200/80"
              />
            </CollapsibleAction>
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
              className="bg-primary-100"
            />
          ))}
        {msg.status === "rejected" && (
          <span className="text-xs font-normal text-primary-500">
            {getToolErrorSafely(msg) || "Tool was rejected"}
          </span>
        )}
      </div>
    </CollapsibleAction>
  );
};

export const EditTaskToolDisplay = ({
  msg,
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  msg: ToolInteraction;
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const validatedTaskArguments = useMemo(() => {
    return msg.functionName === "edit_task" &&
      (msg.status === "completed" || msg.status === "rejected")
      ? isValidToolData(editTaskToolParams)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);
  if (msg.status === "pending_output" && editTaskConfirmationData) {
    return (
      <EditTaskMessage
        editTaskConfirmationData={editTaskConfirmationData}
        setEditTaskConfirmationData={setEditTaskConfirmationData}
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
      />
    );
  }

  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col gap-1 break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {(msg.status === "completed" || msg.status === "rejected") &&
          (validatedTaskArguments ? (
            validatedTaskArguments.edits.length > 0 ? (
              <div className="flex flex-col gap-1">
                <PromptDiff
                  promptData={validatedTaskArguments.edits}
                  diffPromptData={validatedTaskArguments.edits}
                  rowId={validatedTaskArguments.index.toString()}
                  hideDiffDisclaimer
                  hideLineNumbers
                />
              </div>
            ) : (
              <div className="text-xs font-normal text-primary-500">
                No prompt edits were suggested
              </div>
            )
          ) : (
            <SyntaxHighlight
              language="json"
              content={JSON.stringify(getToolOutputSafely(msg), null, 2)}
              className="bg-primary-100"
            />
          ))}
      </div>
    </CollapsibleAction>
  );
};

export const Title = ({ msg }: { msg: ToolInteraction }) => (
  <div className="flex w-full items-center justify-between font-medium">
    {toolLabel(msg)}
    {msg.status === "pending_output" && (
      <Spinner className="ml-2 inline size-3 text-primary-500" />
    )}
    {msg.status === "error_executing_tool" && (
      <span className="ml-2 font-normal text-bad-600">Error</span>
    )}
    {msg.status === "completed" && (
      <span className="ml-2 font-normal text-primary-400">Completed</span>
    )}
    {msg.status === "rejected" && (
      <span className="ml-2 font-normal text-primary-400">Rejected</span>
    )}
  </div>
);

export const CreateCodeScorerToolDisplay = ({
  msg,
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
}: {
  msg: ToolInteraction;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (
    selectedContinueWithoutConsent: boolean,
  ) => void;
}) => {
  const validatedCreateCodeScorerArguments = useMemo(() => {
    return msg.functionName === "create_code_scorer" &&
      (msg.status === "completed" || msg.status === "rejected")
      ? isValidToolData(createCodeScorerParamsSchema)(msg.arguments)
        ? msg.arguments
        : null
      : null;
  }, [msg]);

  if (msg.status === "pending_output" && createCodeScorerConfirmationData) {
    return (
      <CreateCodeScorerMessage
        createCodeScorerConfirmationData={createCodeScorerConfirmationData}
        setCreateCodeScorerConfirmationData={
          setCreateCodeScorerConfirmationData
        }
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
      />
    );
  }
  return (
    <CollapsibleAction
      title={<Title msg={msg} />}
      defaultCollapsed={true}
      pending={msg.status === "pending_output"}
    >
      <div className="flex flex-col gap-1 break-all rounded-b-md border-x border-b p-2 bg-primary-100">
        {msg.status === "error_executing_tool" && (
          <span className="text-xs font-normal text-bad-700">
            {getToolErrorSafely(msg) || "Tool error occurred"}
          </span>
        )}
        {(msg.status === "completed" || msg.status === "rejected") &&
          validatedCreateCodeScorerArguments && (
            <div className="flex flex-col">
              <CollapsibleAction
                title={
                  <div className="flex w-full items-center justify-between font-normal">
                    <div className="flex items-center gap-2">
                      <Percent className="size-3 text-primary-500" />{" "}
                      <span className="text-primary-900">
                        {validatedCreateCodeScorerArguments.name}
                      </span>
                    </div>
                    <span className="text-xs text-primary-500">
                      {validatedCreateCodeScorerArguments.runtime === "python"
                        ? "Python"
                        : "Typescript"}
                    </span>
                  </div>
                }
                defaultCollapsed={true}
                nested
              >
                <div className="group rounded-b-md border-x border-b px-2 bg-background">
                  <div className="relative">
                    <CodeEditor
                      savedCode={validatedCreateCodeScorerArguments.code}
                      language={
                        validatedCreateCodeScorerArguments.runtime === "python"
                          ? "py"
                          : "ts"
                      }
                      hideLineNumbers
                      hideBanner
                      readOnly
                      className="-mx-2 text-xs"
                    />
                    <CopyToClipboardButton
                      textToCopy={validatedCreateCodeScorerArguments.code}
                      className="absolute right-0 top-1 opacity-0 transition-opacity text-primary-400 group-hover:opacity-100"
                      variant="ghost"
                      size="xs"
                    />
                  </div>
                </div>
              </CollapsibleAction>
            </div>
          )}
      </div>
    </CollapsibleAction>
  );
};

export const UserConsentMessage = ({
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
}: {
  userConsentConfirmationData: UserConsentConfirmationData;
  setUserConsentConfirmationData: (
    data: UserConsentConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
}) => {
  const [selectedContinueWithoutConsent, setSelectedContinueWithoutConsent] =
    useState(userConsentConfirmationData.continueWithoutConsent);

  return (
    <div className="flex flex-col  bg-primary-100">
      <div className="rounded-t-md border-x border-t p-2 text-xs">
        Do you want to run evaluations to optimize your prompt?
      </div>
      <ConfirmationFooter
        selectedContinueWithoutConsent={selectedContinueWithoutConsent}
        setSelectedContinueWithoutConsent={setSelectedContinueWithoutConsent}
        onCancel={() => {
          userConsentConfirmationData.onCancel();
          setUserConsentConfirmationData(null);
        }}
        onConfirm={() => {
          userConsentConfirmationData.onConfirm({
            continueWithoutConsent: selectedContinueWithoutConsent,
          });
          setUserConsentConfirmationData(null);
        }}
      />
    </div>
  );
};

const ConfirmationFooter = ({
  selectedContinueWithoutConsent,
  setSelectedContinueWithoutConsent,
  onCancel,
  onConfirm,
}: {
  selectedContinueWithoutConsent: boolean;
  setSelectedContinueWithoutConsent: (value: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
}) => {
  return (
    <div className="flex items-center justify-between rounded-b-md border-x border-b p-2 bg-primary-100">
      <BasicTooltip tooltipContent="Automatically accept the changes without asking for confirmation. This can be changed anytime in the chat settings">
        <Label className="flex items-center gap-1 text-xs text-primary-500">
          <Switch
            className="scale-75"
            checked={selectedContinueWithoutConsent}
            onCheckedChange={(checked) =>
              setSelectedContinueWithoutConsent(checked)
            }
          />
          Auto-accept
        </Label>
      </BasicTooltip>
      <div className="flex items-center gap-1">
        <SkipButton onClick={onCancel} />
        <AcceptButton onClick={onConfirm} />
      </div>
    </div>
  );
};
