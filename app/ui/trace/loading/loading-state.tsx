import {
  getSpanBySpanId,
  type PreviewTrace,
  type SpanIdsMap,
} from "#/ui/trace/graph";

export type LoadingState = "loading" | "not_found" | "loaded";

export function calculateLoadingState({
  isTraceQueryLoading,
  hasNoLoadedTraceData,
  primaryTrace,
  primaryRowId,
  loadedSpanId,
  spanIdsMap,
  isConfigLoading,
}: {
  isTraceQueryLoading?: boolean;
  hasNoLoadedTraceData?: boolean;
  primaryTrace?: PreviewTrace | null;
  primaryRowId?: string | null;
  loadedSpanId?: string | null;
  spanIdsMap: SpanIdsMap;
  isConfigLoading?: boolean;
}): LoadingState {
  if (isTraceQueryLoading || isConfigLoading) {
    return "loading";
  }

  if (hasNoLoadedTraceData) {
    return "not_found";
  }

  if (
    primaryTrace &&
    primaryRowId === primaryTrace.root.id &&
    getSpanBySpanId({
      spanId: loadedSpanId,
      spanIdsMap,
      spans: primaryTrace?.spans,
    })
  ) {
    return "loaded";
  }
  return "loading";
}
