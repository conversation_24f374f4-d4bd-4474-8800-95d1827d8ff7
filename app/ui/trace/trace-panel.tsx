"use client";

import { memo, useCallback, useEffect } from "react";
import { type TraceViewParams, TraceViewer } from "#/ui/trace/trace";
import { makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { type RowId } from "#/utils/diffs/diff-objects";
import { useActiveRowAndSpan, useActiveCommentId } from "#/ui/query-parameters";
import { HotkeyScope } from "#/ui/hotkeys";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { TraceSearchProvider } from "./trace-search-context";
import { useRowNavigation } from "./use-row-navigation";

/**
 * In order to allow Sidepanel have access to duck DB and run queries
 * It should have OrgProvider (to get user context)
 * and SessionRoot (to get duckdb context)
 */
export const TracePanel = memo(
  ({
    rowIds = [],
    traceViewParams,
    onApplySearch,
    isReadOnly,
  }: {
    rowIds: RowId[];
    traceViewParams: TraceViewParams;
    onApplySearch: ApplySearch;
    isReadOnly?: boolean;
  }) => {
    const { enableScope, disableScope, enabledScopes } = useHotkeysContext();
    useEffect(() => {
      enableScope("sidepanel");
      return () => {
        disableScope("sidepanel");
      };
    }, [enableScope, disableScope]);

    const {
      activeRowId,
      rowIdx,
      hasNextRow,
      hasPrevRow,
      onNextRow,
      onPrevRow,
      jumpToRow,
      isDelayedSpanChangeTransitioning,
      startSpanChangeTransition,
    } = useRowNavigation({ rowIds });

    useHotkeys(
      ["j", "ArrowDown"],
      () => onNextRow({ withTransition: true }),
      {
        scopes: ["sidepanel"],
        description: "Move to the next row in the table",
        preventDefault: true,
      },
      [onNextRow],
    );
    useHotkeys(
      ["k", "ArrowUp"],
      () => onPrevRow({ withTransition: true }),
      {
        scopes: ["sidepanel"],
        description: "Move to the previous row in the table",
        preventDefault: true,
      },
      [onPrevRow],
    );

    const fullyCloseSidePanel = useFullyCloseSidePanel();

    useHotkeys(
      ["Escape"],
      fullyCloseSidePanel,
      {
        scopes: ["sidepanel"],
        description: "Close the side panel.",
        enabled: ![
          HotkeyScope.GlobalKeyboardShortcuts,
          HotkeyScope.PromptModal,
          HotkeyScope.ConfirmationModal,
          HotkeyScope.ExpandedTrace,
          HotkeyScope.ExpandedFrame,
          HotkeyScope.HumanReview,
        ].some((scope) => enabledScopes.includes(scope)),
      },
      [fullyCloseSidePanel],
    );

    if (!activeRowId) {
      return null;
    }

    return (
      <TraceSearchProvider activeRowId={makeRowIdPrimary(activeRowId)}>
        <TraceViewer
          rowId={activeRowId}
          traceViewParams={traceViewParams}
          onClose={fullyCloseSidePanel}
          totalRows={rowIds.length}
          rowIndex={rowIdx + 1}
          firstRowId={rowIds[0]}
          onPrevRow={hasPrevRow ? onPrevRow : undefined}
          onNextRow={hasNextRow ? onNextRow : undefined}
          onJumpToRow={jumpToRow}
          onApplySearch={onApplySearch}
          isReadOnly={isReadOnly}
          isDelayedSpanChangeTransitioning={isDelayedSpanChangeTransitioning}
          startSpanChangeTransition={startSpanChangeTransition}
        />
      </TraceSearchProvider>
    );
  },
);

TracePanel.displayName = "TracePanel";

export function useFullyCloseSidePanel() {
  const [_activeRowAndSpan, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_activeCommentId, setFocusedCommentId] = useActiveCommentId();
  const { setFullscreenState } = useTraceFullscreen();
  return useCallback(() => {
    setActiveRowAndSpan({ r: null, s: null });
    setFocusedCommentId(null);
    setFullscreenState(null);
  }, [setActiveRowAndSpan, setFullscreenState, setFocusedCommentId]);
}
