import { Database, Ellipsis, MessagesSquare } from "lucide-react";
import { useState, useRef } from "react";
import { type Span } from "@braintrust/local";
import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { BasicTooltip } from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { useSpanPromptMeta } from "./use-span-prompt-meta";
import { parsePromptFromSpan } from "@braintrust/local";
import { useHotkeys } from "react-hotkeys-hook";
import { fetchBtql } from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { promptSchema } from "#/ui/prompts/schema";
import { type ProjectContextDataset } from "#/app/app/[org]/p/[project]/project-actions";

type DatasetDropdownButtonProps = {
  span: Span;
  projectId: string;
  orgDatasets: ProjectContextDataset[];
  performAddRowsToDataset: (params: {
    datasetName: string;
    datasetId: string;
    spans: Span[];
    selectedProjectId: string;
    selectedProjectName: string;
  }) => void;
  openCreateDatasetDialog: (
    name: string,
    getRows?: () => Promise<Span[]>,
  ) => void;
  spanTitleRef: React.RefObject<HTMLDivElement | null>;
};

export const DatasetDropdownButton = ({
  span,
  projectId,
  orgDatasets,
  performAddRowsToDataset,
  openCreateDatasetDialog,
  spanTitleRef,
}: DatasetDropdownButtonProps) => {
  const [datasetDropdownOpen, setDatasetDropdownOpen] = useState(false);
  const [spanFormat, setSpanFormat] = useState<"raw" | "playground">("raw");

  const parsedPrompt = parsePromptFromSpan(span) ?? undefined;
  const isLLMSpan = !!parsedPrompt && parsedPrompt.success;

  const spanPromptMeta = useSpanPromptMeta({
    span,
    projectId,
    isLLMSpan,
  });

  useHotkeys(
    "D",
    () => {
      setDatasetDropdownOpen(true);
      spanTitleRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    },
    {
      preventDefault: true,
      description: "Add span to dataset",
    },
  );
  useHotkeys(
    "s",
    () => {
      setDatasetDropdownOpen(true);
      setSpanFormat("playground");
      spanTitleRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    },
    {
      preventDefault: true,
      description: "Add formatted multi-turn messages to dataset",
    },
  );

  const org = useOrg();
  const btqlFlags = useBtqlFlags();

  const signal = useRef<AbortController>(new AbortController());
  const { getOrRefreshToken } = useSessionToken();

  const getPlaygroundFormattedSpan = async () => {
    if (!isLLMSpan || !spanPromptMeta?.id) {
      return span;
    }
    signal.current = new AbortController();
    try {
      const result = await fetchBtql({
        args: {
          query: {
            from: Query.from("project_prompts", [spanPromptMeta.project_id]),
            filter: {
              op: "eq",
              left: { op: "ident", name: ["id"] },
              right: { op: "literal", value: spanPromptMeta.id },
            },
            select: [{ op: "star" }],
            limit: 1,
          },
          brainstoreRealtime: true,
          disableLimit: false,
        },
        btqlFlags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        signal: signal.current.signal,
      });

      const maybePrompt = result.data[0];
      const prompt = promptSchema.safeParse(maybePrompt);
      if (!prompt.success) {
        return span;
      }

      const savedPromptMessages = prompt.data.prompt_data?.prompt;
      const parsedPromptMessages = parsedPrompt.data.prompt;
      const isChat =
        savedPromptMessages?.type === "chat" &&
        parsedPromptMessages?.type === "chat";
      const appendedMessages = isChat
        ? parsedPromptMessages?.messages?.slice(
            savedPromptMessages.messages.length,
          )
        : [];

      const formattedSpan: Span = {
        ...span,
        data: {
          ...span.data,
          input: {
            // spread variables into input so that saved prompts which were run with `myVariable` can still use `{{myVariable}}` without modifications
            ...(spanPromptMeta.variables ?? {}),
            // add appendedMessages to input for easy use of the extra multi-turn messages in the playground
            ...(appendedMessages.length > 0 ? { appendedMessages } : {}),
          },
        },
      };
      return formattedSpan;
    } catch (e) {
      return span;
    }
  };

  const spanRef = useRef<HTMLSpanElement>(null);
  // Show multi-turn formatted option for LLM spans from saved prompts
  if (isLLMSpan && spanPromptMeta?.id) {
    return (
      <DatasetDropdown
        datasets={orgDatasets}
        onSelectDataset={async (dataset) => {
          const formattedSpan =
            spanFormat === "playground"
              ? await getPlaygroundFormattedSpan()
              : span;
          performAddRowsToDataset({
            datasetName: dataset.name,
            datasetId: dataset.id,
            spans: [formattedSpan],
            selectedProjectId: dataset.project_id,
            selectedProjectName: dataset.project_name,
          });
        }}
        onCreateNewDataset={(name: string) =>
          openCreateDatasetDialog(
            name,
            spanFormat === "playground"
              ? async () => [await getPlaygroundFormattedSpan()]
              : undefined,
          )
        }
        open={datasetDropdownOpen}
        setOpen={(newOpen) => {
          if (!datasetDropdownOpen) {
            return;
          }
          setDatasetDropdownOpen(newOpen);
        }}
      >
        <div>
          <BasicTooltip
            side="left"
            tooltipContent={
              <div className="flex items-center gap-2">
                <p className="whitespace-pre-wrap text-xs">
                  Add span to dataset
                </p>
                <span className="ml-2.5 inline-block opacity-50">
                  {renderHotkey("D")}
                </span>
              </div>
            }
          >
            <Button
              size="xs"
              className="truncate rounded-r-none border-r-0"
              Icon={Database}
              onClick={() => {
                setDatasetDropdownOpen(true);
                setSpanFormat("raw");
              }}
            >
              <span className="flex-1 truncate" ref={spanRef}>
                Add span to dataset
              </span>
            </Button>
          </BasicTooltip>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="xs"
                className="truncate rounded-l-none"
                Icon={Ellipsis}
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <BasicTooltip
                side="left"
                sideOffset={10}
                tooltipContent={
                  <p className="whitespace-pre-wrap text-xs text-primary-500">
                    Move{" "}
                    <span className="font-mono text-primary-700">
                      metadata.prompt.variables
                    </span>{" "}
                    into{" "}
                    <span className="font-mono text-primary-700">input</span>{" "}
                    and the extra multi-turn messages into{" "}
                    <span className="font-mono text-primary-700">
                      input.appendedMessages
                    </span>
                    .
                  </p>
                }
              >
                <DropdownMenuItem
                  onClick={() => {
                    setDatasetDropdownOpen(true);
                    setSpanFormat("playground");
                  }}
                >
                  <MessagesSquare className="size-3 text-primary-700" />
                  <span>Add playground formatted span</span>
                  <span className="ml-2.5 inline-block opacity-50">
                    {renderHotkey("S")}
                  </span>
                </DropdownMenuItem>
              </BasicTooltip>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </DatasetDropdown>
    );
  }

  return (
    <DatasetDropdown
      datasets={orgDatasets}
      onSelectDataset={(dataset) => {
        performAddRowsToDataset({
          datasetName: dataset.name,
          datasetId: dataset.id,
          spans: [span],
          selectedProjectId: dataset.project_id,
          selectedProjectName: dataset.project_name,
        });
      }}
      onCreateNewDataset={(name: string) => openCreateDatasetDialog(name)}
      open={datasetDropdownOpen}
      setOpen={setDatasetDropdownOpen}
    >
      <div>
        <BasicTooltip
          side="bottom"
          tooltipContent={
            <>
              Add span to dataset
              <span className="ml-2.5 inline-block opacity-50">
                {renderHotkey("D")}
              </span>
            </>
          }
        >
          <Button size="xs" className="truncate" Icon={Database}>
            <span className="flex-1 truncate">Add span to dataset</span>
          </Button>
        </BasicTooltip>
      </div>
    </DatasetDropdown>
  );
};
