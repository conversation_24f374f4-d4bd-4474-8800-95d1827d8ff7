import { describe, it, expect } from "vitest";
import { buildVirtualTrace } from "./use-virtual-trace";
import { type SpanOverviewRow } from "./use-virtual-trace";
import { v4 as uuidv4 } from "uuid";

const ROOT_SPAN_ID = uuidv4();

describe("buildVirtualTrace", () => {
  function makeSpanOverviewRow({
    spanId,
    spanParents,
    rootSpanId,
    scores,
  }: {
    spanId?: string;
    spanParents?: string[];
    rootSpanId?: string;
    scores?: Record<string, number>;
  }): SpanOverviewRow {
    return {
      id: uuidv4(),
      span_id: spanId ?? uuidv4(),
      root_span_id: rootSpanId ?? ROOT_SPAN_ID,
      span_parents: spanParents ?? [],
      span_attributes: { name: "test" },
      scores,
      _xact_id: "0",
      created: "2021-01-01T00:00:00.000Z",
    };
  }

  it("returns null with an empty span list", () => {
    const rows: SpanOverviewRow[] = [];

    const trace = buildVirtualTrace(rows);
    expect(trace).toBe(null);
  });

  it("returns null when no root span is found", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["A"],
      }),
    ];

    const trace = buildVirtualTrace(rows);
    expect(trace).toBe(null);
  });

  it("throws error when span list contains an invalid root span ", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "A",
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["B"],
        rootSpanId: "other",
      }),
    ];

    expect(() => buildVirtualTrace(rows)).toThrowError(
      /does not match root span/,
    );
  });

  it("handles self-cycle", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["A"],
      }),
    ];

    const trace = buildVirtualTrace(rows);
    const cycleSpan = trace?.spans["A"];
    expect(cycleSpan).toBeDefined();
    expect(cycleSpan?.children.length).toBe(0);
  });

  it("handles two span cycle", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["B"],
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["A"],
      }),
    ];

    const trace = buildVirtualTrace(rows);
    const spanA = trace?.spans["A"];
    expect(spanA).toBeDefined();
    expect(spanA?.children.length).toBe(0);
    const spanB = trace?.spans["B"];
    expect(spanB).toBeDefined();
    expect(spanB?.children.length).toBe(1);
  });

  it("puts orphan spans under root", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["other"],
      }),
      // skips spans with no parents
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: [],
      }),
    ];

    const trace = buildVirtualTrace(rows);
    expect(trace).toBeDefined();
    // Check that the orphan spans are under the root
    expect(trace?.root.children.map((c) => c.span_id)).toEqual(["A"]);
    expect(trace?.spans["A"].span_parents).toEqual(["other"]);
    expect(trace?.spans["B"].span_parents).toEqual([]);
  });

  it("merges scores onto root", () => {
    const rows: SpanOverviewRow[] = [
      makeSpanOverviewRow({
        spanId: "root",
        scores: {
          root: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "A",
        spanParents: ["root"],
        scores: {
          A: 1,
        },
      }),
      makeSpanOverviewRow({
        spanId: "B",
        spanParents: ["A"],
        scores: {
          B: 1,
        },
      }),
    ];

    const trace = buildVirtualTrace(rows);
    expect(trace?.root.scores).toBeDefined();
    expect(trace?.root.scores).toEqual({
      A: {
        isDiff: undefined,
        left: [],
        right: [1],
        spanId: "A",
      },
      B: {
        isDiff: false,
        left: [],
        right: [1],
        spanId: "B",
      },
      root: {
        isDiff: undefined,
        left: [],
        right: [1],
        spanId: "root",
      },
    });
  });
});
