import { Input } from "#/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { useCallback, useMemo, useState } from "react";
import {
  Copy,
  Ellipsis,
  Lock,
  PenLine,
  Pin,
  Plus,
  Search,
  Trash,
} from "lucide-react";
import { cn } from "#/utils/classnames";
import { searchMatch } from "#/utils/string-search";
import { type PropsWithChildren } from "react";
import { Button } from "#/ui/button";
import { type View } from "#/utils/view/use-view";
import { BasicTooltip } from "#/ui/tooltip";
import { useEntityStorage } from "#/lib/clientDataStorage";

interface ViewDropdownProps {
  views: View[] | undefined;
  isLoadingViews: boolean;
  loadView: (view: View | null) => void;
  selectedView?: View;
  viewNameOverride?: string;
  setCreateViewDialogOpen: (open: boolean) => void;
  setRenameViewDialogOpen: (open: boolean) => void;
  setDeleteViewDialogOpen: (open: boolean) => void;
  pageIdentifier: string;
  descriptionText?: string;
}

export function ViewDropdown({
  views,
  selectedView,
  viewNameOverride,
  loadView,
  isLoadingViews,
  setCreateViewDialogOpen,
  setRenameViewDialogOpen,
  setDeleteViewDialogOpen,
  pageIdentifier,
  descriptionText,
}: PropsWithChildren<ViewDropdownProps>) {
  const [query, setQuery] = useState("");

  const filteredViews = useMemo(() => {
    return views?.filter((v) => searchMatch({ query, text: v.name ?? "" }));
  }, [views, query]);

  const isEditable = Boolean(selectedView?.id && !selectedView?.builtin);

  const [defaultViewName, setDefaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const setCurrentViewAsDefault = useCallback(() => {
    setDefaultViewName(selectedView?.name ?? null);
  }, [selectedView, setDefaultViewName]);

  return (
    <div className="flex">
      <DropdownMenu
        onOpenChange={(open) => {
          if (!open) {
            setQuery("");
          }
        }}
      >
        <DropdownMenuTrigger asChild>
          <Button
            size="xs"
            className={cn({
              "rounded-r-none": isEditable,
            })}
            disabled={!selectedView || isLoadingViews}
            isDropdown
          >
            {selectedView?.builtin && (
              <BasicTooltip tooltipContent="This view is read-only and cannot be modified">
                <Lock className="size-3 flex-none text-primary-400" />
              </BasicTooltip>
            )}
            <span className="max-w-40 truncate">
              {viewNameOverride ?? selectedView?.name ?? "Views"}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent autoFocus align="start" className="max-w-80">
          <label
            className="mb-1 flex h-8 items-center rounded-sm px-2 bg-primary-100"
            onKeyDown={(e) => e.stopPropagation()}
          >
            <Search className="size-3 text-primary-500" />
            <Input
              placeholder="Find a view"
              autoFocus
              className="h-8 border-0 pl-2 pr-0 text-xs outline-none ring-0 bg-transparent focus-visible:border-0 focus-visible:ring-0"
              onChange={(e) => {
                setQuery(e.target.value);
              }}
            />
          </label>
          {filteredViews?.length === 0 && (
            <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
              No views found
            </DropdownMenuLabel>
          )}
          {filteredViews && filteredViews.length > 0 && (
            <DropdownMenuGroup className="overflow-none max-h-[calc(100vh-300px)]">
              {filteredViews.map((v) => (
                <DropdownMenuCheckboxItem
                  key={v.id}
                  checked={v.id === selectedView?.id}
                  onSelect={() => {
                    loadView(v.id ? v : null);
                    setQuery("");
                  }}
                  className="flex items-center gap-2"
                >
                  {v.builtin && (
                    <BasicTooltip tooltipContent="This view is read-only and cannot be modified">
                      <Lock className="size-3 flex-none text-primary-400" />
                    </BasicTooltip>
                  )}
                  <span className="flex-1">{v.name}</span>
                  {defaultViewName === v.name && (
                    <BasicTooltip tooltipContent="This is your default view">
                      <Pin className="size-3 flex-none text-primary-400" />
                    </BasicTooltip>
                  )}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuGroup>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onSelect={setCurrentViewAsDefault}
            disabled={!selectedView || selectedView.name === defaultViewName}
          >
            <Pin className="size-3" />
            Set current view as default
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => setCreateViewDialogOpen(true)}>
            <Plus className="size-3" />
            Create view
          </DropdownMenuItem>

          <DropdownMenuLabel>
            {descriptionText || (
              <>
                Save filters, sorts, and column configurations
                as&nbsp;a&nbsp;view
              </>
            )}
          </DropdownMenuLabel>
        </DropdownMenuContent>
      </DropdownMenu>
      {isEditable && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size="xs"
              className="rounded-l-none border-l-0"
              Icon={Ellipsis}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuGroup>
              <DropdownMenuItem
                onSelect={() => {
                  setCreateViewDialogOpen(true);
                }}
              >
                <Copy className="size-3" />
                Duplicate view
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  setRenameViewDialogOpen(true);
                }}
              >
                <PenLine className="size-3" />
                Rename view
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  setCurrentViewAsDefault();
                }}
              >
                <Pin className="size-3" />
                Set as default view
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  setDeleteViewDialogOpen(true);
                }}
              >
                <Trash className="size-3" />
                Delete view
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
