import { type SavingState } from "#/ui/saving";
import { strMax, updatePathMut } from "#/utils/object";
import { isEmpty, MERGE_PATHS_FIELD } from "@braintrust/core";
import { type AsyncPreparedStatement } from "@duckdb/duckdb-wasm";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useMemo,
} from "react";
import type { DataObjectSearch, DataObjectType } from "./btapi/btapi";
import {
  ObjectDeleteField,
  type TransactionId,
  TransactionIdField,
  type UpdateLog,
  performUpsert,
  useDBQuery,
  useDuckConn,
  useParquetView,
} from "./duckdb";
import { ObjectIdFields, type CommentData } from "@braintrust/local/api-schema";
import {
  type IdentifiableRow,
  type IdentifiableRowWithOrigin,
  extractIdentifiableRow,
  parseObjectJSON,
} from "./schema";
import { useOrg, useUser } from "./user";
import { toast } from "sonner";
import { useSessionToken } from "./auth/session-token";
import { type Expr } from "@braintrust/btql/parser";
import { singleQuote } from "./sql-utils";
import { bindBTQLExpr } from "./search-btql";
import { useBtqlQueryBuilder } from "./btql/use-query-builder";
import { FAST_OBJECT_TYPES, useFeatureFlags } from "#/lib/feature-flags";

export type UpdateRowFn = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  row: any,
  path: string[],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  newValue: any,
) => Promise<TransactionId | null>;

type DMLInfo =
  | {
      type: "insert";
    }
  | {
      type: "update";
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      updateRowFn: (fullRow: any) => any;
    }
  | {
      type: "delete";
    };

export type CommentFn = (
  comments: {
    id: string;
    row: IdentifiableRow;
    comment: CommentData;
  }[],
) => Promise<TransactionId | null>;

export type DeleteCommentFn = (
  row: IdentifiableRowWithOrigin,
) => Promise<TransactionId | null>;

// TODO (for Manu): We can introduce a type here for the row, including
// some semantics around what's JSON vs. not, and then use that
export interface DML {
  prepareUpdates: (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rows: any[],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    updates: { path: string[]; newValue: any }[],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  ) => Promise<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  prepareDeletes: (rows: any[]) => Promise<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  prepareUpserts: (rows: any[]) => Promise<any>;
  upsert: (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rows: any[],
    opts?: {
      onOptimisticUpdate?: () => void;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      pathUpdates?: any[];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      onOptimisticDataUpdate?: (updatedRows: any[]) => void;
    },
  ) => Promise<TransactionId | null>;
  update: (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rows: any[],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    updates: { path: string[]; newValue: any }[],
  ) => Promise<TransactionId | null>;
  commentOn: CommentFn;
  deleteComment: DeleteCommentFn;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export async function deleteRow(dml: DML, row: any) {
  const rows = await dml.prepareDeletes([row]);
  return await dml.upsert(rows);
}

export const EMPTY_CHANNEL = () => null;

export function useMutableObject({
  scan,
  objectType,
  objectId,
  channel = EMPTY_CHANNEL,
  auditLogChannel = EMPTY_CHANNEL,
  setSavingState,
}: {
  scan: string | null;
  objectType: DataObjectType;
  channel?: () => UpdateLog | null;
  auditLogChannel?: () => UpdateLog | null;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  // If specified, use this instead of the scan to determine the ids of the row to update.
  objectId?: string;
}) {
  const { user } = useUser();

  const toastError = useCallback(
    (actionName: string, description: string) => {
      setSavingState?.(new Error(description));
      toast.error(`Could not ${actionName} row`, {
        description: (
          <>
            Could not {actionName} row ({description}). Please contact{" "}
            <a href="mailto:<EMAIL>" className="underline">
              <EMAIL>
            </a>{" "}
            for help.
          </>
        ),
      });
    },
    [setSavingState],
  );

  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const userId = user?.id;

  const { conn } = useDuckConn();

  const stmtQuery = useMemo(
    () => scan && `SELECT * FROM (${scan}) "t" WHERE t.id = ?`,
    [scan],
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const stmtPromise: Promise<AsyncPreparedStatement<any>> | null =
    useMemo(() => {
      if (!stmtQuery || !conn) {
        return null;
      }
      return conn.prepare(stmtQuery);
    }, [conn, stmtQuery]);

  const {
    flags: { fastExperimentSummary },
  } = useFeatureFlags();

  // If an updateRowFn is provided, the row is updated, else it is deleted.
  const prepareRows = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (rows: any[], dml: DMLInfo) => {
      const { actionName, actionCapName } =
        dml.type === "update"
          ? {
              actionName: "edit",
              actionCapName: "Edit",
            }
          : dml.type === "insert"
            ? {
                actionName: "insert",
                actionCapName: "Insert",
              }
            : {
                actionName: "delete",
                actionCapName: "Delete",
              };

      if (!stmtPromise) {
        toastError(
          actionName,
          `${actionCapName} functionality not initialized`,
        );
        throw new Error(`${actionCapName} functionality not initialized`);
      }
      const stmt = await stmtPromise;

      if (dml.type !== "insert") {
        if (
          objectId &&
          // This is a total hack, but it's essentially saying "if this is a summary table, then dont just use
          // the object ids". This is important because of how real-time updates work. I'm ok with hacking it
          // like this because we're getting rid of this soon (in favor of explicitly refetching mutated rows).
          // comparison_key and span_type_info are both fields that are only present in summary tables.
          ((fastExperimentSummary && FAST_OBJECT_TYPES.includes(objectType)) ||
            (rows.length > 0 &&
              isEmpty(rows[0].comparison_key) &&
              isEmpty(rows[0].span_type_info)))
        ) {
          rows = rows.map((row) => {
            let rowWithIds = {
              ...row,
              ...objectTypeAndIdToObjetIds(objectType, objectId),
            };
            if (dml.type === "update") {
              rowWithIds = dml.updateRowFn(rowWithIds);
            }
            if (dml.type === "delete") {
              rowWithIds = {
                ...extractIdentifiableRow(rowWithIds),
                [ObjectDeleteField]: true,
              };
            }
            return rowWithIds;
          });
        } else {
          // Extract the full rows from the table, so that when we update/delete
          // them, we have all required fields.
          rows = await Promise.all(
            rows.map(async (row) => {
              const result = await stmt!.query(row.id);
              // If we don't find the original row, then just use the row we were given.
              // Merge semantics will forgive us.
              let fullRow = result.toArray()[0] ?? row;
              if (dml.type === "update") {
                fullRow = dml.updateRowFn(fullRow);
              }
              if (dml.type === "delete") {
                fullRow = {
                  ...extractIdentifiableRow(fullRow),
                  [ObjectDeleteField]: true,
                };
              }
              return fullRow;
            }),
          );
        }
      }
      return rows;
    },
    [objectId, objectType, stmtPromise, toastError, fastExperimentSummary],
  );

  const upsertRows = useCallback(
    async (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      rows: any[],
      {
        onOptimisticUpdate,
        pathUpdates,
        logChannel,
        onOptimisticDataUpdate,
      }: {
        onOptimisticUpdate?: () => void;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        pathUpdates?: any[];
        logChannel?: (() => UpdateLog | null) | null;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        onOptimisticDataUpdate?: (updatedRows: any[]) => void;
      } = {},
    ) => {
      if (!apiUrl || !userId) {
        toastError("write", `api functionality not initialized`);
        throw new Error(`api functionality not initialized`);
      }

      // Call optimistic data update immediately before server request
      onOptimisticDataUpdate?.(rows);

      setSavingState?.("saving");
      const sessionToken = await getOrRefreshToken();
      let ret = null;
      try {
        ret = await performUpsert(
          // If the channel is unspecified, then default to the main channel, but if it's
          // specified as null, then don't send any channel.
          logChannel
            ? logChannel()
            : logChannel === undefined
              ? channel()
              : null,
          apiUrl,
          sessionToken,
          userId,
          rows,
          pathUpdates,
          onOptimisticUpdate,
        );
        setSavingState?.("saved");
      } catch (e) {
        toastError("update", `${e}`);
        setSavingState?.(new Error(`${e}`));
        throw e;
      }

      setTimeout(
        () => setSavingState?.((s) => (s === "saved" ? "none" : s)),
        5000,
      );
      return ret;
    },
    [apiUrl, userId, setSavingState, getOrRefreshToken, toastError, channel],
  );

  const prepareUpdateRows = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (rows: any[], updates: { path: string[]; newValue: any }[]) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const updateRowFn = (fullRow: any): any => {
        const newRow = parseObjectJSON(objectType, fullRow);
        for (const { path, newValue } of updates) {
          updatePathMut(newRow, path, newValue);
        }
        return newRow;
      };
      return await prepareRows(rows, { type: "update", updateRowFn });
    },
    [objectType, prepareRows],
  );

  const prepareDeleteRows = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (rows: any[]) => {
      return await prepareRows(rows, { type: "delete" });
    },
    [prepareRows],
  );

  const prepareUpsertRows = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (rows: any[]) => {
      return await prepareRows(rows, { type: "insert" });
    },
    [prepareRows],
  );

  const update = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (rows: any[], updates: { path: string[]; newValue: any }[]) => {
      const rowsWithUpdates = await prepareUpdateRows(rows, updates);
      const pathUpdates = rows.map((fullRow) => {
        const newRow = {
          id: fullRow.id,
          ...Object.fromEntries(
            ObjectIdFields.filter((k) => fullRow[k]).map((k) => [
              k,
              fullRow[k],
            ]),
          ),
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          [MERGE_PATHS_FIELD]: [] as string[][],
        };
        for (const { path, newValue } of updates) {
          updatePathMut(newRow, path, newValue);
          newRow[MERGE_PATHS_FIELD].push(path);
        }
        return newRow;
      });
      return await upsertRows(rowsWithUpdates, {
        pathUpdates,
      });
    },
    [prepareUpdateRows, upsertRows],
  );

  const commentOn = useCallback(
    async (
      comments: {
        id: string;
        row: IdentifiableRow;
        comment: CommentData;
      }[],
    ) => {
      if (!userId) {
        toastError("comment", `api functionality not initialized`);
        throw new Error(`api functionality not initialized`);
      }
      const commentRows = comments.map(({ id, row, comment }) => ({
        id,
        ...Object.fromEntries(ObjectIdFields.map((k) => [k, row[k]])),
        origin: {
          id: row.id,
          [TransactionIdField]: row[TransactionIdField],
        },
        source: "app",
        metadata: {
          user_id: userId,
        },
        comment,
      }));
      return await upsertRows(commentRows, {
        logChannel: auditLogChannel ?? null,
      });
    },
    [auditLogChannel, toastError, upsertRows, userId],
  );

  const deleteComment = useCallback(
    async (row: IdentifiableRowWithOrigin) => {
      if (!apiUrl || !userId) {
        toastError("comment", `api functionality not initialized`);
        throw new Error(`api functionality not initialized`);
      }
      const sessionToken = await getOrRefreshToken();
      const deleteRow = {
        id: row.id,
        origin: row.origin,
        ...Object.fromEntries(ObjectIdFields.map((k) => [k, row[k]])),
        comment: {},
        [ObjectDeleteField]: true,
      };
      return await performUpsert(
        auditLogChannel ? auditLogChannel() : null,
        apiUrl,
        sessionToken,
        userId,
        [deleteRow],
      );
    },
    [apiUrl, auditLogChannel, toastError, userId, getOrRefreshToken],
  );

  const dml: DML = useMemo(
    () => ({
      prepareUpdates: prepareUpdateRows,
      prepareDeletes: prepareDeleteRows,
      prepareUpserts: prepareUpsertRows,
      upsert: upsertRows,
      update,
      commentOn,
      deleteComment,
    }),
    [
      commentOn,
      deleteComment,
      prepareDeleteRows,
      prepareUpdateRows,
      prepareUpsertRows,
      update,
      upsertRows,
    ],
  );

  return dml;
}

export interface DurableObjects {
  dml: DML;
  status: "loading" | "error" | "not_found" | "loaded" | "empty";
  xactId: string | null;
  scan: string | null;
  signals: number[];
  auditLogScan: string | null;
  auditLogSignals: number[];
  objects: Record<string, unknown>[] | null;
}

// This is not a perfect abstraction, but it's useful for querying sets of functions,
// e.g. a specific id (while opening a prompt) or all scorer functions, and doing the classic
// stuff (like DML) on them.
export function useDurableObjectByIds({
  setSavingState,
  objectType,
  objectId,
  ids,
  filters,
  version,
  disableCache,
}: {
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  objectType: DataObjectType;
  objectId: string | undefined;
  ids: string[] | undefined;
  filters?: Expr[];
  version: string | undefined;
  disableCache?: boolean;
}): DurableObjects {
  const builder = useBtqlQueryBuilder({});

  const search = useMemo(() => {
    if (!objectId) {
      return undefined;
    }
    const ret: DataObjectSearch = {
      id: objectId,
      version,
      filters: {
        sql: [
          {
            // DEVNOTE: This basically does not work with the old pre-btql filters, but
            // this is net new surface area so it should be fine. Once we remove sql as
            // an option, we can just delete this.
            type: "sql_filter",
            expr: "false",
          },
        ],
        btql: (ids
          ? [
              builder.or(
                ...ids.map(
                  (id) =>
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    ({
                      op: "eq",
                      left: { op: "ident", name: ["id"] },
                      right: { op: "literal", value: id },
                    }) as Expr,
                ),
              ),
            ]
          : []
        ).concat(filters ? filters : []),
      },
    };
    return ret;
  }, [builder, filters, ids, objectId, version]);

  const { refreshed, scan, channel, error } = useParquetView({
    objectType,
    search,
    disableCache,
  });

  const duckDBFilters = useMemo(() => {
    if (!filters) {
      return [];
    }
    return filters.map((filter) =>
      bindBTQLExpr({ table: objectType, expr: filter, topLevelFields: [] }),
    );
  }, [filters, objectType]);

  const { data: rowData } = useDBQuery(
    scan
      ? `SELECT * FROM (${scan}) base WHERE ${
          ids === undefined
            ? "true"
            : ids.length === 0
              ? "false"
              : ids.length === 1
                ? `id = ${singleQuote(ids[0])}`
                : `id IN (${ids.map((id) => singleQuote(id)).join(", ")})`
        }
        AND ${["true", ...duckDBFilters?.map((f) => f.sql.toPlainStringQuery())].join(" AND ")}
        `
      : null,
    [refreshed],
  );

  const objects = useMemo(
    () =>
      rowData && rowData.numRows > 0
        ? rowData
            .toArray()
            .map((row) => parseObjectJSON(objectType, row.toJSON()))
        : null,
    [objectType, rowData],
  );

  const maxXactId = useMemo(() => {
    return rowData && rowData?.numRows > 0
      ? rowData.toArray().reduce((max, row) => {
          const xactId = row.toJSON()[TransactionIdField];
          return strMax(max, xactId);
        }, "0")
      : null;
  }, [rowData]);
  const status: "loading" | "error" | "not_found" | "loaded" | "empty" =
    objectId && (ids === undefined || ids.length > 0)
      ? objects && objects.length > 0
        ? "loaded"
        : rowData?.numRows === 0
          ? "not_found"
          : !!error
            ? "error"
            : "loading"
      : "empty";

  const auditLogSearch = useMemo(
    () =>
      objectId && objectType.startsWith("project_")
        ? {
            id: objectId,
            audit_log: true,
          }
        : undefined,
    [objectId, objectType],
  );

  const {
    refreshed: auditLogReady,
    scan: auditLogScan,
    channel: auditLogChannel,
  } = useParquetView({
    objectType,
    search: auditLogSearch,
    disableCache,
  });

  const dml = useMutableObject({
    scan,
    objectType,
    channel,
    auditLogChannel,
    setSavingState,
  });

  const baseArgs = {
    scan,
    signals: useMemo(() => [refreshed], [refreshed]),
    auditLogScan,
    auditLogSignals: useMemo(() => [auditLogReady], [auditLogReady]),
    dml,
    status,
    xactId: maxXactId,
  };

  const memoObjects = useMemo(() => objects ?? [], [objects]);

  return {
    objects: memoObjects,
    ...baseArgs,
  };
}

function objectTypeAndIdToObjetIds(
  objectType: DataObjectType,
  objectId: string,
): Record<string, string> {
  switch (objectType) {
    case "project_logs":
      return {
        project_id: objectId,
        log_id: "g",
      };
    case "playground_logs":
      return {
        prompt_session_id: objectId,
        log_id: "x",
      };
    case "project_prompts":
    case "project_functions":
      return {
        project_id: objectId,
        log_id: "p",
      };
    case "org_prompts":
    case "org_functions":
      return {
        org_id: objectId,
        log_id: "p",
      };
    case "dataset":
      return {
        dataset_id: objectId,
      };
    case "experiment":
      return {
        experiment_id: objectId,
      };
    case "prompt_session":
      return {
        prompt_session_id: objectId,
      };
  }
}
