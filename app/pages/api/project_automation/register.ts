import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import {
  createProjectAutomationSchema,
  projectAutomationSchema,
} from "@braintrust/core/typespecs";
import { z } from "zod";

const paramsSchema = createProjectAutomationSchema
  .omit({ name: true })
  .extend({
    project_automation_name: createProjectAutomationSchema.shape.name,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

const outputSchema = z.object({
  project_automation: projectAutomationSchema,
  found_existing: z.boolean(),
});

export default async function register(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_project_automation", {
    paramsSchema,
    postprocessOutput: (output) => {
      // strip the generated columns from the table
      return outputSchema.parse(output);
    },
    outputSchema,
    argnames: [
      "auth_id",
      "project_id",
      "project_automation_name",
      "description",
      "config",
      "update",
    ],
  });
}
