---
title: "View logs"
---

import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";
import Link from "fumadocs-core/link";

# View logs

To view logs, navigate to the **Logs** tab in the appropriate project in the Braintrust UI. Logs are automatically updated
in real-time as new traces are logged.

![Logs](./logs.png)

## Filtering logs

You can filter logs by tags, time range, and other fields using the **Filter** menu.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/logs/filter-logs-poster.png">
  <source src="/docs/guides/logs/filter-logs.mp4" type="video/mp4" />
</video>

### Create custom columns

Create [custom columns](/docs/guides/evals/interpret#create-custom-columns) to extract specific values from `input`, `output`, `expected`, or `metadata` fields.

### Braintrust Query Language (BTQL)

You can also filter by arbitrary subfields using [Braintrust Query Language syntax](/docs/reference/btql).

Here are a few examples of common filters:

| Description                                       | Syntax                                    |
| ------------------------------------------------- | ----------------------------------------- |
| Logs older than the past day                      | `created < CURRENT_DATE - INTERVAL 1 DAY` |
| Logs with a `user_id` field equal to `1234`       | `metadata.user_id = '1234'`               |
| Logs with a `Factuality` score greater than `0.5` | `scores.Factuality > 0.5`                 |

### Querying through the API

For basic filters and access to the logs, you can use the [project logs](/docs/reference/api/Projects#fetch-project-logs-post-form)
endpoint. This endpoint supports the same query syntax as the UI, and also allows you to specify additional fields to return.

For more advanced queries, you can use [BTQL](/docs/reference/btql#api-access) endpoint.

## Tags

Braintrust supports curating logs by adding tags, and then filtering on them in the UI. Tags naturally flow between logs, to datasets, and even
to experiments, so you can use them to track various kinds of data across your application, and track how they change over time.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/logs/Add-Tag-Poster.png">
  <source src="/docs/guides/logs/Add-Tag.mp4" type="video/mp4" />
</video>

### Configuring tags

Tags are configured at the project level, and in addition to a name, you can also specify a color and description.
To configure tags, navigate to the **Configuration** tab in a project, where you can add, modify, and delete tags.

![Configure tags](/docs/guides/logs/Configure-Tags.png)

### Adding tags in the SDK

Specify the `tags` field when you log data to add tags to logs using the SDK.

<CodeTabs>

<TSTab>

```javascript
import { wrapOpenAI, initLogger } from "braintrust";
import { OpenAI } from "openai";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});
const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));

export async function POST(req: Request) {
  return logger.traced(async (span) => {
    const input = await req.text();
    const result = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: input }],
    });
    span.log({ input, output: result, tags: ["user-action"] });
    return {
      result,
      requestId: span.id,
    };
  });
}
```

</TSTab>

<PYTab>

```python
from braintrust import init_logger

logger = init_logger(project="My Project")


def my_route_handler(req):
    with logger.start_span() as span:
        body = req.body
        result = some_llm_function(body)
        span.log(input=body, output=result, tags=["user-action"])
        return {
            "result": result,
            "request_id": span.span_id,
        }
```

</PYTab>

</CodeTabs>

<Callout type="warn">
  Tags can only be applied to top-level spans, e.g those created via `traced()`
  or `logger.startSpan()`/ `logger.start_span()`. You cannot apply tags to
  subspans (those created from another span), because they are properties of the
  whole trace, not individual spans.
</Callout>

You can also apply tags while capturing feedback via the `logFeedback()` / `log_feedback()` method.

<CodeTabs>

<TSTab>

```javascript
import { initLogger } from "braintrust";

const logger = initLogger({
  projectName: "My project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

export async function POSTFeedback(req: Request) {
  const { spanId, comment, score, userId } = await req.json();
  logger.logFeedback({
    id: spanId, // Use the newly created span's id, instead of the original request's id
    comment,
    scores: {
      correctness: score,
    },
    metadata: {
      user_id: userId,
    },
    tags: ["user-feedback"],
  });
}
```

</TSTab>
<PYTab>

```python
from braintrust import init_logger

logger = init_logger(project="My Project")


def my_feedback_handler(req):
    logger.log_feedback(
        id=req.body.request_id,
        scores={
            "correctness": req.body.score,
        },
        comment=req.body.comment,
        metadata={
            "user_id": req.user.id,
        },
        tags=["user-feedback"],
    )
```

</PYTab>

</CodeTabs>

### Filtering by tags

To filter by tags, select the tags you want to filter by in the UI.

<video className="border rounded-md" className="border rounded-md" src="/docs/guides/logs/Filter-Tag.mp4" loop autoPlay muted poster="/docs/guides/logs/Filter-Tag-Poster.png"></video>
