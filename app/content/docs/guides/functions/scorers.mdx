---
title: "Scorers"
---

import Image from 'next/image'
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# Scorers

Scorers in Braintrust allow you to evaluate the output of LLMs based on a set of criteria. These can include both heuristics (expressed as code) or prompts (expressed as LLM-as-a-judge). Scorers help you assign a performance score between 0 and 100% to assess how well the AI outputs match expected results. While many scorers are available out of the box in Braintrust, you can also create your own custom scorers directly in the UI or upload them via the command line. Scorers that you define in the UI can also be used as functions.

## Autoevals
There are several pre-built scorers available via the open-source [autoevals](https://github.com/braintrustdata/autoevals) library, which offers standard evaluation methods that you can start using immediately.

Autoeval scorers offer a strong starting point for a variety of evaluation tasks. Some autoeval scorers require configuration before they can be used effectively. For example, you might need to define expected outputs or certain parameters for specific tasks. To edit an autoeval scorer, you must copy it first.

While autoevals are a great way to get started, you may eventually need to create your own custom scorers for more advanced use cases.

## Custom scorers

You can create custom scorers in TypeScript, Python, or as an LLM-as-a-judge through the UI by navigating to **Library > Scorers** and selecting **Create scorer**. These scorers will be available to use as functions throughout your project. You can also upload custom scorers from the command line.

### TypeScript and Python scorers
For more specialized evals, you can create custom scorers in either TypeScript or Python. These code-based scorers are highly customizable and can return scores based on your exact requirements. Add your custom code to the `TypeScript` or `Python` tabs, and it will run in a sandboxed environment.

![Create TypeScript scorer](./scorers/code-scorer-ui.png)

This command will bundle and upload your custom scorer functions, making them accessible across your Braintrust projects.

### LLM-as-a-judge scorers

In addition to code-based scorers, you can also create LLM-as-a-judge scorers through the UI. For an LLM-as-a-judge scorer, you define a prompt that evaluates the AI's output and maps its choices to specific scores. You can also configure whether to use techniques like chain-of-thought (CoT) reasoning for more complex evaluations.

![Create LLM-as-a-judge scorer](./scorers/llm-as-a-judge-scorer-ui.png)

## Using a scorer in the UI
You can use both autoevals and custom scorers in the Braintrust Playground. In your playground, navigate to **Scorers** and select from the list of available scorers. You can also create a new custom scorer from this menu.

![Using scorer in playground](./scorers/using-scorers-in-playground.gif)

The Playground allows you to iterate quickly on prompts while running evaluations, making it the perfect tool for testing and refining your AI models and prompts.

## Pushing scorers via the CLI

As with tools, when writing custom scorers in the UI,
there may be restrictions on certain imports or functionality,
but you can always write your scorers in your own environment
and upload them for use in Braintrust.
This works for both code-based scorers and LLM-as-a-judge scorers.

<CodeTabs>

<TSTab>
```typescript title=scorer.ts
import braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "scorer" });

project.scorers.create({
  name: "Equality scorer",
  slug: "equality-scorer",
  description: "An equality scorer",
  parameters: z.object({
    output: z.string(),
    expected: z.string(),
  }),
  handler: async ({ output, expected }) => {
    return output == expected ? 1 : 0;
  },
});

project.scorers.create({
  name: "Equality LLM scorer",
  slug: "equality-llm-scorer",
  description: "An equality LLM scorer",
  messages: [
    {
      role: "user",
      content:
        'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
    },
  ],
  model: "gpt-4o",
  useCot: true,
  choiceScores: {
    A: 1,
    B: 0,
  },
});
```
</TSTab>

<PYTab>
```python title=scorer.py
import braintrust
import pydantic

project = braintrust.projects.create(name="scorer")


class Input(pydantic.BaseModel):
    output: str
    expected: str


def handler(output: str, expected: str) -> int:
    return 1 if output == expected else 0


project.scorers.create(
    name="Equality scorer",
    slug="equality-scorer",
    description="An equality scorer",
    parameters=Input,
    handler=handler,
)


project.scorers.create(
    name="Equality LLM scorer",
    slug="equality-llm-scorer",
    description="An equality LLM scorer",
    messages=[
        {
            "role": "user",
            "content": 'Return "A" if {{output}} is equal to {{expected}}, and "B" otherwise.',
        },
    ],
    model="gpt-4o",
    use_cot=True,
    choice_scores={"A": 1, "B": 0},
)
```
</PYTab>

</CodeTabs>

### Pushing to Braintrust

Once you define a scorer, you can push it to Braintrust with `braintrust push`:

<CodeTabs>

<TSTab>
```bash
npx braintrust push scorer.ts
```
</TSTab>

<PYTab>
```bash
braintrust push scorer.py
```
</PYTab>

</CodeTabs>

### Dependencies

Braintrust will take care of bundling the dependencies your scorer needs.

<CodeTabs>

<TSTab>
In TypeScript, we use [esbuild](https://esbuild.github.io/)
to bundle your code and its dependencies together.
This works for most dependencies,
but it does not support native (compiled) libraries like SQLite.

</TSTab>

<PYTab>
In Python, we use [uv](https://github.com/astral-sh/uv) to cross-bundle
a specified list of dependencies to the target platform (Linux).
This works for binary dependencies
except for libraries that require on-demand compilation.

```bash
braintrust push scorer.py --requirements requirements.txt
```
</PYTab>
</CodeTabs>

If you have trouble bundling your dependencies, let us know
by [filing an issue](https://github.com/braintrustdata/braintrust-sdk/issues).

## Using scorers in your evals

The scorers that you create in Braintrust are available throughout the UI, e.g. in the playground, but you can
also use them in your code-based evals. See [Using custom prompts/functions from Braintrust](/docs/guides/evals/write#using-custom-promptsfunctions-from-braintrust)
for more details.
