---
title: State of AI development 2023
description: "Retool recently surveyed over 1,500 workers and how their companies are adopting AI in their State of AI 2023 report. Here's what they are struggling with and how Braintrust can help them."
author: "<PERSON>"
date: "15 November 2023"
image: "/blog/meta/state-of-ai/opengraph-image.png"
twimage: "/blog/meta/state-of-ai/twitter-image.png"
---

import SignUpLink from "#/ui/docs/signup";
import CtaButton from "#/app/(landing)/cta-button";
import { BlogAuthor } from "#/ui/blog/blog-author";

# State of AI development 2023

<BlogAuthor author="<PERSON>" date="15 November 2023" />

Retool recently surveyed over 1,500 workers and how their companies are adopting AI in their [State of AI 2023 report](https://retool.com/reports/state-of-ai-2023).

It seems that most companies are building AI features and putting them into production!

- 66.2% said they have at least one internal AI use case live
- 43.1% said they have at least one external use case live

This is extremely exciting as workers get to save time with better AI internal tools and users get better user experiences with AI.

However, building AI apps is tough. Companies are running into challenges with building and productionizing their AI apps. Some of their top challenges include model output accuracy, hallucinations, and prompt engineering.

<figure>
  ![Retool Report: top pain points for
  development](/blog/img/ai-painpoints-retool.jpg)
</figure>

Everyone's trying to make AI apps they can count on. They're asking:

- "How do I evaluate my AI app?"
- "How do I integrate evaluations with CICD?"
- "How to use GPT-4 to score AI outputs instead of manually grading everything?"
- "How do I build my own internal evaluation system?"

These are tough questions. Companies are trying to find the best ways to do things, making their own tools, and learning from mistakes. Right now, most companies either check their AI's work by hand or don't check it much at all. Others are making their own tools for checking.

<figure>
  ![Retool Report: how developers measure prompt
  performance](/blog/img/measuring-perf-retool.jpg)
</figure>

You don't need to worry about making your own tools. With the Braintrust platform, you can add evals to your AI app in < 10 minutes. Braintrust helps you run evaluations, visualize and inspect your results, and experiment with prompts quickly. You don't have to spend time and effort building your own internal tools.

<figure>
  ![Braintrust: the enterprise-grade stack for building AI products: collection
  of tools (AI evaluation, CICD, datasets, prompt
  playground)](/blog/img/braintrust-platform.png)
</figure>

<SignUpLink>Sign up</SignUpLink> now, or check out our [docs](/docs) for more details.

<div className="mt-12">
  <CtaButton className="w-full sm:w-auto sm:h-12" cta="Get started for free" />
</div>
