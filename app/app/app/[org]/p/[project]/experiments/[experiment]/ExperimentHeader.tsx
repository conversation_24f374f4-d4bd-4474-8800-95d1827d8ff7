"use client";

import {
  type PropsWith<PERSON><PERSON>dren,
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
} from "react";

import { Button } from "#/ui/button";
import { type SavingState, SavingStatus } from "#/ui/saving";
import { useUser } from "#/utils/user";
import { Globe2, LockIcon } from "lucide-react";
import { cn } from "#/utils/classnames";
import { useEntityContextActions } from "../../../useEntityContextActions";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { getProjectLink } from "../../getProjectLink";
import { ProjectContext } from "../../projectContext";
import { getExperimentLink } from "./getExperimentLink";
import { type ExtendedExperiment } from "./experiment-actions";
import {
  useDiffModeState,
  useExperimentComparisonIds,
} from "#/ui/query-parameters";
import { ReviewButton } from "../../review-button";
import {
  experimentComboboxOptionRenderer,
  experimentComboboxOptionTooltipRenderer,
} from "./experiment-sidebar";
import { Combobox } from "#/ui/combobox/combobox";
import { useRouter } from "next/navigation";
import { Skeleton } from "#/ui/skeleton";
import { useRecentExperiments } from "../clientpage";
import { type ErrorSummary } from "./(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { type Search } from "#/utils/search/search";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";

export type ExperimentItem = {
  id: string;
  name: string;
  created?: string | null;
};

export function ExperimentHeader({
  experiment,
  orgName,
  projectName,
  savingState,
  setShareModalOpen,
  selectedComparisonExperiments,
  getRowsForExport,
  getRawData,
  children,
  isReadOnly,
  isLoading,
  errorSummaryData,
  refetchDataQueryFn,
  search,
  columnVisibility,
}: PropsWithChildren<{
  experiment: ExtendedExperiment;
  orgName: string;
  projectName: string;
  savingState: SavingState;
  setShareModalOpen: (open: boolean) => void;
  selectedComparisonExperiments: ExperimentItem[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  getRowsForExport: () => Promise<any[] | undefined>;
  getRawData?: () => Promise<unknown[] | undefined>;
  isReadOnly?: boolean;
  isLoading: boolean;
  errorSummaryData?: Record<string, ErrorSummary>;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  search: Search;
  columnVisibility?: Record<string, boolean>;
}>) {
  const { status: sessionStatus } = useUser();
  const isLoopEnabled = useIsLoopEnabled();

  const [_compareToParam, setCompareToParam] = useExperimentComparisonIds();

  const { latestExperimentId, baselineExperimentId } = useRecentExperiments();

  useEffect(() => {
    setCompareToParam(selectedComparisonExperiments.map((e) => e.name));
  }, [selectedComparisonExperiments, setCompareToParam]);

  const showShareButton = sessionStatus === "authenticated" && !isReadOnly;

  const { mutateExperiments, experiments } = useContext(ProjectContext);

  const { actions: experimentActions, modals: experimentActionModals } =
    useEntityContextActions({
      entityType: "experiment",
      onUpdate: mutateExperiments,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (experimentName) =>
          getExperimentLink({
            orgName,
            projectName,
            experimentName,
          }),
        getDeletedEntityLink: () => getProjectLink({ orgName, projectName }),
      },
    });

  const [diffMode] = useDiffModeState();
  const router = useRouter();

  const comparableExperiments = useMemo(
    () => experiment?.comparables ?? [],
    [experiment?.comparables],
  );

  const setBaseExperiment = useCallback(
    (id?: string) => {
      const nextExperiment = comparableExperiments.find((e) => e.id === id);
      if (nextExperiment) {
        const params = new URLSearchParams({});

        const c = selectedComparisonExperiments
          .flatMap((e) => (id === e.id ? [] : [e.name]))
          .join(",");
        params.set("c", c);
        if (c.length > 0 && diffMode?.enabled) {
          params.set("diff", diffMode.enabledValue);
        }

        router.push(
          getExperimentLink({
            orgName,
            projectName,
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            experimentName: nextExperiment.name as string,
          }) +
            "?" +
            params.toString(),
        );
      }
    },
    [
      comparableExperiments,
      orgName,
      projectName,
      router,
      diffMode,
      selectedComparisonExperiments,
    ],
  );

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 pb-2 pt-1">
        <Skeleton className="size-7" />
        <div className="flex-1">
          <Skeleton className="h-7 w-20" />
        </div>
      </div>
    );
  }

  if (!experiment) return null;

  return (
    <div
      className={cn(
        "group px-3 pb-1.5 bg-primary-50 flex-none flex md:flex-row md:items-center gap-2 flex-col items-start md:flex-wrap",
      )}
    >
      <div className="flex w-full items-center justify-between gap-2">
        <Combobox
          options={[experiment, ...comparableExperiments].map(
            ({ id: value, name: label, created }) => ({
              value,
              label,
              created,
              isLatest: value === latestExperimentId,
              isBaseline: value === baselineExperimentId,
            }),
          )}
          onChange={(v) => {
            setBaseExperiment(v);
          }}
          selectedValue={experiment?.id}
          placeholderLabel="select an experiment"
          searchPlaceholder="Find an experiment"
          noResultsLabel="No experiments found"
          variant="button"
          buttonSize="sm"
          buttonVariant="ghost"
          align="start"
          iconClassName={cn({ hidden: isReadOnly })}
          buttonClassName={cn(
            "px-2 gap-2 h-auto min-h-7 justify-start text-left py-1 border",
            {
              "pointer-events-none border-none": isReadOnly,
            },
          )}
          placeholderClassName="font-medium text-base flex-1"
          contentWidth={500}
          renderOptionLabel={experimentComboboxOptionRenderer}
          renderComboboxDisplayLabel={(option) => (
            <span className="flex items-baseline gap-2">
              <span className="block size-2 flex-none -translate-y-px rounded-full bg-primary-600" />
              <span className="flex-1 text-primary-950">{option.label}</span>
            </span>
          )}
          renderOptionTooltip={(option) => {
            const e = experiments?.find((e) => e.id === option.value);
            return experimentComboboxOptionTooltipRenderer({
              name: e?.name,
              summary: errorSummaryData?.[option.value],
              metadata: e?.metadata,
              description: e?.description,
              git: e?.repo_info ?? null,
            });
          }}
        />
        {!isReadOnly && (
          <EntityContextMenu
            objectName={experiment.name}
            objectId={experiment.id}
            objectType="experiment"
            orgName={orgName}
            projectName={projectName}
            buttonClassName="w-7 h-7 p-0"
            handleEdit={() =>
              experimentActions.editEntityName({
                entityId: experiment.id,
                entityName: experiment.name,
                trackAnalytics: {
                  source: "experiment_page_overflow_control",
                },
              })
            }
            handleDelete={() =>
              experimentActions.deleteEntity({
                entityId: experiment.id,
                entityName: experiment.name,
                trackAnalytics: {
                  source: "experiment_page_overflow_control",
                },
              })
            }
            handleCopyId={() =>
              experimentActions.copyEntityId({
                entityId: experiment.id,
              })
            }
            handleCopyName={() =>
              experimentActions.copyEntityName({
                entityName: experiment.name,
              })
            }
            getRowsForExport={getRowsForExport}
            getRawData={getRawData}
            refetchDataQueryFn={refetchDataQueryFn}
            exportName={experiment.name}
            isFiltering={search.filter && search.filter.length > 0}
            columnVisibility={columnVisibility}
          />
        )}
        {experimentActionModals}
        <div className="flex flex-1 items-center justify-end gap-2">
          <SavingStatus
            state={savingState}
            className="mr-2 text-xs text-primary-400"
          />
          {!isReadOnly ? children : null}
          {!isReadOnly && <ReviewButton />}
          {showShareButton && (
            <Button
              size="xs"
              variant="ghost"
              onClick={() => setShareModalOpen(true)}
              Icon={experiment.public ? Globe2 : LockIcon}
            >
              {experiment.public ? "Public" : "Private"}
            </Button>
          )}
          {isLoopEnabled && (
            <OptimizationChat
              hasMultipleSelectedExperiments={
                selectedComparisonExperiments.length > 0
              }
            />
          )}
        </div>
      </div>
    </div>
  );
}
