import { useHighlightState } from "#/ui/charts/highlight";
import { Skeleton } from "#/ui/skeleton";
import { useState, useMemo, useCallback, useEffect } from "react";
import { useOrg } from "#/utils/user";
import {
  getDataSeries,
  getGroupedDataSeries,
  getGroupedSeriesNames,
  getGroupNames,
  groupData,
} from "../groups";
import { getTracesLink } from "../getMonitorLink";
import { useRouter } from "next/navigation";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { parseAsString, useQueryState } from "nuqs";
import { TimeseriesChart } from "#/ui/charts/timeseries/timeseries-chart";
import { metricsDataToTimeseriesData } from "#/ui/charts/timeseries-data/metrics-data-to-timeseries-data";
import {
  formatValueForSelectionType,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { sortedTimeseriesData } from "#/ui/charts/timeseries-data/sorted-timeseries-data";
import {
  type TIME_BUCKETS,
  timeBucketToDuration,
} from "#/ui/charts/scale-time";
import { type MonitorCardConfig } from "./monitor-card-config.types";
import { type CardState } from "../monitor-cards";
import { useCardConfigQuery } from "./use-card-config-query";
import { MonitorCardHeader } from "./monitor-card-header";
import { useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { cn } from "#/utils/classnames";

interface SeriesMetadataExtension {
  name: string;
  selectionType: SelectionType;
}

interface MonitorCardProps {
  /** The serializable monitor card config */
  cardConfig: MonitorCardConfig;

  /** Callback so that the card can communicate some state upwards */
  setCardState: (state: CardState) => void;

  /** Timeframe to query over */
  chartTimeFrame: ChartTimeFrame;

  /** Time resolution duration*/
  timeBucket: TIME_BUCKETS;

  /** Where we're querying from */
  from: "experiment" | "project_logs";
  projectIds: string[];
  experimentIds: string[];

  /** Optional group by to append to query */
  groupBy?: string;

  /** Callback when brush interaction ends */
  onBrush: (v: [number, number] | null) => void;

  /** If we should use UTC alignment instead of local */
  tzUTC?: boolean;
}

function MonitorCard({
  timeBucket,
  groupBy,
  chartTimeFrame,
  from,
  onBrush,
  cardConfig,
  setCardState,
  projectIds,
  experimentIds,
  tzUTC,
}: MonitorCardProps) {
  const org = useOrg();
  const orgName = org.name;

  const router = useRouter();
  const [projectId] = useQueryState("projectId", parseAsString);
  const highlightState = useHighlightState();

  const [selectedMeasures, setSelectedMeasures] = useState<string[] | null>();

  const toolMetric = cardConfig.toolMetric;
  const vizType = cardConfig.vizType;

  const { data, loading, error, lastTimeBucket } = useCardConfigQuery({
    cardConfig,
    projectIds,
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
    tzUTC,
  });

  const { allMeasureNames, metricToDisplayName } = useMemo(() => {
    if (!data) {
      return { allMeasureNames: [] };
    }

    if (cardConfig.pivot) {
      const pivotKey = cardConfig.idName;
      const uniqueKeys = new Set<string>();
      for (const item of data) {
        if (item[pivotKey]) {
          Object.keys(item[pivotKey]).forEach((key) => uniqueKeys.add(key));
        }
      }
      return { allMeasureNames: Array.from(uniqueKeys) };
    }

    const availableMeasures = cardConfig.measures.filter(
      (n) => n.alias !== "count",
    );
    const map = new Map<string, string>();
    availableMeasures.forEach((m) => {
      map.set(m.alias, m.displayName ?? m.alias);
    });

    // in order of config
    const allMeasureNames = availableMeasures.map((m) => m.alias);
    return {
      allMeasureNames,
      metricToDisplayName: map,
    };
  }, [cardConfig, data]);

  // set to all measures selected if not initialized
  useEffect(() => {
    if (!selectedMeasures && allMeasureNames.length > 0) {
      setSelectedMeasures(allMeasureNames);
    }
  }, [allMeasureNames, selectedMeasures]);

  const aggregateFormatter = useMemo(() => {
    const { unitType } = cardConfig;
    if (unitType === "duration") {
      return (value: number) =>
        formatValueForSelectionType(value, {
          value: "duration",
          type: "metric",
        });
    }

    if (unitType === "cost") {
      return (v: number) =>
        formatValueForSelectionType(v, { value: "cost", type: "metric" });
    }

    if (unitType === "percent") {
      return (value: number) =>
        value.toLocaleString(undefined, {
          style: "percent",
          maximumFractionDigits: 0,
        });
    }

    return (value: number) =>
      value.toLocaleString(undefined, {
        notation: "standard",
        maximumFractionDigits: 1,
      });
  }, [cardConfig]);

  const tickFormatter = useMemo(() => {
    const { unitType } = cardConfig;

    // for count we use a shorter version than the aggregator
    if (unitType === "count") {
      return (value: number) =>
        value.toLocaleString(undefined, {
          notation: "compact",
          compactDisplay: "short",
          maximumFractionDigits: 1,
        });
    }

    // otherwise use same as aggr
    return aggregateFormatter;
  }, [cardConfig, aggregateFormatter]);

  useEffect(() => {
    setCardState({
      isLoading: loading,
      hasError: Boolean(error),
      hasData: Boolean(data && data.length > 0),
      error,
    });
  }, [loading, data, error, setCardState]);

  // try to base on what the data is tied to
  const timeBucketDuration = useMemo(() => {
    return timeBucketToDuration(lastTimeBucket);
  }, [lastTimeBucket]);

  const { seriesData, allSeriesNames } = useMemo(() => {
    if (!data) {
      return {
        seriesData: [],
        allSeriesNames: [],
      };
    }

    const groupedData = groupBy ? groupData(data) : {};
    const allGroupNames = getGroupNames(groupedData);

    const seriesData = groupBy
      ? getGroupedDataSeries({
          groupedData,
          groupNames: allGroupNames,
          selectedSeries: selectedMeasures ?? allMeasureNames,
          toolMetric,
        })
      : getDataSeries({
          data,
          selectedSeries: selectedMeasures ?? allMeasureNames,
          toolMetric,
        });

    // remove time buckets where all values are 'empty'
    // both lines and bars require finite not null
    // bars also require positive
    const isEmpty =
      cardConfig.vizType === "bars"
        ? (v: number) => Number.isFinite(v) && v > 0
        : (v: number) => Number.isFinite(v);
    const filteredSeriesData = seriesData.filter((d) =>
      d.y.some((y) => y !== null && isEmpty(y.value)),
    );

    const allSeriesNames = groupBy
      ? getGroupedSeriesNames({
          groupNames: allGroupNames,
          selectedSeries: selectedMeasures ?? allGroupNames,
          metricToDisplayName,
        })
      : (selectedMeasures ?? allGroupNames).map(
          (n) => metricToDisplayName?.get(n) ?? n,
        );

    return { seriesData: filteredSeriesData, allSeriesNames };
  }, [
    groupBy,
    data,
    selectedMeasures,
    toolMetric,
    allMeasureNames,
    metricToDisplayName,
    cardConfig.vizType,
  ]);

  const timeseriesData = useMemo(() => {
    const seriesMeta: SeriesMetadataExtension[] = allSeriesNames.map((v) => ({
      name: v,
      selectionType: {
        value: v,
        type: "score",
      },
    }));

    const unsortedTimeseries =
      metricsDataToTimeseriesData<SeriesMetadataExtension>(
        seriesData,
        seriesMeta,
        timeBucketDuration,
      );

    return sortedTimeseriesData(unsortedTimeseries, vizType);
  }, [seriesData, allSeriesNames, vizType, timeBucketDuration]);

  const onChartClick = useCallback(
    async (event: React.MouseEvent, timestamp: number) => {
      if (!projectId) return;

      const url = await getTracesLink({
        orgName: orgName,
        projectId: projectId,
        from,
        timeBucket,
        time: new Date(timestamp).toISOString(),
      });

      if (event.metaKey || event.ctrlKey || event.button === 1) {
        window.open(url, "_blank");
      } else {
        router.push(url);
      }
    },
    [orgName, projectId, from, router, timeBucket],
  );

  const [fullscreenCard] = useFullscreenMonitorCardState();
  const isFullscreen = fullscreenCard === cardConfig.name;

  const onSeriesSelectionChange = useCallback(
    (seriesName: string) => {
      if (!selectedMeasures) {
        return;
      }
      if (selectedMeasures.includes(seriesName)) {
        setSelectedMeasures(selectedMeasures.filter((s) => s !== seriesName));
        return;
      }
      // Keep same order as allMeasureNames by filtering allMeasureNames
      setSelectedMeasures(
        allMeasureNames.filter((s) =>
          [...selectedMeasures, seriesName].includes(s),
        ),
      );
    },
    [selectedMeasures, allMeasureNames],
  );

  const showReset = useMemo(() => {
    const measureSelection =
      selectedMeasures && selectedMeasures.length !== allMeasureNames.length;
    const seriesSelection = highlightState.state.selected.length > 0;
    return measureSelection || seriesSelection;
  }, [selectedMeasures, allMeasureNames, highlightState]);

  const onReset = useCallback(() => {
    highlightState.clearSelected();
    setSelectedMeasures(allMeasureNames);
  }, [highlightState, allMeasureNames]);

  if (!data) {
    return <Skeleton className="min-h-48 flex-1" />;
  }

  return (
    <div
      className={cn(
        "group flex flex-1 flex-col rounded-lg p-2 bg-primary-50 border-primary-100",
        {
          "p-0 bg-transparent": isFullscreen,
        },
      )}
    >
      <div className="mb-2 flex items-center gap-2">
        <MonitorCardHeader
          title={cardConfig.title}
          cardName={cardConfig.name}
          iconName={cardConfig.iconName}
          showReset={showReset}
          onReset={onReset}
          showSeriesSelect={Boolean(data && data.length > 0)}
          selectedSeries={selectedMeasures ?? allMeasureNames}
          availableSeries={allMeasureNames}
          onSelectionChange={onSeriesSelectionChange}
          metricToDisplayName={metricToDisplayName}
        />
      </div>
      <TimeseriesChart
        timeseriesData={timeseriesData}
        chartTimeFrame={chartTimeFrame}
        highlightState={highlightState}
        aggregateFormatter={aggregateFormatter}
        tickFormatter={tickFormatter}
        onClick={projectId ? onChartClick : undefined}
        onBrush={onBrush}
        isFetchingData={loading}
        vizType={vizType}
        tzUTC={tzUTC}
      />
    </div>
  );
}

export { MonitorCard };
