import React, {
  useMemo,
  useCallback,
  forwardRef,
  type HTMLAttributes,
} from "react";
import { type ProjectSummary } from "../org-actions";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import { NestedDropdown } from "#/ui/nested-dropdown";

const ROW_TYPE_OPTIONS = [
  { value: "org_logs", label: "Organization logs" },
  { value: "logs", label: "Project logs" },
  { value: "experiment", label: "Project experiments" },
] as const;

export const ROW_TYPE_OPTION_VALUES = ROW_TYPE_OPTIONS.map((r) => r.value);
export type RowType = (typeof ROW_TYPE_OPTIONS)[number]["value"];

type Item = {
  id: string;
  label: string;
  rowType: RowType;
  projectId: string | null;
};

export function RowTypeSelect({
  value,
  onChange,
  projects,
  projectId,
}: {
  value: RowType;
  onChange: (rowType: RowType, projectId: string | null) => void;
  projects: ProjectSummary[];
  projectId: string | null;
}) {
  const selectedProject = projectId
    ? projects.find((p) => p.project_id === projectId)
    : null;

  const dropdownData = useMemo(() => {
    const projectLogItems: Item[] = projects.map((p) => ({
      id: `logs_${p.project_id}`,
      label: p.project_name,
      rowType: "logs",
      projectId: p.project_id,
    }));

    const projectExperimentItems: Item[] = projects.map((p) => ({
      id: `exp_${p.project_id}`,
      label: p.project_name,
      rowType: "experiment",
      projectId: p.project_id,
    }));

    return [
      {
        groupLabel: "Project logs",
        items: projectLogItems,
      },
      {
        groupLabel: "Project experiments",
        items: projectExperimentItems,
      },
    ];
  }, [projects]);

  const ItemComponent = useMemo(() => {
    const Component = forwardRef<
      HTMLDivElement,
      { item: Item } & HTMLAttributes<HTMLDivElement>
    >(({ item, ...props }, ref) => {
      return (
        <DropdownMenuItem
          {...props}
          ref={ref}
          onSelect={() => {
            onChange(item.rowType, item.projectId);
          }}
        >
          {item.label}
        </DropdownMenuItem>
      );
    });
    Component.displayName = "RowTypeSelectItem";
    return Component;
  }, [onChange]);

  const filterItemsCallback = useCallback(
    (search: string, itemsToFilter: Item[]): Item[] => {
      if (!search.trim()) {
        return itemsToFilter;
      }
      const lowerSearch = search.toLowerCase();
      return itemsToFilter.filter((item) =>
        item.label.toLowerCase().includes(lowerSearch),
      );
    },
    [],
  );

  return (
    <NestedDropdown<Item>
      objectType="scope"
      subGroups={dropdownData.filter((sg) => sg.items.length > 0)}
      DropdownItemComponent={ItemComponent}
      filterItems={filterItemsCallback}
      align="start"
    >
      <Button size="xs" isDropdown>
        {value === "experiment" && selectedProject
          ? `Experiments from ${selectedProject.project_name}`
          : value === "logs" && selectedProject
            ? `Logs from ${selectedProject.project_name}`
            : "Select a project"}
      </Button>
    </NestedDropdown>
  );
}
