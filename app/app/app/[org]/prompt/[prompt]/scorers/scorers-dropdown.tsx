"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Evaluators as AutoEvals,
  DEFAULT_MODEL as DEFAULT_AUTOEVALS_MODEL,
} from "autoevals";
import { Blend, Percent, Plus, X } from "lucide-react";
import {
  forwardRef,
  type HTMLAttributes,
  type PropsWithChildren,
  useCallback,
  useMemo,
} from "react";
import {
  DropdownMenuCheckboxItem,
  DropdownMenuGroup,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { type TransactionId } from "@braintrust/core";
import { type SavedScorerObjects, useScorerFunctions } from "./open";
import {
  createOrUpdatePrompt,
  newPrompt,
  type UIFunction,
} from "#/ui/prompts/schema";
import { slugify } from "#/utils/slug";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type JSONStructure } from "#/ui/prompts/hooks";
import { useFeatureFlags } from "#/lib/feature-flags";
import { isEmpty } from "#/utils/object";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import React from "react";
import type { SavedScorer } from "#/utils/scorers";
import { type AdditionalAction, NestedDropdown } from "#/ui/nested-dropdown";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "../../../org-actions";
import { useOrg } from "#/utils/user";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { FunctionDialog } from "#/ui/prompts/function-editor/function-dialog";
import { toast } from "sonner";
import { newId } from "braintrust";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";

type AutoevalMethod = (typeof AutoEvals)[0]["methods"][0];

export const AutoEvalMap = Object.fromEntries(
  AutoEvals.flatMap((section) => section.methods).map((method) => [
    method.method.name,
    method,
  ]),
);

export const AUTOEVAL_OPTIONS = AutoEvals.map((section) => ({
  label: section.label,
  options: section.methods.map((method) => ({
    value: method.method.name,
    description: method.description,
  })),
}));

export const AUTOEVALS = AutoEvals.flatMap((section) =>
  section.methods.map((method) => ({ ...method, methodType: section.label })),
);

interface ScorersDropdownBaseProps {
  projectId: string;
  projectName: string;
  // This is a little bit confusing, but essentially:
  // savedScorers/updateScorers are the list of references (global function name or function id) to
  // the scorers, and savedScorerObjects are the objects themselves (loaded from the database).
  savedScorers: SavedScorer[];
  updateScorers: (scorers: SavedScorer[]) => Promise<TransactionId | null>;
  setIsCreatingScorer?: (isCreating: boolean) => void;
  setSelectedScorerId?: (id: string | null) => void;
  dropdownMenuContentProps?: DropdownMenuContentProps;
  disableScorersThatRequireConfiguration?: boolean;
}

interface ScorersDropdownProps extends ScorersDropdownBaseProps {
  hideSelected?: boolean;
  additionalActions?: AdditionalAction[];
  savedScorerObjects: SavedScorerObjects;
}
interface ScorersDropdownWithCreateDialogProps
  extends ScorersDropdownBaseProps {
  jsonStructure?: JSONStructure;
  getFirstEvaluationRow?: () => Record<string, unknown>;
  copilotContext?: PlaygroundCopilotContext;
  isCreatingScorer: boolean;
  setIsCreatingScorer: (isCreating: boolean) => void;
  selectedScorerId?: string | null;
  setSelectedScorerId?: (id: string | null) => void;
  hideSelected?: boolean;
  scorerFunctionOverride?: Record<string, UIFunction>;
  createPromptOverride?: (prompt: UIFunction) => Promise<string | null>;
  isLoopEnabled?: boolean;
}

type ScorerDropdownItemType =
  | (AutoevalMethod & { methodType?: string })
  | (UIFunction & { projectName?: string });

const isCustomScorer = (item: ScorerDropdownItemType) => "id" in item;

const DEFAULT_JSON_STRUCTURE: JSONStructure = {};
const OUTPUT_NAMES = ["output"];

export function ScorersDropdownWithCreateDialog({
  projectId,
  projectName,
  savedScorers,
  updateScorers,
  jsonStructure = DEFAULT_JSON_STRUCTURE,
  isLoopEnabled = false,
  getFirstEvaluationRow,
  copilotContext,
  children,
  isCreatingScorer,
  setIsCreatingScorer,
  selectedScorerId,
  setSelectedScorerId,
  dropdownMenuContentProps,
  disableScorersThatRequireConfiguration,
  hideSelected = true,
  scorerFunctionOverride,
  createPromptOverride,
}: PropsWithChildren<ScorersDropdownWithCreateDialogProps>) {
  const {
    flags: { customFunctions },
  } = useFeatureFlags();
  const org = useOrg();
  const savedScorerObjects = useScorerFunctions({});

  const { functions: savedFunctions } = savedScorerObjects;
  const functions = scorerFunctionOverride ?? savedFunctions;
  const createPrompt = createPromptOverride
    ? async (args: Parameters<typeof createOrUpdatePrompt>[0]) => {
        // For client-only mode, we only need the prompt object
        return await createPromptOverride(args.prompt);
      }
    : createOrUpdatePrompt;

  const { setIsChatOpen, handleSendMessage } = useGlobalChat();

  const selectedSavedScorer = useMemo(() => {
    return savedScorers.find(
      (s) =>
        (s.type === "global" && s.name === selectedScorerId) ||
        (s.type === "function" && s.id === selectedScorerId),
    );
  }, [savedScorers, selectedScorerId]);

  const isDialogOpen = !!selectedSavedScorer || isCreatingScorer;
  const selectedScorerObject = useMemo(() => {
    if (isCreatingScorer || !selectedSavedScorer) return null;
    if (selectedSavedScorer.type === "global") {
      const name = selectedSavedScorer.name;
      const autoEval = AutoEvalMap[name];
      if (!autoEval) {
        console.warn("Unknown global function", name);
        return null;
      }

      let scorer: UIFunction = getGlobalScorerPrompt({
        name,
        autoEval,
        projectId,
      });
      scorer = {
        ...scorer,
        function_data: scorer.prompt_data
          ? { type: "prompt" }
          : scorer.function_data,
      };
      return scorer;
    } else {
      return functions[selectedSavedScorer.id];
    }
  }, [isCreatingScorer, selectedSavedScorer, projectId, functions]);

  const onSelectCreateCustomScorer = () => {
    // Selecting the create custom scorer button returns focus to the dropdown trigger,
    // which can cause the dialog to auto-close in response to an outside interaction.
    // This delays opening the dialog until the focus has settled.
    document.addEventListener("focusin", () => setIsCreatingScorer(true), {
      once: true, // Only listen for one focus event, then remove the listener
    });
  };

  const additionalActions = [
    ...(isLoopEnabled
      ? [
          {
            label: (
              <div>
                <Blend className="size-3" />
                Choose scorers for me
              </div>
            ),
            onSelect: () => {
              setIsChatOpen(true);
              handleSendMessage({
                id: crypto.randomUUID(),
                type: "user_message",
                message:
                  "Modify scorer selection to be appropriate for the task in my playground.",
              });
            },
          },
        ]
      : []),
    {
      label: (
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <DropdownMenuItem
                onSelect={onSelectCreateCustomScorer}
                disabled={!customFunctions}
              >
                <Plus className="size-3" />
                Create custom scorer
              </DropdownMenuItem>
            </div>
          </TooltipTrigger>
          {!customFunctions && (
            <TooltipContent
              side="right"
              align="start"
              className="max-w-sm text-xs"
            >
              Update your stack to enable custom scorers
            </TooltipContent>
          )}
        </Tooltip>
      ),
      onSelect: onSelectCreateCustomScorer,
    },
  ];

  const onCreateOrUpdate = (scorer: UIFunction) => {
    let newScorers: SavedScorer[];
    // We're creating a new scorer so add to the end of the list
    if (isCreatingScorer) {
      newScorers = [
        ...savedScorers,
        {
          type: "function",
          id: scorer.id,
        },
      ];
      // A copy of the global scorer is created with the user's edits, so we need to save as a function
    } else if (selectedSavedScorer?.type === "global") {
      newScorers = savedScorers.map((savedScorer) => {
        if (getScorerId(savedScorer) === selectedScorerId) {
          return {
            type: "function",
            id: scorer.id,
          };
        } else {
          return savedScorer;
        }
      });
      // We're updating an existing scorer, so the list of scorer ids doesn't need to change
    } else {
      newScorers = savedScorers;
    }
    updateScorers(newScorers);
    setSelectedScorerId?.(null);
    setIsCreatingScorer(false);
  };

  const shouldSaveNewScorer =
    isCreatingScorer || selectedSavedScorer?.type === "global";

  const validatePreSave = () => {
    const shouldValidate = !createPromptOverride;
    if (!shouldValidate) {
      return true;
    }

    if (!org.id) {
      toast.error("Unauthorized");
      return false;
    }
    if (!projectId) {
      toast.error("No project selected");
      return false;
    }

    return true;
  };

  return (
    <>
      <ScorersDropdownInner
        projectId={projectId}
        projectName={projectName}
        savedScorers={savedScorers}
        updateScorers={updateScorers}
        setIsCreatingScorer={setIsCreatingScorer}
        setSelectedScorerId={setSelectedScorerId}
        dropdownMenuContentProps={dropdownMenuContentProps}
        additionalActions={additionalActions}
        savedScorerObjects={savedScorerObjects}
        hideSelected={hideSelected}
        disableScorersThatRequireConfiguration={
          disableScorersThatRequireConfiguration
        }
      >
        {children}
      </ScorersDropdownInner>
      <FunctionDialog
        identifier={`scorer-${selectedScorerObject?.id ?? "new"}`}
        type="scorer"
        context="playground"
        objectType="project_functions"
        outputNames={OUTPUT_NAMES}
        projectName={projectName}
        projectId={projectId}
        orgName={org.name}
        opened={isDialogOpen}
        setOpened={(newOpen) => {
          if (isDialogOpen && !newOpen) {
            setIsCreatingScorer?.(false);
            setSelectedScorerId?.(null);
          }
        }}
        jsonStructure={jsonStructure}
        variableData={getFirstEvaluationRow?.()}
        copilotContext={copilotContext}
        status={savedScorerObjects.status}
        initialFunction={selectedScorerObject}
        mode={{
          type: shouldSaveNewScorer ? "create" : "update",
          upsert: async (prompt, updateSlug) => {
            const isValid = validatePreSave();
            if (!isValid) {
              return null;
            }

            const xactId = await createPrompt({
              dml: savedScorerObjects.dml,
              orgId: org.id!,
              update: !shouldSaveNewScorer,
              prompt,
              updateSlug,
            });
            onCreateOrUpdate(prompt);

            return xactId;
          },
          onDuplicate: async (prompt) => {
            const isValid = validatePreSave();
            if (!isValid) {
              return null;
            }

            const newScorerId = newId();

            try {
              const name = `${prompt.name} (copy)`;
              const slug = slugify(name);
              await createPrompt({
                dml: savedScorerObjects.dml,
                orgId: org.id!,
                update: false,
                updateSlug: false,
                prompt: {
                  function_data: prompt.function_data,
                  description: prompt.description,
                  function_type: prompt.function_type,
                  id: newScorerId,
                  name,
                  slug,
                  _xact_id: "0",
                  project_id: projectId,
                  tags: prompt.tags,
                  prompt_data: prompt.prompt_data,
                },
              });

              // Wait for the new scorer to be created before opening it
              updateScorers([
                ...savedScorers,
                {
                  type: "function" as const,
                  id: newScorerId,
                },
              ]);
              setSelectedScorerId?.(newScorerId);

              toast.success(`Duplicated scorer as ${name}`);
            } catch (e) {
              toast.error(`${e}`);
            }
          },
        }}
      />
    </>
  );
}

export function ScorersDropdown(
  props: Omit<PropsWithChildren<ScorersDropdownProps>, "savedScorerObjects">,
) {
  const savedScorerObjects = useScorerFunctions({});
  return (
    <ScorersDropdownInner {...props} savedScorerObjects={savedScorerObjects} />
  );
}

const DEFAULT_ADDITIONAL_ACTIONS: AdditionalAction[] = [];

function ScorersDropdownInner({
  projectId,
  projectName,
  savedScorers,
  savedScorerObjects,
  updateScorers: updateScorersProp,
  children,
  hideSelected = false,
  setSelectedScorerId,
  setIsCreatingScorer,
  disableScorersThatRequireConfiguration,
  dropdownMenuContentProps,
  additionalActions = DEFAULT_ADDITIONAL_ACTIONS,
}: PropsWithChildren<ScorersDropdownProps>) {
  const org = useOrg();
  const { functions, status: functionsStatus } = savedScorerObjects;

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: org.name,
    },
  });

  const projectsById = useMemo(() => {
    if (!projects) return undefined;
    return Object.fromEntries(projects.map((p) => [p.project_id, p]));
  }, [projects]);

  const selectedScorers: {
    uiFunction: UIFunction;
    scorerItem: ScorerDropdownItemType;
  }[] = useMemo(
    () =>
      savedScorers.reduce(
        (
          acc: { uiFunction: UIFunction; scorerItem: ScorerDropdownItemType }[],
          _,
          idx,
        ) => {
          const scorer = savedScorers[idx];

          if (scorer.type === "global") {
            const autoEval = AutoEvalMap[scorer.name];
            if (!autoEval) {
              console.warn("Unknown global function", scorer.name);
              return acc;
            }

            acc.push({
              uiFunction: getGlobalScorerPrompt({
                name: scorer.name,
                autoEval,
                projectId,
              }),
              scorerItem: autoEval,
            });
          } else {
            const scorerFunction = functions[scorer.id];
            if (!scorerFunction) {
              return acc;
            }
            const projectName =
              projectsById?.[scorerFunction.project_id]?.project_name;
            acc.push({
              uiFunction: scorerFunction,
              scorerItem: { ...scorerFunction, projectName },
            });
          }
          return acc;
        },
        [],
      ),
    [savedScorers, projectId, functions, projectsById],
  );

  const updateScorers = useCallback(
    (scorers: SavedScorer[]) => {
      const validScorers = scorers.filter((scorer) => {
        if (scorer.type === "global") {
          return !!AutoEvalMap[scorer.name];
        } else {
          return !!functions[scorer.id];
        }
      });
      updateScorersProp(validScorers);
    },
    [functions, updateScorersProp],
  );

  const onSelectScorer = useCallback(
    (scorer: SavedScorer) => {
      updateScorers([...savedScorers, scorer]);
    },
    [savedScorers, updateScorers],
  );

  const {
    flags: { customFunctions },
  } = useFeatureFlags();

  const groupedScorers = useMemo(() => {
    const datasetsByProject = Object.values(functions).reduce<{
      [projectId: string]: {
        projectId: string;
        projectName?: string;
        scorers: UIFunction[];
      };
    }>((acc, func) => {
      const { project_id } = func;
      if (!acc[project_id]) {
        acc[project_id] = {
          projectId: project_id,
          projectName: projectsById?.[project_id]?.project_name,
          scorers: [],
        };
      }

      acc[project_id].scorers.push(func);
      return acc;
    }, {});

    return Object.values(datasetsByProject).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : (a.projectName ?? "").localeCompare(b.projectName ?? ""),
    );
  }, [functions, projectsById, projectName]);

  const dropdownData = useMemo(() => {
    if (isEmpty(groupedScorers)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      groupedScorers[0]?.projectName === projectName
        ? groupedScorers[0].scorers.sort((a, b) =>
            (a.name ?? "").localeCompare(b.name ?? ""),
          )
        : undefined;

    const otherProjects = groupedScorers
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ projectName, scorers }) =>
        scorers
          .map((s) => ({ ...s, projectName }))
          .sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      );

    const subGroups = [
      ...(otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : []),
      {
        groupLabel: "AutoEvals",
        items: AUTOEVALS.filter(
          ({ method }) =>
            disableScorersThatRequireConfiguration ||
            !(
              AutoEvalMap[method.name]?.requiresExtraParams &&
              !AutoEvalMap[method.name]?.template
            ) ||
            Object.values(functions).find(
              (s) =>
                s?.name === method.name && s?.function_data.type === "global",
            ),
        ),
      },
    ];
    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [
    groupedScorers,
    projectName,
    functions,
    disableScorersThatRequireConfiguration,
  ]);

  const ScorerDropdownItem = forwardRef<
    HTMLDivElement,
    { item: ScorerDropdownItemType } & HTMLAttributes<HTMLDivElement>
  >(({ item, ...rest }, ref) => {
    if (isCustomScorer(item)) {
      return selectedScorers.some((s) => s?.uiFunction.id === item.id) ? (
        <DropdownMenuItem
          key={item.id}
          onSelect={(e) => {
            e.preventDefault();
            updateScorers(
              savedScorers.filter((s) =>
                s.type === "function" ? s.id !== item.id : true,
              ),
            );
          }}
          className="justify-between"
        >
          {item.name}
          <X className="size-3 text-primary-500" />
        </DropdownMenuItem>
      ) : (
        <Tooltip key={item.id}>
          <TooltipTrigger asChild>
            <DropdownMenuItem
              {...rest}
              ref={ref}
              onSelect={(e) => {
                e.preventDefault();
                onSelectScorer({
                  type: "function",
                  id: item.id,
                });
              }}
              className="flex gap-2"
            >
              <div className="flex-1">{item.name}</div>
              {item.project_id !== projectId && item.projectName ? (
                <span className="max-w-24 flex-none truncate text-primary-500">
                  {item.projectName}
                </span>
              ) : null}
            </DropdownMenuItem>
          </TooltipTrigger>
          <TooltipContent
            side="right"
            align="start"
            className="max-w-sm text-xs"
          >
            <div className="text-sm font-medium">{item.name}</div>
            {item.description || null}
            {item.slug && (
              <div className="font-mono text-[10px] text-primary-600">
                {item.slug}
              </div>
            )}
          </TooltipContent>
        </Tooltip>
      );
    }

    const scorer = selectedScorers.find(
      (s) =>
        s?.uiFunction.name === item.method.name &&
        s?.uiFunction.function_data.type === "global",
    );

    if (scorer) {
      return (
        <DropdownMenuItem
          key={item.method.name}
          onSelect={(e) => {
            e.preventDefault();
            updateScorers(
              savedScorers.filter((s) =>
                s.type === "global" ? s.name !== item.method.name : true,
              ),
            );
          }}
          className="justify-between"
        >
          {item.method.name}
          <X className="size-3 text-primary-500" />
        </DropdownMenuItem>
      );
    }

    return (
      <Tooltip key={item.method.name}>
        <TooltipTrigger asChild>
          <DropdownMenuCheckboxItem
            {...rest}
            ref={ref}
            disabled={
              item.requiresExtraParams &&
              (disableScorersThatRequireConfiguration || !customFunctions)
            }
            onSelect={(e) => {
              if (item.requiresExtraParams && setSelectedScorerId) {
                // Selecting the create custom scorer button returns focus to the dropdown trigger,
                // which can cause the editor dialog to auto-close in response to an outside interaction.
                // This delays opening the dialog until the focus has settled.
                let focusInCount = 0;
                const onFocusIn = () => {
                  focusInCount++;
                  // Once for the menu item, once for the trigger
                  if (focusInCount === 2) {
                    document.removeEventListener("focusin", onFocusIn);
                    onSelectScorer({
                      type: "global",
                      name: item.method.name,
                    });
                    setSelectedScorerId(item.method.name);
                  }
                };
                document.addEventListener("focusin", onFocusIn);
              } else {
                onSelectScorer({
                  type: "global",
                  name: item.method.name,
                });
                e.preventDefault();
              }
            }}
          >
            {item.method.name}
          </DropdownMenuCheckboxItem>
        </TooltipTrigger>
        <TooltipContent
          side="right"
          align="start"
          className="flex max-w-sm flex-col gap-1 text-xs"
        >
          <div className="text-sm font-medium">{item.method.name}</div>
          {item.requiresExtraParams && !customFunctions
            ? "Update your stack to enable this scorer"
            : item.description}
          <div className="text-primary-600">
            This scorer comes from the AutoEvals collection
          </div>
        </TooltipContent>
      </Tooltip>
    );
  });
  ScorerDropdownItem.displayName = "ScorerDropdownItem";

  return (
    <>
      <NestedDropdown<ScorerDropdownItemType>
        hideSelected={hideSelected}
        objectType="scorer"
        align={dropdownMenuContentProps?.align}
        selectedItems={selectedScorers.map(({ scorerItem }) => scorerItem)}
        renderItems={(items, DropdownItemComponent, isSearching) => {
          const isSelected = (item: ScorerDropdownItemType) =>
            selectedScorers.some((s) => {
              if (isCustomScorer(item)) {
                return s?.uiFunction.id === item.id;
              } else {
                return (
                  s?.uiFunction.name === item.method.name &&
                  s?.uiFunction.function_data.type === "global"
                );
              }
            });
          const selectedItems = items.filter(isSelected);
          // If searching, show all items, otherwise show only items that are not selected
          const prunedItems = isSearching
            ? items
            : items.filter((item) => !selectedItems.includes(item));

          if (prunedItems.length === 0) {
            return (
              <DropdownMenuItem key="all-items-selected" disabled>
                All scorers are selected
              </DropdownMenuItem>
            );
          }

          if (isCustomScorer(items[0])) {
            return prunedItems.map((item, key) => (
              <DropdownItemComponent item={item} key={key} />
            ));
          }

          const grouped = prunedItems.reduce(
            (
              acc: Record<
                string,
                (AutoevalMethod & {
                  methodType?: string;
                })[]
              >,
              item,
            ) => {
              if (!isCustomScorer(item)) {
                if (!item.methodType) return acc;
                if (!acc[item.methodType]) {
                  acc[item.methodType] = [];
                }

                acc[item.methodType].push(item);
              }
              return acc;
            },
            {},
          );

          return Object.entries(grouped).map(([label, options], i) => (
            <DropdownMenuGroup key={i}>
              <div className="px-2 pb-1 pt-2 text-xs text-primary-600">
                {label}
              </div>
              {options.map((item, key) => (
                <DropdownItemComponent item={item} key={key} />
              ))}
            </DropdownMenuGroup>
          ));
        }}
        dropdownItems={dropdownData.items}
        subGroups={dropdownData.subGroups}
        DropdownItemComponent={ScorerDropdownItem}
        filterItems={(search, opts) =>
          opts.filter((opt) => {
            if (isCustomScorer(opt)) {
              return (
                opt.name
                  ?.toLocaleLowerCase()
                  .includes(search.toLocaleLowerCase()) ||
                opt.slug
                  ?.toLocaleLowerCase()
                  .includes(search.toLocaleLowerCase()) ||
                opt.projectName
                  ?.toLocaleLowerCase()
                  .includes(search.toLocaleLowerCase())
              );
            }
            return opt.method.name
              ?.toLocaleLowerCase()
              .includes(search.toLocaleLowerCase());
          })
        }
        additionalActions={[
          ...additionalActions,
          ...(selectedScorers.length > 0
            ? [
                {
                  label: (
                    <div className="flex items-center gap-2">
                      <X className="size-3 flex-none" />
                      Deselect all
                    </div>
                  ),
                  onSelect: () => updateScorers([]),
                },
              ]
            : []),
        ]}
      >
        {children ?? (
          <Button
            size="sm"
            className="h-auto min-h-10 w-full justify-start px-3 py-1"
            isLoading={functionsStatus === "loading"}
            isDropdown
          >
            <span className="flex flex-1 flex-col gap-2 overflow-hidden text-left">
              {savedScorers && savedScorers.length > 0 ? (
                <>
                  {savedScorers.map((e, idx) => (
                    <span
                      key={idx}
                      className="flex items-center gap-1.5 truncate"
                    >
                      <Percent className="size-3 flex-none text-lime-600 dark:text-lime-400" />
                      <span className="flex-1 truncate">
                        {e.type === "global"
                          ? e.name
                          : (functions[e.id]?.name ?? `Function ${e.id}`)}
                      </span>
                    </span>
                  ))}
                </>
              ) : (
                <span className="flex items-center gap-1 font-normal text-primary-500">
                  Select scorers
                </span>
              )}
            </span>
          </Button>
        )}
      </NestedDropdown>
    </>
  );
}

const getGlobalScorerPrompt = ({
  name,
  autoEval,
  projectId,
}: {
  name: string;
  autoEval: AutoevalMethod;
  projectId: string;
}): UIFunction => {
  return {
    ...newPrompt(projectId),
    name,
    slug: slugify(name),
    description: autoEval.description,
    metadata: undefined,
    function_data: {
      type: "global",
      name,
    },
    function_type: "scorer",
    prompt_data: autoEval.template
      ? {
          prompt: {
            type: "chat",
            messages: [
              {
                role: "user",
                content: autoEval.template.prompt,
              },
            ],
          },
          options: {
            model: DEFAULT_AUTOEVALS_MODEL,
            params: { temperature: 0 },
          },
          parser: {
            type: "llm_classifier",
            use_cot: true,
            choice_scores: autoEval.template.choice_scores,
          },
        }
      : undefined,
  };
};

const getScorerId = (scorer: SavedScorer) => {
  if (scorer.type === "global") {
    return scorer.name;
  } else {
    return scorer.id;
  }
};
