"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { useCallback, useMemo, useState } from "react";
import { Dialog } from "#/ui/dialog";
import {
  createServiceToken,
  useCheckDataPlaneSyncStatus,
  useDataPlaneManager,
  useReprovisionDataPlaneManager,
  useUpsertDataPlaneServiceToken,
} from "./util";
import {
  AlertTriangle,
  CheckCircle,
  Ellipsis,
  PencilLine,
  PlusIcon,
  Trash2Icon,
  Clipboard,
} from "lucide-react";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { smartTimeFormat } from "#/ui/date";
import {
  type deleteServiceTokenAsOrgOwner,
  type fetchServiceTokensAsOrgOwner,
} from "./actions";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import {
  type ApiKey,
  type User,
  type Organization,
} from "@braintrust/core/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { type removeMembers } from "#/pages/api/organization/member_actions";
import { type fetchOrgUsers } from "#/utils/org-users";
import { OrgUsersContext } from "#/utils/org-users-context";
import { useContext } from "react";

import OrgLevelPermissionsDialog from "#/ui/permissions/org-level-permissions-dialog";
import { CreateNewServiceTokenModal } from "./create-new-service-token-dialog";
import { CreateNewServiceAccountModal } from "./create-new-service-account-dialog";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { CreatedServiceTokenDialog } from "./created-service-token-dialog";
import { isBraintrustDataPlane, MultiTenantApiURL } from "#/utils/user-types";
import { InfoBanner } from "#/ui/info-banner";
import { TableSkeleton } from "#/ui/table/table-skeleton";

export default function ClientPage({
  org,
  isSysadmin,
  isOrgOwner,
}: {
  org: Organization;
  isSysadmin: boolean;
  isOrgOwner: boolean;
}) {
  const { refreshOrgUsers } = useContext(OrgUsersContext);
  const { data: accountsById, invalidate: refreshAccounts } = useQueryFunc<
    typeof fetchOrgUsers
  >({
    fName: "fetchOrgUsers",
    args: { orgName: org.name, userType: "service_account" },
  });
  const accounts = useMemo(
    // NOTE: hide "system" service accounts like the data plane manager from the account list.
    // it will be shown below in the DataPlaneManagerCard
    () =>
      accountsById
        ? Object.values(accountsById).filter(
            (a) => !a.email?.startsWith("bt::sp::custom::data_plane_manager"),
          )
        : undefined,
    [accountsById],
  );

  const { getToken } = useAuth();
  const [lastCreatedToken, setLastCreatedToken] = useState<string | null>(null);

  const [tokenToDelete, setTokenToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [serviceaccountToRemove, setServiceAccountToRemove] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [selectedAccount, setSelectedAccount] = useState<User | null>(null);

  const [serviceaccountToSetPermissions, setServiceAccountToSetPermissions] =
    useState<{
      id: string;
      name: string;
    } | null>(null);

  const deleteToken = async (id: string) => {
    try {
      await invokeServerAction<typeof deleteServiceTokenAsOrgOwner>({
        fName: "deleteServiceTokenAsOrgOwner",
        args: { service_token_id: id, org_id: org.id ?? "" },
        getToken,
      });
      refreshTokens();
    } catch (error) {
      toast.error("Failed to delete service token", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  const createToken = async (
    name: string,
    orgId: string,
    accountId: string,
  ) => {
    const token = await createServiceToken({
      name,
      orgId,
      accountId: accountId,
    });
    setLastCreatedToken(token);
    refreshTokens();
  };

  const refreshAccountsWithOrgUsers = useCallback(() => {
    refreshAccounts();
    refreshOrgUsers();
  }, [refreshAccounts, refreshOrgUsers]);

  const removeServiceAccount = async (id: string) => {
    try {
      await invokeServerAction<typeof removeMembers>({
        fName: "removeMembers",
        args: { orgId: org.id ?? "", users: { ids: [id] } },
        getToken,
      });
      refreshAccountsWithOrgUsers();
    } catch (error) {
      toast.error("Failed to remove serviceaccount", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  const {
    data: all_service_tokens,
    invalidate: refreshTokens,
    isLoading: isLoadingTokens,
  } = useQueryFunc<typeof fetchServiceTokensAsOrgOwner>({
    fName: "fetchServiceTokensAsOrgOwner",
    args: { org_id: org.id ?? "" },
  });
  const tokens = useMemo(
    () =>
      // NOTE: we intentionally do not show unscoped (org_id === null) keys here since
      // they could be used in other organizations.
      // This is a legacy edge case that does not apply to keys created in 2024 and later
      // when organization scoped api keys became the default in this UI:
      // https://github.com/braintrustdata/braintrust/commit/ece930e810944b3ae7b91d7537dfc038b65a05e5
      all_service_tokens?.filter((k) => k.org_id === org.id) ?? [],
    [all_service_tokens, org.id],
  );

  if (!isOrgOwner) {
    return (
      <TableEmptyState label="You do not have permission to manage service tokens for this organization. Ask your administrator to grant the 'Manage settings' permission for this organization." />
    );
  }
  const api_url = org.api_url || MultiTenantApiURL;
  const btDataPlaneManager = isBraintrustDataPlane(api_url) && isSysadmin;
  const selfHostedDataPlaneManager =
    !isBraintrustDataPlane(api_url) && isOrgOwner;
  const showDataPlaneManager = btDataPlaneManager || selfHostedDataPlaneManager;
  const dataPlaneManager = showDataPlaneManager && (
    <DataPlaneManagerCard
      orgId={org.id}
      apiUrl={api_url}
      mode={btDataPlaneManager ? "bt-hosted" : "self-hosted"}
      getToken={getToken}
      setLastCreatedToken={setLastCreatedToken}
    />
  );

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Service tokens</h3>
        {!isLoadingTokens && (
          <CreateNewServiceAccountModal
            org={org}
            refreshAccounts={refreshAccountsWithOrgUsers}
            refreshTokens={refreshTokens}
            setLastCreatedToken={setLastCreatedToken}
            accounts={accounts}
            createToken={createToken}
          />
        )}
      </div>
      <div className="flex flex-col gap-2">
        {isLoadingTokens ? (
          <TableSkeleton />
        ) : (
          <>
            {accounts &&
              accounts.length > 0 &&
              accounts.map((account) => (
                <ServiceAccountCard
                  key={account.id}
                  account={account}
                  tokens={tokens.filter(
                    (token) => token.user_id === account.id,
                  )}
                  setSelectedAccount={setSelectedAccount}
                  setTokenToDelete={setTokenToDelete}
                  setServiceAccountToRemove={setServiceAccountToRemove}
                  setServiceAccountToSetPermissions={
                    setServiceAccountToSetPermissions
                  }
                />
              ))}
            {dataPlaneManager}
          </>
        )}
      </div>
      <CreateNewServiceTokenModal
        isOpen={selectedAccount !== null}
        selectedAccount={selectedAccount}
        setSelectedAccount={setSelectedAccount}
        onCreate={async (name, account) => {
          if (!org.id) {
            toast.error("Failed to create service token", {
              description: "No org ID",
            });
            return;
          }
          await createToken(name, org.id, account.id);
        }}
      />

      <ConfirmationDialog
        title="Confirm delete API key"
        description={`Are you sure you want to delete the '${tokenToDelete?.name}' service token? This action cannot be undone.`}
        open={Boolean(tokenToDelete)}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setTokenToDelete(null);
          }
        }}
        onConfirm={() => {
          if (tokenToDelete) {
            deleteToken(tokenToDelete.id);
          }
        }}
        confirmText="Confirm"
      />
      <ConfirmationDialog
        title="Confirm delete service account"
        description={`Are you sure you want to delete the service account '${serviceaccountToRemove?.name}'? This action cannot be undone.`}
        open={Boolean(serviceaccountToRemove)}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setServiceAccountToRemove(null);
          }
        }}
        onConfirm={() => {
          if (serviceaccountToRemove) {
            removeServiceAccount(serviceaccountToRemove.id);
          }
        }}
        confirmText="Confirm"
      />
      {serviceaccountToSetPermissions && (
        <Dialog
          open
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setServiceAccountToSetPermissions(null);
            }
          }}
        >
          <OrgLevelPermissionsDialog
            orgId={org.id ?? ""}
            userOrGroupLabel={`${serviceaccountToSetPermissions.name}`}
            userOrGroupId={serviceaccountToSetPermissions.id}
            userObjectType="user"
            onSubmit={() => {
              setServiceAccountToSetPermissions(null);
            }}
          />
        </Dialog>
      )}
      <CreatedServiceTokenDialog
        lastCreatedToken={lastCreatedToken}
        setLastCreatedToken={setLastCreatedToken}
      />
    </div>
  );
}

const ServiceAccountCard = ({
  account,
  tokens,
  setSelectedAccount,
  setTokenToDelete,
  setServiceAccountToRemove,
  setServiceAccountToSetPermissions,
}: {
  account: User;
  tokens: Array<ApiKey> | undefined;
  setSelectedAccount: (account: User | null) => void;
  setTokenToDelete: (token: { id: string; name: string }) => void;
  setServiceAccountToRemove: (serviceaccount: {
    id: string;
    name: string;
  }) => void;
  setServiceAccountToSetPermissions: (serviceaccount: {
    id: string;
    name: string;
  }) => void;
}) => {
  return (
    <div className="flex flex-col gap-2 rounded-md border p-4">
      <div className="flex flex-col">
        <div className="flex items-center justify-between">
          <div className="flex w-full items-start justify-between gap-2">
            <div className="flex flex-col">
              <div className="text-sm font-medium">{account.given_name}</div>
              <div className="text-xs text-primary-500">Service account</div>
            </div>
            <CopyToClipboardButton
              textToCopy={account.id}
              size={"xs"}
              variant={"ghost"}
              className="ml-auto mt-1 size-fit flex-none gap-2 px-0 font-mono text-xs transition-all text-primary-500 hover:bg-transparent hover:text-primary-900"
            >
              {account.id}
            </CopyToClipboardButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="size-6">
                  <Ellipsis className="size-4 text-primary-700" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setServiceAccountToSetPermissions({
                      id: account.id,
                      name: account.given_name || "unknown",
                    });
                  }}
                >
                  <PencilLine className="size-3" />
                  Edit Permissions
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedAccount(account);
                  }}
                >
                  <PlusIcon className="size-3" />
                  Add token
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setServiceAccountToRemove({
                      id: account.id,
                      name: account.given_name ?? "",
                    });
                  }}
                >
                  <Trash2Icon className="size-3" />
                  Delete service account
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-0 pt-1"></div>
      <Table className="table-auto text-left">
        <TableHeader>
          <TableRow className="text-sm">
            <TableHead className="w-60 truncate">Service token name</TableHead>
            <TableHead className="flex-1 truncate">Key</TableHead>
            <TableHead className="w-48">Created</TableHead>
            <TableHead className="w-6"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tokens && tokens.length > 0 ? (
            tokens?.map((token) => (
              <TableRow
                key={token.id}
                className="py-2 text-sm text-primary-700"
              >
                <TableCell className="w-60 truncate">{token.name}</TableCell>
                <TableCell className="flex-1 truncate font-mono">
                  {token.preview_name?.replace(/^(bt-st-)(.{4})$/, "$1****$2")}
                </TableCell>
                <TableCell className="w-48">
                  {smartTimeFormat(new Date(token.created ?? "").getTime())}
                </TableCell>
                <TableCell className="w-fit pr-0">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="size-6"
                    onClick={() => {
                      setTokenToDelete({ id: token.id, name: token.name });
                    }}
                  >
                    <Trash2Icon className="size-4 text-primary-500" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow className="py-4 text-center text-sm text-primary-500">
              <TableCell
                colSpan={4}
                className="flex-1 items-center justify-center py-0 text-center text-sm text-primary-500"
              >
                No service tokens found for this service account
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

const DataPlaneManagerCard = ({
  orgId,
  apiUrl,
  mode,
  getToken,
  setLastCreatedToken,
}: {
  orgId: string;
  apiUrl: string;
  mode: "bt-hosted" | "self-hosted";
  getToken: () => Promise<string | null>;
  setLastCreatedToken: (token: string | null) => void;
}) => {
  const [isReprovisionDialogOpen, setIsReprovisionDialogOpen] = useState(false);

  const { data: dataPlaneData, isLoading: isLoadingDataPlaneManager } =
    useDataPlaneManager(orgId);
  const {
    mutateAsync: reprovisionDataPlaneManager,
    isPending: isReprovisioning,
  } = useReprovisionDataPlaneManager();

  const dataPlaneManagerError = dataPlaneData?.data_plane_manager?.error;

  const {
    data: dataPlaneServiceTokenSynced,
    isLoading: isLoadingDataPlaneServiceTokenSynced,
  } = useCheckDataPlaneSyncStatus({
    apiUrl,
    dataPlaneServiceToken: dataPlaneData?.data_plane_service_token,
    getToken,
  });
  const { mutateAsync: upsertDataPlaneServiceToken } =
    useUpsertDataPlaneServiceToken();

  const provision = useCallback(async () => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error("No auth token found");
      }

      const { data_plane_service_token } = await reprovisionDataPlaneManager({
        orgId,
      });

      if (data_plane_service_token.key) {
        setLastCreatedToken(data_plane_service_token.key);
      }

      const resp = await upsertDataPlaneServiceToken({
        apiUrl,
        token,
        dataPlaneServiceToken: data_plane_service_token,
      });

      if (!resp) {
        throw new Error(`Failed to update data plane service token: ${resp}`);
      }
    } catch (error) {
      toast.error("Data plane manager error: ", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  }, [
    apiUrl,
    orgId,
    getToken,
    setLastCreatedToken,
    upsertDataPlaneServiceToken,
    reprovisionDataPlaneManager,
  ]);

  return (
    <div className="flex flex-col gap-2 rounded-md border p-4">
      {dataPlaneData && (
        <div className="flex flex-row items-start justify-between">
          <div className="flex flex-col">
            <span className="text-sm font-medium">
              {dataPlaneData?.data_plane_manager.name}
            </span>
            <span className="text-xs text-primary-500">Data plane manager</span>
          </div>
          <div className="flex gap-2">
            <CopyToClipboardButton
              textToCopy={dataPlaneData?.data_plane_manager.id ?? ""}
              size={"xs"}
              variant={"ghost"}
              className="ml-auto mt-1 size-fit flex-none gap-2 px-0 font-mono text-xs transition-all text-primary-500 hover:bg-transparent hover:text-primary-900"
            >
              {dataPlaneData?.data_plane_manager.id}
            </CopyToClipboardButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="size-6">
                  <Ellipsis className="size-4 text-primary-700" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    navigator.clipboard.writeText(
                      dataPlaneData?.data_plane_manager.email ?? "",
                    );
                    toast("Data plane manager invite ID copied to clipboard");
                  }}
                >
                  <Clipboard className="size-3" />
                  Copy data plane manager invite ID
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      )}
      <div className="flex flex-col gap-4">
        {isLoadingDataPlaneManager && <TableSkeleton />}
        {dataPlaneData && !isLoadingDataPlaneManager && (
          <div className="text-sm">
            <InfoBanner className="mb-4">
              {mode === "bt-hosted" ? (
                <>
                  This is a sysadmin service account with unscoped service
                  tokens for functionality like retention across all orgs on
                  this data plane.
                </>
              ) : (
                <>
                  This is a service account with read-only permissions on
                  projects in this org for functionality like retention.
                </>
              )}
            </InfoBanner>
            <Table className="table-auto text-left">
              <TableHeader>
                <TableRow className="text-sm">
                  <TableHead className="w-60 truncate">
                    Service token name
                  </TableHead>
                  <TableHead className="flex-1 truncate">Key</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow
                  key={dataPlaneData?.data_plane_service_token.id}
                  className="py-2 text-sm text-primary-700"
                >
                  <TableCell className="w-60 truncate">
                    {dataPlaneData?.data_plane_service_token.name}
                  </TableCell>
                  <TableCell className="flex-1 truncate font-mono">
                    <div className="flex items-center">
                      {dataPlaneData?.data_plane_service_token.preview_name ? (
                        <span>
                          bt-st-*****
                          {dataPlaneData?.data_plane_service_token.preview_name.slice(
                            -4,
                          )}
                        </span>
                      ) : (
                        <span>
                          Service token already provisioned. Reprovision to
                          copy.
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="w-fit pr-0">
                    <Button
                      size="sm"
                      variant="border"
                      isLoading={isReprovisioning}
                      disabled={isLoadingDataPlaneManager}
                      onClick={() => setIsReprovisionDialogOpen(true)}
                    >
                      Reprovision
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            {isLoadingDataPlaneServiceTokenSynced && <TableSkeleton />}
            {!isLoadingDataPlaneServiceTokenSynced && (
              <>
                {dataPlaneManagerError && (
                  <div className="mt-4 flex flex-row items-center gap-2">
                    <AlertTriangle className="size-3 text-bad-500" />
                    <span className="text-xs text-bad-500">
                      {dataPlaneManagerError}
                    </span>
                  </div>
                )}
                {!dataPlaneServiceTokenSynced && (
                  <div className="mt-4 flex flex-row items-center gap-2">
                    <AlertTriangle className="size-3 text-bad-500" />
                    <span className="text-xs text-bad-500">
                      bt_data_plane_service_token not found in data plane.
                      Reprovision to fix.
                    </span>
                  </div>
                )}
                {!dataPlaneManagerError && dataPlaneServiceTokenSynced && (
                  <div className="mt-4 flex flex-row items-center gap-2">
                    <CheckCircle className="size-3 text-good-500" />
                    <span className="text-xs text-primary-600">
                      bt_data_plane_service_token found in data plane. This does
                      not guarantee that the service token between control plane
                      and data plane are in sync.
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>
      <ConfirmationDialog
        title="Confirm reprovision"
        description="Are you sure you want to reprovision the data plane manager? This will delete the current service token and create a new one. This action cannot be undone."
        open={isReprovisionDialogOpen}
        onOpenChange={setIsReprovisionDialogOpen}
        onConfirm={() => {
          provision();
        }}
        confirmText="Confirm"
      />
    </div>
  );
};
