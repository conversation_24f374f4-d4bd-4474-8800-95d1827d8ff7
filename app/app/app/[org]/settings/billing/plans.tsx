import { useOrg } from "#/utils/user";
import { type OrgContextT } from "#/utils/user-types";
import type Orb from "orb-billing";

// TODO: Move this stuff to env variables
export const PLAN_SLUGS = {
  FREE: "free",
  PRO: "pro",
  ENTERPRISE: "enterprise",
} as const;

export const PLAN_LABELS = {
  [PLAN_SLUGS.FREE]: "Free",
  [PLAN_SLUGS.PRO]: "Pro",
  [PLAN_SLUGS.ENTERPRISE]: "Enterprise",
};

const PLAN_IDS_TEST_MODE = {
  [PLAN_SLUGS.FREE]: "XNZGjyARNVREdY78",
  [PLAN_SLUGS.PRO]: "F3cWrgBNNbQBnoL3",
  [PLAN_SLUGS.ENTERPRISE]: "ZfC99twiWMTNC9AA",
};

const PLAN_IDS_PRODUCTION = {
  [PLAN_SLUGS.FREE]: "gPoP9fDHzDm8CgUF",
  [PLAN_SLUGS.PRO]: "6nrSUKpBp9TieqpB",
  [PLAN_SLUGS.ENTERPRISE]: "RthSC3UXZDjy6qpQ",
};

const BILLABLE_METRIC_IDS_PRODUCTION = {
  SCORES_AND_CUSTOM_METRICS: "CkaY4iPn5vPYqqHu",
  SCORES_AND_CUSTOM_METRICS_V2: "cbEXZMjmQPJATzBw",
  MONTHLY_LOGS_INGESTED_GB: "ZUc6xwW25ktfraxn",
};

const BILLABLE_METRIC_IDS_TEST_MODE = {
  SCORES_AND_CUSTOM_METRICS: "K6WgKnb429Ym7btC",
  SCORES_AND_CUSTOM_METRICS_V2: "25TH4Xx7MKvnzmys",
  MONTHLY_LOGS_INGESTED_GB: "UrxvmueMTgfbiCr5",
};

export type PlanSlug = (typeof PLAN_SLUGS)[keyof typeof PLAN_SLUGS];

export function getTestModePlanId(slug: PlanSlug) {
  return PLAN_IDS_TEST_MODE[slug];
}

export function getProductionPlanId(slug: PlanSlug) {
  return PLAN_IDS_PRODUCTION[slug];
}

export function getPlanId(slug: PlanSlug) {
  return process.env.NODE_ENV === "development"
    ? getTestModePlanId(slug)
    : getProductionPlanId(slug);
}

export function isFreeSubscription(subscription: Orb.Subscription) {
  return subscription.plan.id === getPlanId(PLAN_SLUGS.FREE);
}

export function isProSubscription(subscription: Orb.Subscription) {
  return subscription.plan.id === getPlanId(PLAN_SLUGS.PRO);
}

export function isProPlan(planId: string) {
  return planId === getPlanId(PLAN_SLUGS.PRO);
}

export function isFreePlan(planId: string) {
  return planId === getPlanId(PLAN_SLUGS.FREE);
}

export function isEnterprisePlan(plan: Orb.Plan) {
  const planMetadata = plan.metadata;
  return (
    planMetadata?.isEnterprise === "true" ||
    plan.id === getPlanId(PLAN_SLUGS.ENTERPRISE)
  );
}

export function getPlanOptions() {
  return Object.entries(PLAN_SLUGS).map(([, slug]) => ({
    label: PLAN_LABELS[slug],
    value: getPlanId(slug),
  }));
}

export function getLabelForPlanId(planId: string) {
  const planIds =
    process.env.NODE_ENV === "development"
      ? PLAN_IDS_TEST_MODE
      : PLAN_IDS_PRODUCTION;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const slug = Object.entries(planIds).find(([, id]) => id === planId)?.[0] as
    | PlanSlug
    | undefined;
  return slug ? PLAN_LABELS[slug] : null;
}

export function isScoresAndCustomMetricsId(billableMetricId: string) {
  const scoresAndCustomMetricsId =
    process.env.NODE_ENV === "development"
      ? BILLABLE_METRIC_IDS_TEST_MODE
      : BILLABLE_METRIC_IDS_PRODUCTION;

  return (
    billableMetricId === scoresAndCustomMetricsId.SCORES_AND_CUSTOM_METRICS ||
    billableMetricId === scoresAndCustomMetricsId.SCORES_AND_CUSTOM_METRICS_V2
  );
}

export function isMonthlyLogsIngestedGBId(billableMetricId: string) {
  const monthlyLogsIngestedGbId =
    process.env.NODE_ENV === "development"
      ? BILLABLE_METRIC_IDS_TEST_MODE
      : BILLABLE_METRIC_IDS_PRODUCTION;

  return billableMetricId === monthlyLogsIngestedGbId.MONTHLY_LOGS_INGESTED_GB;
}

const isOrgLimited = ({ org }: { org: OrgContextT }) => {
  return [
    org.resources?.num_dataset_row_actions_calendar_months,
    org.resources?.num_dataset_row_actions,
    org.resources?.num_log_bytes_calendar_months,
    org.resources?.num_log_bytes,
    org.resources?.num_private_experiment_row_actions_calendar_months,
    org.resources?.num_private_experiment_row_actions,
    org.resources?.num_production_log_row_actions_calendar_months,
    org.resources?.num_production_log_row_actions,
  ].some(Boolean);
};

export function isUnlimitedOrg({ org }: { org: OrgContextT }) {
  return !isOrgLimited({ org });
}

export const isValidPlanSlug = (
  slug: string | null | undefined,
): slug is PlanSlug => {
  const slugs: string[] = Object.values(PLAN_SLUGS);
  return slug !== null && slug !== undefined && slugs.includes(slug);
};

export const useOrgPlan = () => {
  const org = useOrg();
  if (!org) {
    return null;
  }
  if (!org.plan_id) {
    return PLAN_SLUGS.ENTERPRISE;
  }
  // If the org is unlimited, do not treat this org as a free plan
  if (isFreePlan(org.plan_id) && !isUnlimitedOrg({ org })) {
    return PLAN_SLUGS.FREE;
  }
  if (isProPlan(org.plan_id)) {
    return PLAN_SLUGS.PRO;
  }
  return PLAN_SLUGS.ENTERPRISE;
};
