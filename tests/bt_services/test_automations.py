import errno
import http.server
import json
import random
import socket
import threading
import time
import unittest
from contextlib import closing
from datetime import datetime, timed<PERSON>ta
from typing import Callable
from urllib.parse import parse_qs, urlparse
from uuid import uuid4

import braintrust
from braintrust.logger import app_conn
from braintrust_local.api_db_util import log3_raw

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


class AutomationTestBase(BraintrustAppTestBase):
    def _create_automation(self, project_id, automation, api_key=None):
        request_kwargs = {} if not api_key else {"headers": {"Authorization": f"Bearer {api_key}"}}
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/project_automation/register",
            json=dict(
                project_automation_name=automation["name"],
                description=automation.get("description", ""),
                project_id=project_id,
                config=automation["config"],
            ),
            expect_error=False,
            allow_500_errors=False,
            skip_enable_audit=False,
            **request_kwargs,
        )

    def _set_automations(self, project_id, automations):
        existing_automations = self.run_request(
            "get", f"{LOCAL_API_URL}/v1/project_automation", params={"project_id": project_id}
        ).json()
        for c in existing_automations["objects"]:
            self.run_request("delete", f"{LOCAL_API_URL}/v1/project_automation/{c['id']}")

        ids = []
        for a in automations:
            resp = self._create_automation(project_id, a)
            ids.append(resp.json()["project_automation"]["id"])
        return ids

    def _update_automation(self, project_id, automation, api_key=None):
        request_kwargs = {} if not api_key else {"headers": {"Authorization": f"Bearer {api_key}"}}
        self.run_request(
            "patch",
            f"{LOCAL_API_URL}/v1/project_automation/{automation['id']}",
            json=dict(
                name=automation["name"],
                description=automation.get("description", ""),
                config=automation["config"],
            ),
            expect_error=False,
            allow_500_errors=False,
            skip_enable_audit=False,
            **request_kwargs,
        )

    def _flush(self):
        braintrust.flush()
        BraintrustAppTestBase.flush_proxy_promises()


class WebhookHandler(http.server.BaseHTTPRequestHandler):
    response_func: Callable[[], tuple[int, str]] | None = None

    webhook_requests = []
    webhook_responses = []
    delete_requests = []

    @classmethod
    def webhook_summary(cls):
        return [
            (
                a["automation"]["name"],
                a["details"]["count"],
                a["details"]["is_test"],
            )
            for a in cls.webhook_requests
        ]

    @classmethod
    def clear(cls):
        cls.webhook_requests = []
        cls.webhook_responses = []
        cls.response_func = None

    @classmethod
    def set_response(cls, response_func):
        cls.response_func = response_func

    def do_POST(self):
        content_length = int(self.headers["Content-Length"])
        post_data = self.rfile.read(content_length)
        WebhookHandler.webhook_requests.append(json.loads(post_data))
        if WebhookHandler.response_func:
            response = WebhookHandler.response_func()
        else:
            response = (200, "OK")
        self.send_response(*response)
        self.end_headers()
        WebhookHandler.webhook_responses.append(response)

    def do_DELETE(self):
        WebhookHandler.delete_requests.append({})

    # Silence the logging output
    def log_message(self, format, *args):
        pass


def get_open_port():
    for attempts in range(5):
        port = random.randint(3001, 4999)
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
            res = sock.connect_ex(("localhost", port))
            if res == errno.ECONNREFUSED:
                return port
    raise RuntimeError("Could not find an open port")


class AutomationTest(AutomationTestBase):
    def setUp(self):
        super().setUp()

        # All of these tests depend on webhook services being set up.
        if BraintrustAppTestBase.skip_webhook():
            raise unittest.SkipTest("")

        # Start webhook server
        self.server_port = get_open_port()
        self.server = http.server.HTTPServer(("localhost", self.server_port), WebhookHandler)
        self.server_url = f"http://{self.server.server_address[0]}:{self.server.server_address[1]}"
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True  # Don't hang if test fails
        self.server_thread.start()
        WebhookHandler.clear()

    def tearDown(self):
        # Stop webhook server
        self.server.shutdown()
        self.server.server_close()
        self.server_thread.join()

    def test_basic(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="basic",
                description="a basic automation",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        # automation should not trigger on logs which don't match the filter
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="goo", output="bar", expected="baz", metadata=dict(x="goo"))
        # automation should not trigger on logs which are missing filter fields
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="goo", output="bar", expected="baz", metadata=dict(this_is_not_x="foo"))
        self._flush()

        self.maxDiff = None
        webhooks = WebhookHandler.webhook_requests
        self.assertEqual(len(webhooks), 1)
        self.assertEqual(set(webhooks[0].keys()), {"organization", "project", "automation", "details"})
        self.assertEqual(
            webhooks[0]["automation"],
            dict(
                id=automation_ids[0],
                name=automations[0]["name"],
                description=automations[0]["description"],
                event_type=automations[0]["config"]["event_type"],
                btql_filter=automations[0]["config"]["btql_filter"],
                interval_seconds=automations[0]["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/automations?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(
            set(webhooks[0]["details"].keys()),
            {"is_test", "message", "time_start", "time_end", "count", "related_logs_url"},
        )
        self.assertEqual(webhooks[0]["details"]["is_test"], False)
        self.assertEqual(
            webhooks[0]["details"]["message"],
            f"{automations[0]['name']}: 1 logs triggered automation in the past 1 second",
        )
        self.assertEqual(webhooks[0]["details"]["count"], 1)

        # its annoying to url encode with the same whitespace as node so decode and compare the raw clauses
        filter_clause = parse_qs(urlparse(webhooks[0]["details"]["related_logs_url"]).query)["search"][0]
        time_start = webhooks[0]["details"]["time_start"]
        time_end = webhooks[0]["details"]["time_end"]
        self.assertEqual(
            json.loads(filter_clause),
            {
                "filter": [
                    {
                        "text": automations[0]["config"]["btql_filter"],
                        "label": automations[0]["config"]["btql_filter"],
                        "originType": "btql",
                    },
                    {
                        "text": f'created >= "{time_start}" AND created < "{time_end}"',
                        "label": f'created >= "{time_start}" AND created < "{time_end}"',
                        "originType": "btql",
                    },
                ]
            },
        )

    def test_project_automation_delete(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="basic",
                description="a basic automation",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        self.assertEqual(len(WebhookHandler.delete_requests), 0)

        res = app_conn().delete("/api/project_automation/delete_id", json={"id": automation_ids[0]})
        res.raise_for_status()

        self.assertEqual(len(WebhookHandler.delete_requests), 1)

    def test_empty(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="empty_filter",
                config=dict(
                    event_type="logs",
                    btql_filter="",
                    interval_seconds=5,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.maxDiff = None
        webhooks = WebhookHandler.webhook_requests
        self.assertEqual(len(webhooks), 1)
        self.assertEqual(set(webhooks[0].keys()), {"organization", "project", "automation", "details"})
        self.assertEqual(
            webhooks[0]["automation"],
            dict(
                id=automation_ids[0],
                name=automations[0]["name"],
                description=automations[0].get("description", ""),
                event_type=automations[0]["config"]["event_type"],
                btql_filter=None,
                interval_seconds=automations[0]["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/automations?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(
            set(webhooks[0]["details"].keys()),
            {"is_test", "message", "time_start", "time_end", "count", "related_logs_url"},
        )
        self.assertEqual(webhooks[0]["details"]["is_test"], False)
        self.assertEqual(
            webhooks[0]["details"]["message"],
            f"{automations[0]['name']}: 1 logs triggered automation in the past 5 seconds",
        )
        self.assertEqual(webhooks[0]["details"]["count"], 1)

        # its annoying to url encode with the same whitespace as node so decode and compare the raw clauses
        filter_clause = parse_qs(urlparse(webhooks[0]["details"]["related_logs_url"]).query)["search"][0]
        time_start = webhooks[0]["details"]["time_start"]
        time_end = webhooks[0]["details"]["time_end"]
        self.assertEqual(
            json.loads(filter_clause),
            {
                "filter": [
                    {
                        "text": f'created >= "{time_start}" AND created < "{time_end}"',
                        "label": f'created >= "{time_start}" AND created < "{time_end}"',
                        "originType": "btql",
                    },
                ]
            },
        )

    def test_interval(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="interval",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), [(automations[0]["name"], 1, False)])

        previous_webhooks = WebhookHandler.webhook_summary()
        # logging again should not trigger automation because of interval
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="goo", output="gar", expected="bar", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), previous_webhooks)

    def test_multi_project_insert(self):
        logger0 = braintrust.init_logger("test0")
        logger1 = braintrust.init_logger("test1")
        logger2 = braintrust.init_logger("test2")
        automations = [
            dict(
                name="multi_project_insert",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        self._set_automations(logger0.project.id, automations)
        self._set_automations(logger1.project.id, automations)

        # Use sync flush mode so that we can write to all three projects in a
        # single request.
        with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
            custom_bg_logger.sync_flush = True
            for l in [logger0, logger1, logger2]:
                l.log(id="row", input="foo", output="bar", expected="bar", metadata=dict(x="foo"))
            custom_bg_logger.flush()
        self._flush()

        # Check that the automation triggered in first 2 projects
        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                (automations[0]["name"], 1, False),
                (automations[0]["name"], 1, False),
            ],
        )

    def test_multiple_automations(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="automation1",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
            dict(
                name="automation2",
                config=dict(
                    event_type="logs",
                    btql_filter="scores.Exactmatch < 0.5",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
            dict(
                name="automation3",
                config=dict(
                    event_type="logs",
                    btql_filter="input = '12'",
                    interval_seconds=500,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        ]
        self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", scores=dict(Exactmatch=0.25))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="12", expected="baz")
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="foo", output="bar", expected="baz")
        self._flush()

        self.assertEqual(
            set(WebhookHandler.webhook_summary()),
            {
                ("automation1", 1, False),
                ("automation2", 1, False),
                ("automation3", 1, False),
            },
        )

    def test_automation_cache(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id

        # Initial automation with filter for x='foo'
        automations = [
            dict(
                name="cache_test",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="test", output="result", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test", 1, False)])

        self._update_automation(
            project_id,
            dict(
                id=automation_ids[0],
                name="cache_test_updated",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'bar'",
                    interval_seconds=60 * 60,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        )
        WebhookHandler.clear()

        # Log with metadata.x = 'bar', should trigger webhook with updated filter
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # Verify webhook was received with updated filter
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated", 1, False)])

        with braintrust.start_span(id="row2") as span2:
            span2.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # not triggered again because of last run cache
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated", 1, False)])

        self._update_automation(
            project_id,
            dict(
                id=automation_ids[0],
                name="cache_test_updated_smaller_interval",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'bar'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            ),
        )
        WebhookHandler.clear()

        time.sleep(1)
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="test", output="result", metadata=dict(x="bar"))
        self._flush()

        # should trigger despite last run cache because the updated interval is smaller
        self.assertEqual(WebhookHandler.webhook_summary(), [("cache_test_updated_smaller_interval", 1, False)])

    def test_automation_action_retry(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id
        automations = [
            dict(
                name="automation_action_retry",
                config=dict(
                    event_type="logs",
                    btql_filter="metadata.x = 'foo'",
                    interval_seconds=1,
                    action=dict(type="webhook", url=self.server_url),
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)
        i = 0

        def response_func():
            nonlocal i
            if i < 2:
                i = i + 1
                return (500, "Internal Server Error")
            return (200, "OK")

        WebhookHandler.set_response(response_func)

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
            ],
        )
        self.assertEqual(
            WebhookHandler.webhook_responses,
            [
                (500, "Internal Server Error"),
                (500, "Internal Server Error"),
                (200, "OK"),
            ],
        )

        WebhookHandler.clear()
        WebhookHandler.set_response(lambda: (404, "Not Found"))

        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        self._flush()

        self.assertEqual(
            WebhookHandler.webhook_summary(),
            [
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
                ("automation_action_retry", 1, False),
            ],
        )
        self.assertEqual(
            WebhookHandler.webhook_responses,
            [
                (404, "Not Found"),
                (404, "Not Found"),
                (404, "Not Found"),
            ],
        )

    def test_test_automation(self):
        logger = braintrust.init_logger("test")
        project_id = logger.project.id

        with braintrust.start_span(id="row0") as span0:
            span0.log(input="foo", output="bar", expected="baz", metadata=dict(x="foo"))
        with braintrust.start_span(id="row1") as span1:
            span1.log(input="foo", output="bar", expected="baz", scores=dict(Exactmatch=0.25))
        with braintrust.start_span(id="row2") as span2:
            span2.log(input="foo", output="bar", expected="baz", metadata=dict(y=12))
        with braintrust.start_span(id="row3") as span3:
            span3.log(input="foo", output="bar", expected="baz")
        self._flush()

        unsaved_automation = dict(
            name="test_automation_unsaved",
            description="",
            config=dict(
                event_type="logs",
                btql_filter="metadata.x = 'foo'",
                interval_seconds=60,
                action=dict(type="webhook", url=self.server_url),
            ),
        )

        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **unsaved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        payload = result["payload"]
        self.assertEqual(payload["organization"]["id"], self.org_id)
        self.assertEqual(payload["project"]["id"], logger.project.id)
        self.assertEqual(
            payload["automation"],
            dict(
                id=None,
                name=unsaved_automation["name"],
                description=unsaved_automation["description"],
                event_type=unsaved_automation["config"]["event_type"],
                btql_filter=unsaved_automation["config"]["btql_filter"],
                interval_seconds=unsaved_automation["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/automations?aid=new",
            ),
        )
        self.assertEqual(payload["details"]["is_test"], True)
        self.assertEqual(
            payload["details"]["message"],
            f"{unsaved_automation['name']}: 1 logs triggered automation in the past 1 minute",
        )
        self.assertEqual(payload["details"]["count"], 1)
        self.assertIsNotNone(payload["details"]["time_start"])
        self.assertIsNotNone(payload["details"]["time_end"])
        self.assertIsNotNone(payload["details"]["related_logs_url"])
        self.assertEqual(WebhookHandler.webhook_summary(), [(unsaved_automation["name"], 1, True)])

        WebhookHandler.clear()
        unsaved_automation["config"]["btql_filter"] = "metadata.x = 'quux'"
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **unsaved_automation),
        ).json()
        self.assertEqual(
            result,
            {
                "kind": "error",
                "message": "No matching rows found for the provided BTQL filter and interval. Adjust the filter or interval or log rows to test again.",
            },
        )
        self.assertEqual(WebhookHandler.webhook_summary(), [])

        saved_automation = dict(**unsaved_automation)
        saved_automation["name"] = "test_automation_saved"
        automation_ids = self._set_automations(project_id, [saved_automation])
        saved_automation["id"] = automation_ids[0]
        saved_automation["config"]["btql_filter"] = "metadata.x = 'foo'"

        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        self.assertEqual(
            result["payload"]["automation"],
            dict(
                id=automation_ids[0],
                name=saved_automation["name"],
                description=saved_automation["description"],
                event_type=saved_automation["config"]["event_type"],
                btql_filter=saved_automation["config"]["btql_filter"],
                interval_seconds=saved_automation["config"]["interval_seconds"],
                url=f"{LOCAL_APP_URL}/app/{self.org_name}/p/{logger.project.name}/configuration/automations?aid={automation_ids[0]}",
            ),
        )
        self.assertEqual(result["payload"]["details"]["is_test"], True)
        self.assertEqual(
            result["payload"]["details"]["message"],
            f"{saved_automation['name']}: 1 logs triggered automation in the past 1 minute",
        )
        self.assertEqual(result["payload"]["details"]["count"], 1)
        self.assertIsNotNone(result["payload"]["details"]["time_start"])
        self.assertIsNotNone(result["payload"]["details"]["time_end"])
        self.assertEqual(WebhookHandler.webhook_summary(), [(saved_automation["name"], 1, True)])

        WebhookHandler.clear()
        saved_automation["config"]["btql_filter"] = "metadata.x = 'quux'"
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(
            result,
            {
                "kind": "error",
                "message": "No matching rows found for the provided BTQL filter and interval. Adjust the filter or interval or log rows to test again.",
            },
        )
        self.assertEqual(WebhookHandler.webhook_summary(), [])

        saved_automation["config"]["btql_filter"] = ""
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/test-automation",
            json=dict(project_id=project_id, **saved_automation),
        ).json()
        self.assertEqual(result["kind"], "success")
        self.assertEqual(WebhookHandler.webhook_summary(), [(saved_automation["name"], 4, True)])


class RetentionTest(AutomationTestBase):
    def get_retention_policies(self, objects):
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/retention/fetch-policies",
            json=dict(objects=objects),
        ).json()

    def get_xact_id_from_datetime(self, dt: datetime):
        # adapted from api-ts/src/xact_id.ts to generate time-traveling xact_ids
        # ignore the global id since there won't be collisions in the past
        ts = int(dt.timestamp())  # timestamp is already in seconds
        return str((0x0DE1 << 48) | ((ts & 0xFFFFFFFFFFFF) << 16))

    def test_retention_policy_validation(self):
        logger = braintrust.init_logger("logs")
        project_id = logger.project.id
        # Test basic retention policy creation works
        automations = [
            dict(
                name="test_retention",
                description="test retention policy",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=90,
                ),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        # Test duplicate name returns existing automation
        resp = self._create_automation(
            project_id,
            dict(
                name="test_retention",
                description="test retention policy duplicate",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=180,
                ),
            ),
        ).json()
        assert resp["found_existing"] is True
        assert resp["project_automation"]["name"] == "test_retention"
        assert resp["project_automation"]["description"] == "test retention policy"
        assert resp["project_automation"]["config"]["event_type"] == "retention"
        assert resp["project_automation"]["config"]["object_type"] == "project_logs"
        assert resp["project_automation"]["config"]["retention_days"] == 90

        # Test duplicate policy on same object fails
        with self.assertRaises(Exception) as cm:
            self._create_automation(
                project_id,
                dict(
                    name="test_retention_2",
                    description="test retention policy 2",
                    config=dict(
                        event_type="retention",
                        object_type="project_logs",
                        retention_days=365,
                    ),
                ),
            )
        assert "Project automation test_retention already exists with this retention configuration" in str(
            cm.exception
        )

        dataset = self.registerDataset(dict(project_id=project_id))["dataset"]
        self._create_automation(
            project_id,
            dict(
                name="test_retention_dataset_wildcard",
                description="test retention dataset policy wildcard",
                config=dict(
                    event_type="retention",
                    object_type="dataset",
                    retention_days=90,
                ),
            ),
        )

        # test fails if user does not have project update permissions
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        with self.assertRaises(Exception) as cm:
            self._create_automation(
                project_id,
                dict(
                    name="test_retention_dataset_wildcard2",
                    description="test retention dataset policy wildcard",
                    config=dict(event_type="retention", object_type="dataset", retention_days=90),
                ),
                api_key=user_api_key,
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

        with self.assertRaises(Exception) as cm:
            self._update_automation(
                project_id,
                dict(
                    id=automation_ids[0],
                    name="test_retention_dataset_wildcard3",
                    description="test retention dataset policy wildcard",
                    config=dict(event_type="retention", object_type="dataset", retention_days=90),
                ),
                api_key=user_api_key,
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project_automation does not exist", str(cm.exception))

    def test_retention_policy_acl(self):
        logger1 = braintrust.init_logger("logs")
        project_id1 = logger1.project.id

        # nonexistent project id
        with self.assertRaises(Exception) as cm:
            self._set_automations(
                str(uuid4()),
                [
                    dict(
                        name="test_retention_basic",
                        description="test retention policy basic",
                        config=dict(event_type="retention", object_type="project_logs", retention_days=180),
                    )
                ],
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

        # project id outside of org
        org_id2, org_name2 = self.createOrg()
        user2_id, _, user2_api_key = self.createUserInOrg(org_id2)
        braintrust.login(app_url=LOCAL_APP_URL, api_key=user2_api_key, org_name=org_name2, force_login=True)
        logger2 = braintrust.init_logger("logs", org_name=org_name2)
        project_id2 = logger2.project.id

        with self.assertRaises(Exception) as cm:
            self._set_automations(
                project_id2,
                [
                    dict(
                        name="test_retention_basic",
                        description="test retention policy basic",
                        config=dict(event_type="retention", object_type="project_logs", retention_days=180),
                    )
                ],
            )
        self.assertIn("Missing update access to project", str(cm.exception))
        self.assertIn("or the project does not exist", str(cm.exception))

    def test_retention_policy_resolution(self):
        logger = braintrust.init_logger("logs")
        project_id = logger.project.id

        dataset = self.registerDataset(dict(project_id=logger.project.id))["dataset"]
        experiment = self.registerExperiment(dict(project_id=logger.project.id))["experiment"]
        automations = [
            dict(
                name="basic",
                description="a basic retention policy on logs",
                config=dict(
                    event_type="retention",
                    object_type="project_logs",
                    retention_days=180,
                ),
            )
        ]
        automation_ids = self._set_automations(logger.project.id, automations)

        # no policy for other object types
        objects = [
            {"object_type": "experiment", "object_id": experiment["id"]},
            {"object_type": "dataset", "object_id": dataset["id"]},
        ]
        result = self.get_retention_policies(objects)
        assert result == [{**o, "policy": None} for o in objects]

        # Test that policy doesn't resolve for different project_id
        another_project = self.registerProject(dict(org_id=self.org_id, project_name="another_project"))["project"]
        objects = [{"object_type": "project_logs", "object_id": another_project["id"]}]
        result = self.get_retention_policies(objects)
        assert result == [{**o, "policy": None} for o in objects]

        # Test that policy resolves for matching project
        objects = [{"object_type": "project_logs", "object_id": project_id}]
        result = self.get_retention_policies(objects)
        assert result == [
            {
                **o,
                "policy": {"automation_id": automation_ids[0], "retention_days": 180},
            }
            for o in objects
        ]

        # delete policy and verify results are empty again
        self._set_automations(project_id, [])
        objects = [{"object_type": "project_logs", "object_id": project_id}]
        result = self.get_retention_policies(objects)
        assert result == [
            {
                **o,
                "policy": None,
            }
            for o in objects
        ]

    def test_retention_basic(self):
        logger = braintrust.init_logger("logs")
        project_id = logger.project.id

        automations = [
            dict(
                name="test_retention_basic",
                description="test retention policy basic",
                config=dict(event_type="retention", object_type="project_logs", retention_days=7),
            )
        ]
        automation_ids = self._set_automations(project_id, automations)

        object_id_dict = {"log_id": "g"}
        log3_raw(dict(project_id=project_id, id="row0", input="foo", **object_id_dict))
        past_xact_id = self.get_xact_id_from_datetime(datetime.now() - timedelta(days=14))
        log3_raw(
            dict(
                project_id=project_id,
                id="row1",
                input="bar",
                _bt_internal_override_xact_id=past_xact_id,
                **object_id_dict,
            )
        )
        self._flush()

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=f"select: id, _xact_id, created, input | from: project_logs('{project_id}') spans",
            ),
        ).json()

        # TODO: call purge_up_to_xact_id(past_xact_id)
        assert resp["data"][0]["_xact_id"] == past_xact_id
