[{"context": {}, "expected": "2", "id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "input": "1+1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "input": "1+1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "input": "1+1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1+1", "root_span_id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "6e461890bfedc1cafae7eb06a87d561f8e2fdbefa45694e877d085810f6515ed", "input": "1+1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1+1", "root_span_id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "6e461890bfedc1cafae7eb06a87d561f8e2fdbefa45694e877d085810f6515ed", "span_parents": ["7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488"], "tags": null}, {"context": {}, "expected": null, "id": "d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e", "input": "1+1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e", "span_parents": ["01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50"], "tags": null}, {"context": {}, "expected": null, "id": "d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e", "input": "1+1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e", "span_parents": ["01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50"], "tags": null}, {"context": {}, "expected": null, "id": "d172bd53d360d033ec25bec6729177d4a7c65f2b842a03ba0f7ef37bf9e77c11", "input": "my input", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "my output", "root_span_id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "scores": null, "span_attributes": {"name": "foo2", "type": "function"}, "span_id": "d172bd53d360d033ec25bec6729177d4a7c65f2b842a03ba0f7ef37bf9e77c11", "span_parents": ["6e461890bfedc1cafae7eb06a87d561f8e2fdbefa45694e877d085810f6515ed"], "tags": null}, {"context": {}, "expected": null, "id": "1fc486d31c54f6880aa68e3c8f2a90aa62fbd36d9fc00e5a06c6bb799958c1e2", "input": ["1+1", {"expected": "2", "metadata": {}, "parameters": {}, "span": "<span>", "trialIndex": 0}], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "foo", "type": "function"}, "span_id": "1fc486d31c54f6880aa68e3c8f2a90aa62fbd36d9fc00e5a06c6bb799958c1e2", "span_parents": ["d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e"], "tags": null}, {"context": {}, "expected": null, "id": "e51573aef34eff4d79bd599e36d020bdf87b914246b8a9c874e12aecec312f4d", "input": ["1+1"], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1+1 equals 2.", "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": null, "span_attributes": {"name": "foo", "type": "function"}, "span_id": "e51573aef34eff4d79bd599e36d020bdf87b914246b8a9c874e12aecec312f4d", "span_parents": ["d473ce4b7b43fbbd91c2e9f18a0cc3335e4a2c5709d1bb7f7a3b71d519a3f06e"], "tags": null}, {"context": {}, "expected": null, "id": "64625189c5009e185e1ea5caff87960bc69213b27eaeb8e1a72d51d638b413cd", "input": {"expected": "2", "input": "1+1", "metadata": {}, "output": "1+1 equals 2."}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.076923}, "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": {"Levenshtein": 0.076923}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "64625189c5009e185e1ea5caff87960bc69213b27eaeb8e1a72d51d638b413cd", "span_parents": ["01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50"], "tags": null}, {"context": {}, "expected": null, "id": "64625189c5009e185e1ea5caff87960bc69213b27eaeb8e1a72d51d638b413cd", "input": {"expected": "2", "input": "1+1", "metadata": {}, "output": "1+1 equals 2."}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.076923}, "root_span_id": "01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50", "scores": {"Levenshtein": 0.076923}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "64625189c5009e185e1ea5caff87960bc69213b27eaeb8e1a72d51d638b413cd", "span_parents": ["01d6833afc9a4afbd37ff62f43533eab3ee487371baa3f139866d95c09ca0b50"], "tags": null}, {"context": {}, "expected": null, "id": "bce3c2cf1cefbbacacb0b90fb984234eb78b15734903599427c7742d3e7d743b", "input": {"expected": "2", "input": "1+1", "metadata": {}, "output": "1+1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "bce3c2cf1cefbbacacb0b90fb984234eb78b15734903599427c7742d3e7d743b", "span_parents": ["7971f91b8e47a5d4631b646112a7b7d5d999af857e8bd9313abb549d0ec95488"], "tags": null}]