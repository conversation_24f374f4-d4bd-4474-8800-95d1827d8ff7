

# ElastiCache Serverless Redis instance for Atlantis locking
resource "aws_elasticache_subnet_group" "atlantis_redis" {
  name       = "${local.environment_short}-redis-subnet-group"
  subnet_ids = data.terraform_remote_state.core.outputs.vpc_config.private_subnets
}

resource "aws_security_group" "atlantis_redis" {
  name   = "${local.environment_short}-atlantis-redis-sg"
  vpc_id = data.terraform_remote_state.core.outputs.vpc_config.vpc_id

  ingress {
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [module.atlantis_service.service_security_group_id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${local.environment_short}-atlantis-redis-sg"
  }
}

resource "aws_elasticache_user_group" "atlantis_redis" {
  engine        = "REDIS"
  user_group_id = "${local.environment_short}-atlantis-redis-users"

  user_ids = [aws_elasticache_user.atlantis_redis_default.user_id]
}

resource "aws_elasticache_user" "atlantis_redis_default" {
  user_id       = "${local.environment_short}-atlantis-redis-default-user"
  user_name     = "default"
  access_string = "on ~* +@all"
  engine        = "REDIS"

  authentication_mode {
    type      = "password"
    passwords = [data.aws_secretsmanager_secret_version.atlantis_redis_password.secret_string]
  }
}

resource "aws_elasticache_serverless_cache" "atlantis_redis" {
  name = "${local.environment_short}-atlantis-redis"

  engine = "redis"

  subnet_ids         = data.terraform_remote_state.core.outputs.vpc_config.private_subnets
  security_group_ids = [aws_security_group.atlantis_redis.id]

  user_group_id = aws_elasticache_user_group.atlantis_redis.user_group_id

  tags = {
    Name = "${local.environment_short}-atlantis-redis"
  }
}

# Output the Redis endpoint for Atlantis configuration
output "atlantis_redis_endpoint" {
  value = aws_elasticache_serverless_cache.atlantis_redis.endpoint[0].address
}

output "atlantis_redis_port" {
  value = aws_elasticache_serverless_cache.atlantis_redis.endpoint[0].port
}
