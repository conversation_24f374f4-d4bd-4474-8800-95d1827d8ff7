module "pulse_service" {
  source = "../../../modules/ecs-service"

  service_name     = "pulse"
  environment      = local.environment
  ecs_cluster_name = data.terraform_remote_state.core.outputs.cluster_name
  vpc_config       = data.terraform_remote_state.core.outputs.vpc_config

  container_image      = "public.ecr.aws/braintrust/pulse:${var.pulse_version}"
  force_new_deployment = var.force_new_deployment

  container_port      = 4319
  healthcheck_command = "curl -f http://localhost:4319/ || exit 1"

  cpu    = 512
  memory = 1024

  is_public_service = true
  authorized_cidr_blocks = {
    internet = "0.0.0.0/0"
  }

  desired_count         = 2
  min_count             = 2
  max_count             = 10
  cpu_scaling           = true
  cpu_scaling_target    = 75
  memory_scaling        = true
  memory_scaling_target = 75

  sidecar_containers = [
    {
      name      = "otel-collector"
      image     = "public.ecr.aws/braintrust/otelcol:${var.otel_collector_version}"
      essential = true
      secrets = [
        {
          name      = "DD_API_KEY"
          valueFrom = data.aws_secretsmanager_secret.dd_api_key.arn
        }
      ]
    }
  ]
}

locals {
  environment = "production"
}

# Change terraform.tfvars to update these
variable "pulse_version" {
  description = "Docker image version for the pulse service"
  type        = string
}

variable "otel_collector_version" {
  description = "Docker image version for the OpenTelemetry collector sidecar"
  type        = string
}

variable "force_new_deployment" {
  description = "Force a new deployment of the service. Useful when using static version like 'latest'."
  type        = bool
  default     = false
}

data "aws_secretsmanager_secret" "dd_api_key" {
  name = "${local.environment}/datadog-api-key"
}

data "terraform_remote_state" "core" {
  backend = "s3"
  config = {
    region       = "us-east-1"
    bucket       = "braintrust-terraform-state"
    use_lockfile = true
    key          = "prod/core/terraform.tfstate"
  }
}
